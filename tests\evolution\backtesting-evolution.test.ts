// tests/evolution/backtesting-evolution.test.ts
// Komplexné backtesting testy pre evolúciu trading agentov

import { BacktestingEvolution } from './framework/BacktestingEvolution';
import { StrategyGenome } from './framework/StrategyGenome';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { AgentEvolutionFramework } from './framework/AgentEvolutionFramework';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Backtesting Evolution Framework', () => {
    let backtestingEvolution: BacktestingEvolution;
    let fitnessEvaluator: FitnessEvaluator;
    const initialBalance = 10000;

    beforeEach(() => {
        backtestingEvolution = new BacktestingEvolution({
            walkForwardPeriods: 5,
            trainTestSplit: 0.7,
            validationSplit: 0.15,
            rebalancePeriod: 30,
            minTrainingSamples: 100
        });
        
        fitnessEvaluator = new FitnessEvaluator();
    });

    describe('Walk-Forward Analysis', () => {
        it('should perform walk-forward optimization', async () => {
            const fullDataset = testScenarios.optimalConditions.slice(0, 500);
            
            const walkForwardResult = await backtestingEvolution.walkForwardOptimization(
                fullDataset,
                initialBalance,
                fitnessEvaluator,
                {
                    windowSize: 100,
                    stepSize: 20,
                    optimizationPeriods: 5
                }
            );

            expect(walkForwardResult.periods).toHaveLength(5);
            expect(walkForwardResult.overallPerformance.overallScore).toBeGreaterThan(0);
            expect(walkForwardResult.stabilityMetrics.consistency).toBeGreaterThanOrEqual(0);

            // Každé obdobie by malo mať trénovacie a testovacie výsledky
            walkForwardResult.periods.forEach((period, index) => {
                expect(period.trainPerformance.overallScore).toBeGreaterThanOrEqual(0);
                expect(period.testPerformance.overallScore).toBeGreaterThanOrEqual(0);
                expect(period.optimizedParameters).toBeDefined();
                
                console.log(`📊 Period ${index + 1}:`);
                console.log(`  Train Score: ${period.trainPerformance.overallScore.toFixed(2)}`);
                console.log(`  Test Score: ${period.testPerformance.overallScore.toFixed(2)}`);
                console.log(`  Generalization Gap: ${(period.trainPerformance.overallScore - period.testPerformance.overallScore).toFixed(2)}`);
            });

            console.log(`🎯 Walk-Forward Summary:`);
            console.log(`  Overall Score: ${walkForwardResult.overallPerformance.overallScore.toFixed(2)}`);
            console.log(`  Stability: ${walkForwardResult.stabilityMetrics.consistency.toFixed(3)}`);
        });

        it('should detect overfitting through walk-forward analysis', async () => {
            const dataset = testScenarios.volatileMarket.slice(0, 400);
            
            const overfittingAnalysis = await backtestingEvolution.detectOverfitting(
                dataset,
                initialBalance,
                fitnessEvaluator,
                {
                    complexityLevels: [
                        { populationSize: 10, generations: 3 },
                        { populationSize: 20, generations: 5 },
                        { populationSize: 30, generations: 8 }
                    ]
                }
            );

            expect(overfittingAnalysis.complexityResults).toHaveLength(3);
            expect(overfittingAnalysis.optimalComplexity).toBeDefined();
            expect(overfittingAnalysis.overfittingScore).toBeGreaterThanOrEqual(0);

            // Vyššia komplexnosť by mala viesť k lepším trénovacím, ale možno horším testovacím výsledkom
            const results = overfittingAnalysis.complexityResults;
            
            console.log(`🔍 Overfitting Analysis:`);
            results.forEach((result, index) => {
                console.log(`  Complexity ${index + 1}:`);
                console.log(`    Train: ${result.trainScore.toFixed(2)}, Test: ${result.testScore.toFixed(2)}`);
                console.log(`    Gap: ${(result.trainScore - result.testScore).toFixed(2)}`);
            });
            
            console.log(`  Optimal Complexity: Level ${overfittingAnalysis.optimalComplexity}`);
            console.log(`  Overfitting Score: ${overfittingAnalysis.overfittingScore.toFixed(3)}`);
        });
    });

    describe('Cross-Validation Evolution', () => {
        it('should perform k-fold cross-validation', async () => {
            const dataset = testScenarios.trendingMarket.slice(0, 300);
            
            const cvResult = await backtestingEvolution.kFoldCrossValidation(
                dataset,
                initialBalance,
                fitnessEvaluator,
                {
                    k: 5,
                    stratified: true,
                    shuffle: true
                }
            );

            expect(cvResult.foldResults).toHaveLength(5);
            expect(cvResult.meanScore).toBeGreaterThan(0);
            expect(cvResult.stdScore).toBeGreaterThanOrEqual(0);
            expect(cvResult.confidenceInterval).toHaveLength(2);

            // Všetky foldy by mali mať rozumné výsledky
            cvResult.foldResults.forEach((fold, index) => {
                expect(fold.trainScore).toBeGreaterThanOrEqual(0);
                expect(fold.validationScore).toBeGreaterThanOrEqual(0);
                
                console.log(`📁 Fold ${index + 1}: Train ${fold.trainScore.toFixed(2)}, Val ${fold.validationScore.toFixed(2)}`);
            });

            console.log(`📈 Cross-Validation Summary:`);
            console.log(`  Mean Score: ${cvResult.meanScore.toFixed(2)} ± ${cvResult.stdScore.toFixed(2)}`);
            console.log(`  95% CI: [${cvResult.confidenceInterval[0].toFixed(2)}, ${cvResult.confidenceInterval[1].toFixed(2)}]`);
        });

        it('should perform time series cross-validation', async () => {
            const dataset = testScenarios.optimalConditions.slice(0, 400);
            
            const tsCvResult = await backtestingEvolution.timeSeriesCrossValidation(
                dataset,
                initialBalance,
                fitnessEvaluator,
                {
                    initialTrainSize: 100,
                    testSize: 50,
                    gap: 10,
                    maxSplits: 5
                }
            );

            expect(tsCvResult.splits).toHaveLength(5);
            expect(tsCvResult.meanScore).toBeGreaterThan(0);
            expect(tsCvResult.temporalStability).toBeGreaterThanOrEqual(0);

            // Splits by mali rešpektovať časovú štruktúru
            tsCvResult.splits.forEach((split, index) => {
                expect(split.trainEnd).toBeLessThan(split.testStart);
                expect(split.testStart - split.trainEnd).toBeGreaterThanOrEqual(10); // gap
                
                console.log(`⏰ Split ${index + 1}: Train [${split.trainStart}-${split.trainEnd}], Test [${split.testStart}-${split.testEnd}]`);
                console.log(`    Score: ${split.score.toFixed(2)}`);
            });

            console.log(`🕒 Time Series CV Summary:`);
            console.log(`  Mean Score: ${tsCvResult.meanScore.toFixed(2)}`);
            console.log(`  Temporal Stability: ${tsCvResult.temporalStability.toFixed(3)}`);
        });
    });

    describe('Monte Carlo Backtesting', () => {
        it('should perform Monte Carlo simulation', async () => {
            const baseStrategy = new StrategyGenome({
                minConfidence: 80,
                minSignalStrength: 75,
                maxRiskLevel: 'medium'
            });

            const mcResult = await backtestingEvolution.monteCarloBacktest(
                baseStrategy,
                testScenarios.optimalConditions.slice(0, 200),
                initialBalance,
                fitnessEvaluator,
                {
                    simulations: 100,
                    noiseLevel: 0.05,
                    bootstrapSamples: true
                }
            );

            expect(mcResult.simulations).toHaveLength(100);
            expect(mcResult.statistics.mean).toBeGreaterThan(0);
            expect(mcResult.statistics.std).toBeGreaterThanOrEqual(0);
            expect(mcResult.confidenceIntervals['95%']).toHaveLength(2);

            // Skontroluj distribúciu výsledkov
            const scores = mcResult.simulations.map(s => s.score);
            const positiveResults = scores.filter(s => s > 0).length;
            const winRate = positiveResults / scores.length;

            expect(winRate).toBeGreaterThan(0.3); // Aspoň 30% pozitívnych výsledkov

            console.log(`🎲 Monte Carlo Results (${mcResult.simulations.length} simulations):`);
            console.log(`  Mean Score: ${mcResult.statistics.mean.toFixed(2)}`);
            console.log(`  Std Dev: ${mcResult.statistics.std.toFixed(2)}`);
            console.log(`  Win Rate: ${(winRate * 100).toFixed(1)}%`);
            console.log(`  95% CI: [${mcResult.confidenceIntervals['95%'][0].toFixed(2)}, ${mcResult.confidenceIntervals['95%'][1].toFixed(2)}]`);
        });

        it('should test robustness to market noise', async () => {
            const strategy = new StrategyGenome({
                minConfidence: 85,
                minSignalStrength: 80,
                maxRiskLevel: 'low'
            });

            const robustnessTest = await backtestingEvolution.testRobustness(
                strategy,
                testScenarios.volatileMarket.slice(0, 200),
                initialBalance,
                fitnessEvaluator,
                {
                    noiseLevels: [0.01, 0.02, 0.05, 0.1],
                    simulationsPerLevel: 20
                }
            );

            expect(robustnessTest.noiseLevels).toHaveLength(4);
            expect(robustnessTest.robustnessScore).toBeGreaterThanOrEqual(0);

            // Výkonnosť by sa mala zhoršovať s rastúcim šumom
            let previousMean = Infinity;
            robustnessTest.noiseLevels.forEach((level, index) => {
                expect(level.noiseLevel).toBeGreaterThanOrEqual(0);
                expect(level.meanScore).toBeLessThanOrEqual(previousMean * 1.1); // Tolerancia 10%
                previousMean = level.meanScore;
                
                console.log(`🔊 Noise ${(level.noiseLevel * 100).toFixed(1)}%: Mean ${level.meanScore.toFixed(2)}, Std ${level.stdScore.toFixed(2)}`);
            });

            console.log(`🛡️ Robustness Score: ${robustnessTest.robustnessScore.toFixed(3)}`);
        });
    });

    describe('Out-of-Sample Testing', () => {
        it('should perform out-of-sample validation', async () => {
            const trainData = testScenarios.optimalConditions.slice(0, 300);
            const testData = testScenarios.optimalConditions.slice(300, 500);

            const oosResult = await backtestingEvolution.outOfSampleValidation(
                trainData,
                testData,
                initialBalance,
                fitnessEvaluator,
                {
                    evolutionConfig: {
                        populationSize: 15,
                        generations: 5,
                        mutationRate: 0.1,
                        crossoverRate: 0.8,
                        elitismRate: 0.2,
                        tournamentSize: 3
                    }
                }
            );

            expect(oosResult.trainPerformance.overallScore).toBeGreaterThan(0);
            expect(oosResult.testPerformance.overallScore).toBeGreaterThan(0);
            expect(oosResult.generalizationGap).toBeDefined();
            expect(oosResult.optimizedStrategy).toBeDefined();

            // Generalization gap by nemal byť príliš veľký
            const gap = oosResult.generalizationGap;
            expect(Math.abs(gap)).toBeLessThan(oosResult.trainPerformance.overallScore * 0.5); // Max 50% gap

            console.log(`🎯 Out-of-Sample Results:`);
            console.log(`  Train Score: ${oosResult.trainPerformance.overallScore.toFixed(2)}`);
            console.log(`  Test Score: ${oosResult.testPerformance.overallScore.toFixed(2)}`);
            console.log(`  Generalization Gap: ${gap.toFixed(2)}`);
            console.log(`  Gap Percentage: ${((gap / oosResult.trainPerformance.overallScore) * 100).toFixed(1)}%`);
        });

        it('should test on different market regimes', async () => {
            const trainData = testScenarios.optimalConditions.slice(0, 200);
            
            const regimeTests = [
                { name: 'Volatile Market', data: testScenarios.volatileMarket.slice(0, 150) },
                { name: 'Trending Market', data: testScenarios.trendingMarket.slice(0, 150) },
                { name: 'Sideways Market', data: testScenarios.sidewaysMarket.slice(0, 150) }
            ];

            const regimeResults = [];

            for (const regime of regimeTests) {
                const result = await backtestingEvolution.outOfSampleValidation(
                    trainData,
                    regime.data,
                    initialBalance,
                    fitnessEvaluator
                );

                regimeResults.push({
                    regime: regime.name,
                    performance: result.testPerformance,
                    gap: result.generalizationGap
                });

                console.log(`📊 ${regime.name}:`);
                console.log(`  Test Score: ${result.testPerformance.overallScore.toFixed(2)}`);
                console.log(`  Gap: ${result.generalizationGap.toFixed(2)}`);
            }

            // Stratégia by mala fungovať aspoň v niektorých režimoch
            const workingRegimes = regimeResults.filter(r => r.performance.overallScore > 0);
            expect(workingRegimes.length).toBeGreaterThan(0);

            console.log(`✅ Working in ${workingRegimes.length}/${regimeResults.length} market regimes`);
        });
    });

    describe('Performance Attribution', () => {
        it('should analyze performance attribution', async () => {
            const strategy = new StrategyGenome({
                minConfidence: 80,
                minSignalStrength: 75,
                maxRiskLevel: 'medium',
                volumeConfirmation: true,
                multiTimeframeConfirmation: true
            });

            const attributionResult = await backtestingEvolution.performanceAttribution(
                strategy,
                testScenarios.optimalConditions.slice(0, 300),
                initialBalance,
                fitnessEvaluator
            );

            expect(attributionResult.totalReturn).toBeDefined();
            expect(attributionResult.factorContributions).toBeDefined();
            expect(attributionResult.parameterSensitivity).toBeDefined();

            // Skontroluj, že faktory sa sčítajú na celkový výnos
            const sumOfFactors = Object.values(attributionResult.factorContributions)
                .reduce((sum: number, contrib: any) => sum + contrib, 0);
            
            expect(Math.abs(sumOfFactors - attributionResult.totalReturn)).toBeLessThan(0.1);

            console.log(`📈 Performance Attribution:`);
            console.log(`  Total Return: ${attributionResult.totalReturn.toFixed(2)}`);
            console.log(`  Factor Contributions:`);
            Object.entries(attributionResult.factorContributions).forEach(([factor, contrib]) => {
                console.log(`    ${factor}: ${(contrib as number).toFixed(2)}`);
            });
        });

        it('should perform sensitivity analysis', async () => {
            const baseStrategy = new StrategyGenome({
                minConfidence: 80,
                minSignalStrength: 75,
                maxRiskLevel: 'medium'
            });

            const sensitivityResult = await backtestingEvolution.sensitivityAnalysis(
                baseStrategy,
                testScenarios.optimalConditions.slice(0, 200),
                initialBalance,
                fitnessEvaluator,
                {
                    parameters: ['minConfidence', 'minSignalStrength'],
                    perturbationRange: 0.1 // ±10%
                }
            );

            expect(sensitivityResult.parameterSensitivities).toBeDefined();
            expect(sensitivityResult.mostSensitiveParameter).toBeDefined();
            expect(sensitivityResult.stabilityScore).toBeGreaterThanOrEqual(0);

            console.log(`🔍 Sensitivity Analysis:`);
            Object.entries(sensitivityResult.parameterSensitivities).forEach(([param, sensitivity]) => {
                console.log(`  ${param}: ${(sensitivity as number).toFixed(3)}`);
            });
            console.log(`  Most Sensitive: ${sensitivityResult.mostSensitiveParameter}`);
            console.log(`  Stability Score: ${sensitivityResult.stabilityScore.toFixed(3)}`);
        });
    });

    describe('Stress Testing', () => {
        it('should perform stress testing under extreme conditions', async () => {
            const strategy = new StrategyGenome({
                minConfidence: 85,
                minSignalStrength: 80,
                maxRiskLevel: 'low',
                stopLoss: 0.5
            });

            const stressTestResult = await backtestingEvolution.stressTesting(
                strategy,
                testScenarios.volatileMarket.slice(0, 200),
                initialBalance,
                fitnessEvaluator,
                {
                    stressScenarios: [
                        { name: 'Market Crash', volatilityMultiplier: 3, trendShift: -0.5 },
                        { name: 'Flash Crash', volatilityMultiplier: 5, duration: 10 },
                        { name: 'Low Liquidity', volumeReduction: 0.8, spreadIncrease: 2 }
                    ]
                }
            );

            expect(stressTestResult.scenarios).toHaveLength(3);
            expect(stressTestResult.overallStressScore).toBeGreaterThanOrEqual(0);

            stressTestResult.scenarios.forEach(scenario => {
                expect(scenario.performance.overallScore).toBeGreaterThanOrEqual(-1000); // Rozumný dolný limit
                console.log(`💥 ${scenario.name}: Score ${scenario.performance.overallScore.toFixed(2)}`);
            });

            console.log(`🛡️ Overall Stress Score: ${stressTestResult.overallStressScore.toFixed(3)}`);
        });
    });
});
