// tests/evolution/framework/ParameterOptimizer.ts
// Základný framework pre optimalizáciu parametrov

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface OptimizationConfig {
    method: 'grid_search' | 'bayesian' | 'genetic' | 'nsga2' | 'weighted_sum' | 'adaptive';
    maxIterations?: number;
    populationSize?: number;
    generations?: number;
    objectives?: string[] | { [key: string]: number };
    adaptationPeriod?: number;
    learningRate?: number;
}

export interface ParameterBounds {
    [key: string]: { min: number; max: number } | any[];
}

export interface OptimizationResult {
    bestParameters: any;
    bestFitness: FitnessResult;
    allResults?: any[];
    paretoFront?: { parameters: any; fitness: FitnessResult }[];
    bestCompromise?: { parameters: any; fitness: FitnessResult };
    convergenceHistory?: number[];
    executionTime?: number;
}

export interface AdaptiveResult {
    adaptedParameters: any;
    performance: FitnessResult;
    adaptationHistory: any[];
}

export class ParameterOptimizer {
    private config: OptimizationConfig;

    constructor(config: OptimizationConfig) {
        this.config = config;
    }

    /**
     * Hlavná optimalizačná metóda
     */
    async optimize(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        const startTime = Date.now();

        let result: OptimizationResult;

        switch (this.config.method) {
            case 'grid_search':
                result = await this.gridSearchOptimization(parameterBounds, marketData, initialBalance, fitnessEvaluator);
                break;
            case 'bayesian':
                result = await this.bayesianOptimization(parameterBounds, marketData, initialBalance, fitnessEvaluator);
                break;
            case 'genetic':
                result = await this.geneticOptimization(parameterBounds, marketData, initialBalance, fitnessEvaluator);
                break;
            case 'nsga2':
                result = await this.nsga2Optimization(parameterBounds, marketData, initialBalance, fitnessEvaluator);
                break;
            case 'weighted_sum':
                result = await this.weightedSumOptimization(parameterBounds, marketData, initialBalance, fitnessEvaluator);
                break;
            default:
                throw new Error(`Unsupported optimization method: ${this.config.method}`);
        }

        result.executionTime = Date.now() - startTime;
        return result;
    }

    /**
     * Grid search optimalizácia
     */
    private async gridSearchOptimization(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        const combinations = this.generateParameterCombinations(parameterBounds);
        const results = [];
        let bestResult = null;

        for (const params of combinations) {
            const genome = new StrategyGenome(params);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            
            const result = { parameters: params, fitness };
            results.push(result);

            if (!bestResult || fitness.overallScore > bestResult.fitness.overallScore) {
                bestResult = result;
            }
        }

        return {
            bestParameters: bestResult!.parameters,
            bestFitness: bestResult!.fitness,
            allResults: results
        };
    }

    /**
     * Bayesian optimalizácia (zjednodušená implementácia)
     */
    private async bayesianOptimization(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        const maxIterations = this.config.maxIterations || 20;
        const convergenceHistory = [];
        let bestResult = null;

        // Počiatočné náhodné vzorky
        const initialSamples = 5;
        const evaluatedPoints = [];

        for (let i = 0; i < initialSamples; i++) {
            const params = this.sampleRandomParameters(parameterBounds);
            const genome = new StrategyGenome(params);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            
            evaluatedPoints.push({ parameters: params, fitness });
            convergenceHistory.push(fitness.overallScore);

            if (!bestResult || fitness.overallScore > bestResult.fitness.overallScore) {
                bestResult = { parameters: params, fitness };
            }
        }

        // Iteratívne zlepšovanie
        for (let iteration = initialSamples; iteration < maxIterations; iteration++) {
            // Zjednodušená acquisition function - vyber parametre blízko k najlepším
            const params = this.acquireNextParameters(evaluatedPoints, parameterBounds);
            const genome = new StrategyGenome(params);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            
            evaluatedPoints.push({ parameters: params, fitness });
            convergenceHistory.push(fitness.overallScore);

            if (fitness.overallScore > bestResult!.fitness.overallScore) {
                bestResult = { parameters: params, fitness };
            }
        }

        return {
            bestParameters: bestResult!.parameters,
            bestFitness: bestResult!.fitness,
            convergenceHistory
        };
    }

    /**
     * NSGA-II multi-objective optimalizácia
     */
    private async nsga2Optimization(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        const populationSize = this.config.populationSize || 20;
        const generations = this.config.generations || 10;
        
        // Vytvor počiatočnú populáciu
        const population = [];
        for (let i = 0; i < populationSize; i++) {
            const params = this.sampleRandomParameters(parameterBounds);
            const genome = new StrategyGenome(params);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            population.push({ parameters: params, fitness });
        }

        // Simuluj NSGA-II evolúciu
        let currentPopulation = population;
        
        for (let gen = 0; gen < generations; gen++) {
            // Zjednodušená implementácia - vyber najlepších podľa rôznych kritérií
            const nextGeneration = [];
            
            // Elitizmus pre rôzne objektívy
            const sortedByWinRate = [...currentPopulation].sort((a, b) => b.fitness.winRate - a.fitness.winRate);
            const sortedByProfitFactor = [...currentPopulation].sort((a, b) => b.fitness.profitFactor - a.fitness.profitFactor);
            const sortedByDrawdown = [...currentPopulation].sort((a, b) => a.fitness.maxDrawdown - b.fitness.maxDrawdown);
            
            // Pridaj najlepších z každej kategórie
            nextGeneration.push(sortedByWinRate[0]);
            nextGeneration.push(sortedByProfitFactor[0]);
            nextGeneration.push(sortedByDrawdown[0]);
            
            // Doplň zvyšok populácie mutáciou najlepších
            while (nextGeneration.length < populationSize) {
                const parent = currentPopulation[Math.floor(Math.random() * Math.min(5, currentPopulation.length))];
                const mutatedParams = this.mutateParameters(parent.parameters, parameterBounds);
                const genome = new StrategyGenome(mutatedParams);
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                nextGeneration.push({ parameters: mutatedParams, fitness });
            }
            
            currentPopulation = nextGeneration;
        }

        // Vytvor Pareto front
        const paretoFront = this.extractParetoFront(currentPopulation);
        const bestCompromise = this.findBestCompromise(paretoFront);

        return {
            bestParameters: bestCompromise.parameters,
            bestFitness: bestCompromise.fitness,
            paretoFront,
            bestCompromise
        };
    }

    /**
     * Weighted sum optimalizácia
     */
    private async weightedSumOptimization(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        const objectives = this.config.objectives as { [key: string]: number };
        const maxIterations = this.config.maxIterations || 50;
        
        let bestResult = null;
        
        for (let i = 0; i < maxIterations; i++) {
            const params = this.sampleRandomParameters(parameterBounds);
            const genome = new StrategyGenome(params);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            
            // Vypočítaj weighted sum
            const weightedScore = this.calculateWeightedScore(fitness, objectives);
            
            if (!bestResult || weightedScore > bestResult.weightedScore) {
                bestResult = {
                    parameters: params,
                    fitness,
                    weightedScore
                };
            }
        }

        return {
            bestParameters: bestResult!.parameters,
            bestFitness: bestResult!.fitness
        };
    }

    /**
     * Genetická optimalizácia
     */
    private async geneticOptimization(
        parameterBounds: ParameterBounds,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<OptimizationResult> {
        // Zjednodušená implementácia - použije existujúci AgentEvolutionFramework
        const populationSize = this.config.populationSize || 20;
        const generations = this.config.generations || 10;
        
        // Vytvor počiatočnú populáciu v rámci bounds
        const population = [];
        for (let i = 0; i < populationSize; i++) {
            const params = this.sampleRandomParameters(parameterBounds);
            const genome = new StrategyGenome(params);
            population.push(genome);
        }

        // Simuluj evolúciu
        let bestGenome = null;
        let bestFitness = null;

        for (const genome of population) {
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            if (!bestFitness || fitness.overallScore > bestFitness.overallScore) {
                bestGenome = genome;
                bestFitness = fitness;
            }
        }

        return {
            bestParameters: bestGenome!.getParameters(),
            bestFitness: bestFitness!
        };
    }

    /**
     * Adaptívna optimalizácia pre rôzne trhové podmienky
     */
    async adaptToMarket(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<AdaptiveResult> {
        const adaptationPeriod = this.config.adaptationPeriod || 50;
        const learningRate = this.config.learningRate || 0.1;
        
        // Začni s defaultnými parametrami
        let currentParams = {
            minConfidence: 75,
            minSignalStrength: 70,
            maxRiskLevel: 'medium' as const,
            trendConfirmationPeriod: 5
        };

        const adaptationHistory = [];
        
        // Adaptuj parametre na základe výkonnosti
        for (let i = 0; i < marketData.length; i += adaptationPeriod) {
            const segment = marketData.slice(i, i + adaptationPeriod);
            if (segment.length < 10) break;
            
            const genome = new StrategyGenome(currentParams);
            const fitness = await fitnessEvaluator.evaluateFitness(genome, segment, initialBalance);
            
            adaptationHistory.push({
                period: i / adaptationPeriod,
                parameters: { ...currentParams },
                performance: fitness.overallScore
            });
            
            // Adaptuj parametre na základe výkonnosti
            if (fitness.winRate < 0.5) {
                currentParams.minConfidence = Math.min(95, currentParams.minConfidence + 5);
            } else if (fitness.winRate > 0.8) {
                currentParams.minConfidence = Math.max(60, currentParams.minConfidence - 2);
            }
            
            if (fitness.maxDrawdown > 0.2) {
                currentParams.maxRiskLevel = 'low';
            } else if (fitness.maxDrawdown < 0.05) {
                currentParams.maxRiskLevel = 'medium';
            }
        }

        // Finálne vyhodnotenie
        const finalGenome = new StrategyGenome(currentParams);
        const finalPerformance = await fitnessEvaluator.evaluateFitness(finalGenome, marketData, initialBalance);

        return {
            adaptedParameters: currentParams,
            performance: finalPerformance,
            adaptationHistory
        };
    }

    // Pomocné metódy
    private generateParameterCombinations(bounds: ParameterBounds): any[] {
        const keys = Object.keys(bounds);
        const combinations: any[] = [];
        
        const generateCombos = (index: number, current: any) => {
            if (index === keys.length) {
                combinations.push({ ...current });
                return;
            }
            
            const key = keys[index];
            const values = Array.isArray(bounds[key]) ? bounds[key] as any[] : [bounds[key]];
            
            for (const value of values) {
                current[key] = value;
                generateCombos(index + 1, current);
            }
        };
        
        generateCombos(0, {});
        return combinations;
    }

    private sampleRandomParameters(bounds: ParameterBounds): any {
        const params: any = {};
        
        for (const [key, bound] of Object.entries(bounds)) {
            if (Array.isArray(bound)) {
                params[key] = bound[Math.floor(Math.random() * bound.length)];
            } else if (typeof bound === 'object' && 'min' in bound && 'max' in bound) {
                params[key] = Math.random() * (bound.max - bound.min) + bound.min;
            }
        }
        
        return params;
    }

    private acquireNextParameters(evaluatedPoints: any[], bounds: ParameterBounds): any {
        // Zjednodušená acquisition function - vyber parametre blízko k najlepším
        const best = evaluatedPoints.reduce((best, current) => 
            current.fitness.overallScore > best.fitness.overallScore ? current : best
        );
        
        const params = { ...best.parameters };
        
        // Pridaj malý šum
        for (const [key, bound] of Object.entries(bounds)) {
            if (typeof bound === 'object' && 'min' in bound && 'max' in bound) {
                const noise = (Math.random() - 0.5) * (bound.max - bound.min) * 0.1;
                params[key] = Math.max(bound.min, Math.min(bound.max, params[key] + noise));
            }
        }
        
        return params;
    }

    private mutateParameters(params: any, bounds: ParameterBounds): any {
        const mutated = { ...params };
        
        for (const [key, bound] of Object.entries(bounds)) {
            if (Math.random() < 0.3) { // 30% šanca na mutáciu
                if (Array.isArray(bound)) {
                    mutated[key] = bound[Math.floor(Math.random() * bound.length)];
                } else if (typeof bound === 'object' && 'min' in bound && 'max' in bound) {
                    const noise = (Math.random() - 0.5) * (bound.max - bound.min) * 0.2;
                    mutated[key] = Math.max(bound.min, Math.min(bound.max, params[key] + noise));
                }
            }
        }
        
        return mutated;
    }

    private extractParetoFront(population: any[]): any[] {
        // Zjednodušená implementácia Pareto frontu
        const paretoFront = [];
        
        for (const individual of population) {
            let isDominated = false;
            
            for (const other of population) {
                if (this.dominates(other.fitness, individual.fitness)) {
                    isDominated = true;
                    break;
                }
            }
            
            if (!isDominated) {
                paretoFront.push(individual);
            }
        }
        
        return paretoFront;
    }

    private dominates(fitness1: FitnessResult, fitness2: FitnessResult): boolean {
        return fitness1.winRate >= fitness2.winRate &&
               fitness1.profitFactor >= fitness2.profitFactor &&
               fitness1.maxDrawdown <= fitness2.maxDrawdown &&
               (fitness1.winRate > fitness2.winRate ||
                fitness1.profitFactor > fitness2.profitFactor ||
                fitness1.maxDrawdown < fitness2.maxDrawdown);
    }

    private findBestCompromise(paretoFront: any[]): any {
        // Nájdi najlepší kompromis (najvyšší celkový score)
        return paretoFront.reduce((best, current) => 
            current.fitness.overallScore > best.fitness.overallScore ? current : best
        );
    }

    private calculateWeightedScore(fitness: FitnessResult, objectives: { [key: string]: number }): number {
        let score = 0;
        
        for (const [objective, weight] of Object.entries(objectives)) {
            const value = (fitness as any)[objective] || 0;
            score += value * weight;
        }
        
        return score;
    }
}
