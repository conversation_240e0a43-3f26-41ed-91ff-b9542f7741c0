import { Trader } from '../../src/agent/trader';

describe('Trader', () => {
    let trader: Trader;

    beforeEach(() => {
        trader = new Trader();
    });

    test('should execute a buy trade', () => {
        const result = trader.buy(100);
        expect(result).toBe('Bought 100 units');
    });

    test('should execute a sell trade', () => {
        const result = trader.sell(50);
        expect(result).toBe('Sold 50 units');
    });

    test('should execute a trade', () => {
        const result = trader.executeTrade('buy', 200);
        expect(result).toBe('Executed buy trade for 200 units');
    });
});