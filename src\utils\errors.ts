// src/utils/errors.ts

/**
 * Základná trieda pre všetky vlastné chyby v aplikácii.
 */
export class AppError extends Error {
    constructor(message: string) {
        super(message);
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * <PERSON>y<PERSON>, ktorá nastane pri problémoch s API.
 */
export class ApiError extends AppError {
    constructor(message = 'Error fetching data from API') {
        super(message);
    }
}

/**
 * Chy<PERSON>, ktorá nastane pri nedostatočnom zostatku na účte.
 */
export class InsufficientBalanceError extends AppError {
    constructor(message = 'Insufficient balance for the transaction') {
        super(message);
    }
}

/**
 * Chyba, ktorá nastane pri neúspešnom vykonaní obchodu.
 */
export class TradeExecutionError extends AppError {
    constructor(message = 'Failed to execute trade') {
        super(message);
    }
}