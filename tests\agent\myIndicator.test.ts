import { myIndicator } from '../../src/agent/myIndicator';
import * as assert from 'assert';

describe('myIndicator', () => {
  it('should calculate the average of the input data', () => {
    const data = [1, 2, 3, 4, 5];
    const expectedAverage = 3;
    const actualAverage = myIndicator(data);
    assert.strictEqual(actualAverage, expectedAverage, 'The average should be 3');
  });

  it('should handle empty array', () => {
    const data: number[] = [];
    const result = myIndicator(data);
    expect(isNaN(result)).toBe(true);
  });

  it('should handle single element', () => {
    const data = [5];
    const result = myIndicator(data);
    expect(result).toBe(5);
  });
});