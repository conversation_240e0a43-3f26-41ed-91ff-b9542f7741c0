// tests/integration/full-system.test.ts

import Trader from '../../src/agent/trader';
import { DataProcessor } from '../../src/data/processor';
import { SmartStrategy } from '../../src/agent/smartStrategy';
import { RiskManager } from '../../src/risk/riskManager';
import { conservativeRiskConfig } from '../../src/risk/riskConfig';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Full System Integration', () => {
    let trader: Trader;
    let dataProcessor: DataProcessor;
    let strategy: SmartStrategy;
    let riskManager: RiskManager;

    beforeEach(() => {
        trader = new Trader(10000);
        dataProcessor = new DataProcessor();
        strategy = new SmartStrategy();
        riskManager = new RiskManager(10000, conservativeRiskConfig);
    });

    it('should integrate all components successfully', () => {
        expect(trader).toBeInstanceOf(Trader);
        expect(dataProcessor).toBeInstanceOf(DataProcessor);
        expect(strategy).toBeInstanceOf(SmartStrategy);
        expect(riskManager).toBeInstanceOf(RiskManager);
    });

    it('should process data and make trading decisions', () => {
        const rawData = testScenarios.optimalConditions.slice(0, 10);
        const processedData = dataProcessor.processData(rawData);

        expect(processedData).toHaveLength(rawData.length);
        expect(processedData[0]).toHaveProperty('processed', true);

        // Test strategy with processed data
        processedData.forEach(dataPoint => {
            const decision = strategy.decideAction(dataPoint);
            if (decision) {
                expect(['buy', 'sell']).toContain(decision.action);
                expect(decision.amount).toBeGreaterThan(0);
            }
        });
    });

    it('should handle risk management correctly', () => {
        const entryPrice = 1.2000;
        const stopLoss = riskManager.calculateStopLoss(entryPrice, 'buy');
        const takeProfit = riskManager.calculateTakeProfit(entryPrice, 'buy');

        expect(stopLoss).toBeLessThan(entryPrice);
        expect(takeProfit).toBeGreaterThan(entryPrice);

        const isValid = riskManager.validateTrade(entryPrice, stopLoss, takeProfit, 'buy');
        expect(typeof isValid).toBe('boolean');
    });

    it('should maintain system stability under load', () => {
        const largeDataSet = testScenarios.mixedConditions;
        let processedCount = 0;
        let decisionsCount = 0;

        largeDataSet.forEach(dataPoint => {
            try {
                const processed = dataProcessor.processData([dataPoint]);
                processedCount++;

                const decision = strategy.decideAction(dataPoint);
                if (decision) {
                    decisionsCount++;
                    
                    // Validate decision
                    expect(['buy', 'sell']).toContain(decision.action);
                    expect(decision.amount).toBeGreaterThan(0);
                }
            } catch (error) {
                // Should not throw errors
                fail(`System should not throw errors: ${error.message}`);
            }
        });

        expect(processedCount).toBe(largeDataSet.length);
        expect(decisionsCount).toBeGreaterThanOrEqual(0);
    });
});