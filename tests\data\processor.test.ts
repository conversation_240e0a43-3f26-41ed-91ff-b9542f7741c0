import { DataProcessor } from '../../src/data/processor';

describe('DataProcessor', () => {
    let dataProcessor: DataProcessor;

    beforeEach(() => {
        dataProcessor = new DataProcessor();
    });

    test('should process data correctly', () => {
        const rawData = [/* mock raw data */];
        const expectedProcessedData = [/* expected processed data */];

        dataProcessor.processData(rawData);
        const processedData = dataProcessor.getProcessedData();

        expect(processedData).toEqual(expectedProcessedData);
    });

    test('should handle empty data', () => {
        const rawData: any[] = [];

        dataProcessor.processData(rawData);
        const processedData = dataProcessor.getProcessedData();

        expect(processedData).toEqual([]);
    });
});