// tests/risk/riskManager.test.ts

import { RiskManager, RiskConfig } from '../../src/risk/riskManager';
import { moderateRiskConfig } from '../../src/risk/riskConfig';

describe('RiskManager', () => {
    let riskManager: RiskManager;
    const initialBalance = 10000;

    beforeEach(() => {
        riskManager = new RiskManager(initialBalance, moderateRiskConfig);
    });

    describe('Position Size Calculation', () => {
        it('should calculate correct position size based on risk', () => {
            const entryPrice = 1.2000;
            const stopLossPrice = 1.1900; // 100 pips risk
            const expectedRiskAmount = initialBalance * (moderateRiskConfig.maxRiskPerTrade / 100); // 2% = 200
            const priceRisk = entryPrice - stopLossPrice; // 0.01
            const expectedSize = Math.floor(expectedRiskAmount / priceRisk); // 20000

            const actualSize = riskManager.calculatePositionSize(entryPrice, stopLossPrice);
            expect(actualSize).toBe(expectedSize);
        });

        it('should return 0 for zero price risk', () => {
            const entryPrice = 1.2000;
            const stopLossPrice = 1.2000; // Same price = no risk
            
            const actualSize = riskManager.calculatePositionSize(entryPrice, stopLossPrice);
            expect(actualSize).toBe(0);
        });
    });

    describe('Stop Loss and Take Profit Calculation', () => {
        it('should calculate correct stop loss for buy order', () => {
            const entryPrice = 1.2000;
            const expectedStopLoss = entryPrice * (1 - moderateRiskConfig.stopLossPercent / 100);
            
            const actualStopLoss = riskManager.calculateStopLoss(entryPrice, 'buy');
            expect(actualStopLoss).toBeCloseTo(expectedStopLoss, 6);
        });

        it('should calculate correct stop loss for sell order', () => {
            const entryPrice = 1.2000;
            const expectedStopLoss = entryPrice * (1 + moderateRiskConfig.stopLossPercent / 100);
            
            const actualStopLoss = riskManager.calculateStopLoss(entryPrice, 'sell');
            expect(actualStopLoss).toBeCloseTo(expectedStopLoss, 6);
        });

        it('should calculate correct take profit for buy order', () => {
            const entryPrice = 1.2000;
            const expectedTakeProfit = entryPrice * (1 + moderateRiskConfig.takeProfitPercent / 100);
            
            const actualTakeProfit = riskManager.calculateTakeProfit(entryPrice, 'buy');
            expect(actualTakeProfit).toBeCloseTo(expectedTakeProfit, 6);
        });

        it('should calculate correct take profit for sell order', () => {
            const entryPrice = 1.2000;
            const expectedTakeProfit = entryPrice * (1 - moderateRiskConfig.takeProfitPercent / 100);
            
            const actualTakeProfit = riskManager.calculateTakeProfit(entryPrice, 'sell');
            expect(actualTakeProfit).toBeCloseTo(expectedTakeProfit, 6);
        });
    });

    describe('Trade Validation', () => {
        it('should validate trade with good risk/reward ratio', () => {
            const entryPrice = 1.2000;
            const stopLoss = 1.1900; // 100 pips risk
            const takeProfit = 1.2200; // 200 pips reward = 2:1 ratio
            
            const isValid = riskManager.validateTrade(entryPrice, stopLoss, takeProfit, 'buy');
            expect(isValid).toBe(true);
        });

        it('should reject trade with poor risk/reward ratio', () => {
            const entryPrice = 1.2000;
            const stopLoss = 1.1900; // 100 pips risk
            const takeProfit = 1.2050; // 50 pips reward = 0.5:1 ratio
            
            const isValid = riskManager.validateTrade(entryPrice, stopLoss, takeProfit, 'buy');
            expect(isValid).toBe(false);
        });
    });

    describe('Position Management', () => {
        it('should add position correctly', () => {
            const position = riskManager.addPosition('EURUSD', 1000, 1.2000, 'buy');
            
            expect(position.symbol).toBe('EURUSD');
            expect(position.amount).toBe(1000);
            expect(position.entryPrice).toBe(1.2000);
            expect(position.type).toBe('buy');
            expect(position.stopLoss).toBeDefined();
            expect(position.takeProfit).toBeDefined();
            
            const openPositions = riskManager.getOpenPositions();
            expect(openPositions).toHaveLength(1);
        });

        it('should trigger stop loss for buy position', () => {
            const entryPrice = 1.2000;
            riskManager.addPosition('EURUSD', 1000, entryPrice, 'buy');
            
            const stopLossPrice = riskManager.calculateStopLoss(entryPrice, 'buy');
            const currentPrices = { 'EURUSD': stopLossPrice - 0.0001 }; // Below stop loss
            
            riskManager.updatePositions(currentPrices);
            
            const openPositions = riskManager.getOpenPositions();
            expect(openPositions).toHaveLength(0); // Position should be closed
            
            const tradeHistory = riskManager.getTradeHistory();
            expect(tradeHistory).toHaveLength(1);
            expect(tradeHistory[0].reason).toBe('Stop Loss');
        });

        it('should trigger take profit for buy position', () => {
            const entryPrice = 1.2000;
            riskManager.addPosition('EURUSD', 1000, entryPrice, 'buy');
            
            const takeProfitPrice = riskManager.calculateTakeProfit(entryPrice, 'buy');
            const currentPrices = { 'EURUSD': takeProfitPrice + 0.0001 }; // Above take profit
            
            riskManager.updatePositions(currentPrices);
            
            const openPositions = riskManager.getOpenPositions();
            expect(openPositions).toHaveLength(0); // Position should be closed
            
            const tradeHistory = riskManager.getTradeHistory();
            expect(tradeHistory).toHaveLength(1);
            expect(tradeHistory[0].reason).toBe('Take Profit');
        });
    });

    describe('Risk Controls', () => {
        it('should allow trading when within risk limits', () => {
            expect(riskManager.canTrade()).toBe(true);
        });

        it('should prevent trading when max drawdown exceeded', () => {
            // Simulate large loss to trigger max drawdown
            const testConfig: RiskConfig = {
                ...moderateRiskConfig,
                maxDrawdown: 1.0 // 1% max drawdown
            };
            
            const testRiskManager = new RiskManager(initialBalance, testConfig);
            
            // Add losing position that will trigger stop loss
            testRiskManager.addPosition('EURUSD', 10000, 1.2000, 'buy');
            const stopLossPrice = testRiskManager.calculateStopLoss(1.2000, 'buy');
            const currentPrices = { 'EURUSD': stopLossPrice - 0.0001 }; // Trigger stop loss
            testRiskManager.updatePositions(currentPrices);
            
            expect(testRiskManager.canTrade()).toBe(false);
        });

        it('should calculate drawdown correctly', () => {
            // Add position and simulate loss
            riskManager.addPosition('EURUSD', 1000, 1.2000, 'buy');
            const currentPrices = { 'EURUSD': 1.1000 }; // 200 pip loss = 200 currency units
            riskManager.updatePositions(currentPrices);
            
            const drawdown = riskManager.getCurrentDrawdown();
            expect(drawdown).toBeGreaterThan(0);
        });
    });

    describe('Risk Metrics', () => {
        it('should calculate win rate correctly', () => {
            // Add winning trade - trigger take profit
            riskManager.addPosition('EURUSD', 1000, 1.2000, 'buy');
            const takeProfitPrice = riskManager.calculateTakeProfit(1.2000, 'buy');
            let currentPrices = { 'EURUSD': takeProfitPrice + 0.0001 }; // Above take profit
            riskManager.updatePositions(currentPrices);
            
            // Add losing trade - trigger stop loss
            riskManager.addPosition('EURUSD', 1000, 1.2000, 'buy');
            const stopLossPrice = riskManager.calculateStopLoss(1.2000, 'buy');
            currentPrices = { 'EURUSD': stopLossPrice - 0.0001 }; // Below stop loss
            riskManager.updatePositions(currentPrices);
            
            const metrics = riskManager.getRiskMetrics();
            expect(metrics.totalTrades).toBe(2);
            expect(metrics.winningTrades).toBe(1);
            expect(metrics.losingTrades).toBe(1);
            expect(metrics.winRate).toBe(50);
        });

        it('should track total P&L correctly', () => {
            const initialMetrics = riskManager.getRiskMetrics();
            expect(initialMetrics.totalPnL).toBe(0);
            
            // Add profitable trade - trigger take profit
            riskManager.addPosition('EURUSD', 1000, 1.2000, 'buy');
            const takeProfitPrice = riskManager.calculateTakeProfit(1.2000, 'buy');
            const currentPrices = { 'EURUSD': takeProfitPrice + 0.0001 }; // Above take profit
            riskManager.updatePositions(currentPrices);
            
            const finalMetrics = riskManager.getRiskMetrics();
            expect(finalMetrics.totalPnL).toBeGreaterThan(0);
        });
    });
});