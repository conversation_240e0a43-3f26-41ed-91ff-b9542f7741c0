// src/analysis/strategyComparison.ts

import { SmartStrategy } from '../agent/smartStrategy';
import { OptimizedSmartStrategy } from '../agent/optimizedSmartStrategy';
import { testScenarios, EnhancedMarketData } from '../data/enhancedMockData';
import { RiskManager } from '../risk/riskManager';
import { conservativeRiskConfig } from '../risk/riskConfig';

export interface StrategyPerformance {
    strategyName: string;
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    winRate: number;
    totalPnL: number;
    avgTradeSize: number;
    maxDrawdown: number;
    qualityMetrics: {
        highQualitySignals: number;
        avgConfidence: number;
        avgConfirmations: number;
    };
    riskMetrics: {
        avgRiskPerTrade: number;
        maxConsecutiveLosses: number;
        sharpeRatio: number;
    };
}

export interface ComparisonResult {
    originalStrategy: StrategyPerformance;
    optimizedStrategy: StrategyPerformance;
    improvement: {
        winRateImprovement: number;
        pnlImprovement: number;
        riskReduction: number;
        qualityImprovement: number;
    };
    scenarios: {
        [scenarioName: string]: {
            original: StrategyPerformance;
            optimized: StrategyPerformance;
        };
    };
}

export class StrategyComparison {
    
    static async compareStrategies(initialBalance: number = 10000): Promise<ComparisonResult> {
        console.log('🔍 === POROVNANIE STRATÉGIÍ ===');
        console.log('Porovnávam pôvodnú Smart Strategy s optimalizovanou verziou...\n');

        const scenarios = [
            { name: 'Optimálne podmienky', data: testScenarios.optimalConditions },
            { name: 'Silný rastúci trend', data: testScenarios.strongUptrend },
            { name: 'Silný klesajúci trend', data: testScenarios.strongDowntrend },
            { name: 'Sideways trh', data: testScenarios.sidewaysMarket },
            { name: 'Volatilný trh', data: testScenarios.volatileMarket },
            { name: 'Zmiešané podmienky', data: testScenarios.mixedConditions }
        ];

        // Test pôvodnej stratégie
        console.log('📊 Testovanie pôvodnej Smart Strategy...');
        const originalResults = await this.testStrategy(
            new SmartStrategy(), 
            scenarios, 
            initialBalance,
            'original'
        );

        // Test optimalizovanej stratégie
        console.log('⚡ Testovanie optimalizovanej Smart Strategy...');
        const optimizedResults = await this.testStrategy(
            new OptimizedSmartStrategy(), 
            scenarios, 
            initialBalance,
            'optimized'
        );

        // Vypočítaj celkové výsledky
        const originalOverall = this.calculateOverallPerformance(originalResults, 'Pôvodná Smart Strategy');
        const optimizedOverall = this.calculateOverallPerformance(optimizedResults, 'Optimalizovaná Smart Strategy');

        // Vypočítaj zlepšenia
        const improvement = this.calculateImprovement(originalOverall, optimizedOverall);

        const result: ComparisonResult = {
            originalStrategy: originalOverall,
            optimizedStrategy: optimizedOverall,
            improvement,
            scenarios: {}
        };

        // Pridaj výsledky scenárov
        scenarios.forEach((scenario, index) => {
            result.scenarios[scenario.name] = {
                original: originalResults[index],
                optimized: optimizedResults[index]
            };
        });

        return result;
    }

    private static async testStrategy(
        strategy: SmartStrategy | OptimizedSmartStrategy, 
        scenarios: { name: string; data: EnhancedMarketData[] }[],
        initialBalance: number,
        strategyType: 'original' | 'optimized'
    ): Promise<StrategyPerformance[]> {
        
        const results: StrategyPerformance[] = [];

        for (const scenario of scenarios) {
            console.log(`  🎯 Testovanie scenára: ${scenario.name}`);
            
            const riskManager = new RiskManager(initialBalance, conservativeRiskConfig);
            const currentPrices: { [symbol: string]: number } = {};
            
            let trades = 0;
            let wins = 0;
            let losses = 0;
            let totalTradeSize = 0;
            let maxConsecutiveLosses = 0;
            let currentConsecutiveLosses = 0;
            let highQualitySignals = 0;
            let totalConfidence = 0;
            let totalConfirmations = 0;
            let analysisCount = 0;

            for (let i = 0; i < scenario.data.length; i++) {
                const dataPoint = scenario.data[i];
                currentPrices['EURUSD'] = dataPoint.price;

                // Aktualizuj pozície
                riskManager.updatePositions(currentPrices);

                if (!riskManager.canTrade()) continue;

                // Rozhoduj pomocou stratégie
                const order = strategy.decideAction(dataPoint);

                if (order) {
                    const entryPrice = dataPoint.price;
                    const stopLoss = riskManager.calculateStopLoss(entryPrice, order.action);
                    const takeProfit = riskManager.calculateTakeProfit(entryPrice, order.action);

                    if (riskManager.validateTrade(entryPrice, stopLoss, takeProfit, order.action)) {
                        const optimalSize = riskManager.calculatePositionSize(entryPrice, stopLoss);
                        const finalAmount = Math.min(order.amount, optimalSize);

                        if (finalAmount > 0) {
                            riskManager.addPosition('EURUSD', finalAmount, entryPrice, order.action);
                            trades++;
                            totalTradeSize += finalAmount;

                            // Simuluj výsledok obchodu (zjednodušené)
                            const futurePrice = i + 10 < scenario.data.length ? 
                                scenario.data[i + 10].price : dataPoint.price;
                            
                            const isWin = (order.action === 'buy' && futurePrice > entryPrice) ||
                                         (order.action === 'sell' && futurePrice < entryPrice);
                            
                            if (isWin) {
                                wins++;
                                currentConsecutiveLosses = 0;
                                if (strategyType === 'optimized') {
                                    (strategy as OptimizedSmartStrategy).recordTradeWin();
                                }
                            } else {
                                losses++;
                                currentConsecutiveLosses++;
                                maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentConsecutiveLosses);
                                if (strategyType === 'optimized') {
                                    (strategy as OptimizedSmartStrategy).recordTradeLoss();
                                }
                            }
                        }
                    }
                }

                // Zbieraj metriky kvality
                if (strategyType === 'optimized') {
                    const history = (strategy as OptimizedSmartStrategy).getOptimizedAnalysisHistory();
                    if (history.length > analysisCount) {
                        const lastAnalysis = history[history.length - 1];
                        analysisCount = history.length;
                        
                        if (lastAnalysis.signals.quality === 'A+' || lastAnalysis.signals.quality === 'A') {
                            highQualitySignals++;
                        }
                        totalConfidence += lastAnalysis.signals.confidence;
                        totalConfirmations += lastAnalysis.signals.confirmations;
                    }
                } else {
                    const history = (strategy as SmartStrategy).getAnalysisHistory();
                    if (history.length > analysisCount) {
                        const lastAnalysis = history[history.length - 1];
                        analysisCount = history.length;
                        totalConfidence += lastAnalysis.signals.confidence;
                        // Pôvodná stratégia nemá confirmations, použijeme odhad
                        totalConfirmations += 2; // Priemerný odhad
                    }
                }
            }

            const metrics = riskManager.getRiskMetrics();
            const finalBalance = riskManager.getCurrentBalance();
            const winRate = trades > 0 ? (wins / trades) * 100 : 0;
            const avgTradeSize = trades > 0 ? totalTradeSize / trades : 0;
            const avgConfidence = analysisCount > 0 ? totalConfidence / analysisCount : 0;
            const avgConfirmations = analysisCount > 0 ? totalConfirmations / analysisCount : 0;

            results.push({
                strategyName: strategyType === 'original' ? 'Smart Strategy' : 'Optimalizovaná Smart Strategy',
                totalTrades: trades,
                winningTrades: wins,
                losingTrades: losses,
                winRate,
                totalPnL: finalBalance - initialBalance,
                avgTradeSize,
                maxDrawdown: Math.abs(metrics.currentDrawdown),
                qualityMetrics: {
                    highQualitySignals,
                    avgConfidence,
                    avgConfirmations
                },
                riskMetrics: {
                    avgRiskPerTrade: trades > 0 ? (initialBalance * 0.02) : 0, // 2% risk estimate
                    maxConsecutiveLosses,
                    sharpeRatio: this.calculateSharpeRatio(finalBalance - initialBalance, Math.abs(metrics.currentDrawdown))
                }
            });

            console.log(`    ✅ ${scenario.name}: ${trades} obchodov, ${winRate.toFixed(1)}% win rate`);
        }

        return results;
    }

    private static calculateOverallPerformance(results: StrategyPerformance[], strategyName: string): StrategyPerformance {
        const totalTrades = results.reduce((sum, r) => sum + r.totalTrades, 0);
        const totalWins = results.reduce((sum, r) => sum + r.winningTrades, 0);
        const totalLosses = results.reduce((sum, r) => sum + r.losingTrades, 0);
        const totalPnL = results.reduce((sum, r) => sum + r.totalPnL, 0);
        const avgWinRate = results.length > 0 ? results.reduce((sum, r) => sum + r.winRate, 0) / results.length : 0;
        const maxDrawdown = Math.max(...results.map(r => r.maxDrawdown));
        const avgTradeSize = results.length > 0 ? results.reduce((sum, r) => sum + r.avgTradeSize, 0) / results.length : 0;

        // Quality metrics
        const totalHighQuality = results.reduce((sum, r) => sum + r.qualityMetrics.highQualitySignals, 0);
        const avgConfidence = results.length > 0 ? 
            results.reduce((sum, r) => sum + r.qualityMetrics.avgConfidence, 0) / results.length : 0;
        const avgConfirmations = results.length > 0 ? 
            results.reduce((sum, r) => sum + r.qualityMetrics.avgConfirmations, 0) / results.length : 0;

        // Risk metrics
        const maxConsecutiveLosses = Math.max(...results.map(r => r.riskMetrics.maxConsecutiveLosses));
        const avgSharpeRatio = results.length > 0 ? 
            results.reduce((sum, r) => sum + r.riskMetrics.sharpeRatio, 0) / results.length : 0;

        return {
            strategyName,
            totalTrades,
            winningTrades: totalWins,
            losingTrades: totalLosses,
            winRate: avgWinRate,
            totalPnL,
            avgTradeSize,
            maxDrawdown,
            qualityMetrics: {
                highQualitySignals: totalHighQuality,
                avgConfidence,
                avgConfirmations
            },
            riskMetrics: {
                avgRiskPerTrade: 200, // 2% of 10000
                maxConsecutiveLosses,
                sharpeRatio: avgSharpeRatio
            }
        };
    }

    private static calculateImprovement(original: StrategyPerformance, optimized: StrategyPerformance) {
        const winRateImprovement = optimized.winRate - original.winRate;
        const pnlImprovement = ((optimized.totalPnL - original.totalPnL) / Math.abs(original.totalPnL || 1)) * 100;
        const riskReduction = ((original.maxDrawdown - optimized.maxDrawdown) / original.maxDrawdown) * 100;
        const qualityImprovement = ((optimized.qualityMetrics.avgConfidence - original.qualityMetrics.avgConfidence) / original.qualityMetrics.avgConfidence) * 100;

        return {
            winRateImprovement,
            pnlImprovement,
            riskReduction,
            qualityImprovement
        };
    }

    private static calculateSharpeRatio(returns: number, volatility: number): number {
        if (volatility === 0) return 0;
        return returns / volatility;
    }

    static printComparisonReport(result: ComparisonResult): void {
        console.log('\n🏆 === FINÁLNE POROVNANIE STRATÉGIÍ ===\n');

        // Celkové výsledky
        console.log('📊 CELKOVÉ VÝSLEDKY:');
        console.log(`Pôvodná stratégia:`);
        console.log(`  • Obchody: ${result.originalStrategy.totalTrades}`);
        console.log(`  • Win Rate: ${result.originalStrategy.winRate.toFixed(1)}%`);
        console.log(`  • P&L: ${result.originalStrategy.totalPnL.toFixed(2)}`);
        console.log(`  • Max Drawdown: ${result.originalStrategy.maxDrawdown.toFixed(2)}%`);
        console.log(`  • Avg Confidence: ${result.originalStrategy.qualityMetrics.avgConfidence.toFixed(1)}%`);

        console.log(`\nOptimalizovaná stratégia:`);
        console.log(`  • Obchody: ${result.optimizedStrategy.totalTrades}`);
        console.log(`  • Win Rate: ${result.optimizedStrategy.winRate.toFixed(1)}%`);
        console.log(`  • P&L: ${result.optimizedStrategy.totalPnL.toFixed(2)}`);
        console.log(`  • Max Drawdown: ${result.optimizedStrategy.maxDrawdown.toFixed(2)}%`);
        console.log(`  • Avg Confidence: ${result.optimizedStrategy.qualityMetrics.avgConfidence.toFixed(1)}%`);
        console.log(`  • High Quality Signals: ${result.optimizedStrategy.qualityMetrics.highQualitySignals}`);

        // Zlepšenia
        console.log('\n📈 ZLEPŠENIA:');
        console.log(`  • Win Rate: ${result.improvement.winRateImprovement > 0 ? '+' : ''}${result.improvement.winRateImprovement.toFixed(1)}%`);
        console.log(`  • P&L: ${result.improvement.pnlImprovement > 0 ? '+' : ''}${result.improvement.pnlImprovement.toFixed(1)}%`);
        console.log(`  • Risk Reduction: ${result.improvement.riskReduction > 0 ? '+' : ''}${result.improvement.riskReduction.toFixed(1)}%`);
        console.log(`  • Quality: ${result.improvement.qualityImprovement > 0 ? '+' : ''}${result.improvement.qualityImprovement.toFixed(1)}%`);

        // Scenáre
        console.log('\n📋 VÝSLEDKY PODĽA SCENÁROV:');
        Object.entries(result.scenarios).forEach(([scenarioName, scenarioResult]) => {
            console.log(`\n${scenarioName}:`);
            console.log(`  Pôvodná: ${scenarioResult.original.winRate.toFixed(1)}% win rate, ${scenarioResult.original.totalTrades} obchodov`);
            console.log(`  Optimalizovaná: ${scenarioResult.optimized.winRate.toFixed(1)}% win rate, ${scenarioResult.optimized.totalTrades} obchodov`);
            const improvement = scenarioResult.optimized.winRate - scenarioResult.original.winRate;
            console.log(`  Zlepšenie: ${improvement > 0 ? '+' : ''}${improvement.toFixed(1)}%`);
        });

        // Záver
        console.log('\n🎯 ZÁVER:');
        if (result.improvement.winRateImprovement > 5) {
            console.log('🏆 VÝBORNÉ ZLEPŠENIE! Optimalizovaná stratégia výrazne prevyšuje pôvodnú.');
        } else if (result.improvement.winRateImprovement > 0) {
            console.log('✅ DOBRÉ ZLEPŠENIE! Optimalizovaná stratégia je lepšia ako pôvodná.');
        } else {
            console.log('⚠️ POTREBNÉ ĎALŠIE OPTIMALIZÁCIE! Optimalizovaná stratégia nepriniesla očakávané zlepšenie.');
        }
    }
}