import * as ti from 'technicalindicators';
export class DataProcessor {
    private processedData: any[] = [];
    private prices: number[] = [];


    public processData(rawData: any[]): any[] {
        // Implement data processing logic here
        this.processedData = rawData.map(data => {
            this.prices.push(data.price);
            if (this.prices.length > 26) { // Keep only the last 26 prices for MACD calculation
                this.prices.shift();
            }
            // Calculate additional indicators here
            const ema = this.calculateEMA(this.prices, 14);
            const macdResult = this.calculateMACD(this.prices);
            return {
                ...data,
                ema,
                macd: macdResult.macd,
                macdSignal: macdResult.signal,
                macdHistogram: macdResult.histogram,
                processed: true // Example transformation
            };
        });
        return this.processedData;
    }

    public getProcessedData(): any[] {
        return this.processedData;
    }

    private calculateEMA(prices: number[], period: number): number {
        // Implement EMA calculation logic here
        if (prices.length < period) {
            return prices[prices.length - 1] || 0;
        }
        const ema = ti.EMA.calculate({ period: period, values: prices });
        return ema[ema.length - 1] || 0;
    }

    private calculateMACD(prices: number[]): any {
        // Implement MACD calculation logic here
        if (prices.length < 26) {
            return { macd: 0, signal: 0, histogram: 0 };
        }
        const macd = ti.MACD.calculate({ values: prices, fastPeriod: 12, slowPeriod: 26, signalPeriod: 9, SimpleMAOscillator: false, SimpleMASignal: false });
        return macd[macd.length - 1] || { macd: 0, signal: 0, histogram: 0 };
    }
}