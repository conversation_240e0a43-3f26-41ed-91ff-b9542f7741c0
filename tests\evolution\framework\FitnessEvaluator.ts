// tests/evolution/framework/FitnessEvaluator.ts
// Vyhodnocovanie fitness pre trading stratégie

import { StrategyGenome } from './StrategyGenome';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';
import { SmartStrategy } from '../../../src/agent/smartStrategy';
import { RiskManager } from '../../../src/risk/riskManager';
import { conservativeRiskConfig } from '../../../src/risk/riskConfig';
import Trader from '../../../src/agent/trader';

export interface FitnessResult {
    winRate: number;                    // 0-1
    profitFactor: number;              // Profit/Loss ratio
    sharpeRatio: number;               // Risk-adjusted return
    maxDrawdown: number;               // Maximum drawdown (0-1)
    totalTrades: number;               // Number of trades executed
    totalProfit: number;               // Total profit/loss
    averageWin: number;                // Average winning trade
    averageLoss: number;               // Average losing trade
    consecutiveWins: number;           // Max consecutive wins
    consecutiveLosses: number;         // Max consecutive losses
    profitability: number;             // Total return percentage
    volatility: number;                // Strategy volatility
    calmarRatio: number;               // Return/MaxDrawdown
    sortinoRatio: number;              // Downside deviation adjusted return
    overallScore: number;              // Composite fitness score
    
    // Dodatočné metriky
    tradesPerDay: number;
    winningDays: number;
    losingDays: number;
    maxDailyProfit: number;
    maxDailyLoss: number;
    recoveryFactor: number;            // Net profit / Max drawdown
    expectancy: number;                // Expected value per trade
}

export interface TradeResult {
    profit: number;
    isWin: boolean;
    entryPrice: number;
    exitPrice: number;
    timestamp: number;
    duration: number;
}

export class FitnessEvaluator {
    private riskWeights = {
        winRate: 0.25,
        profitFactor: 0.20,
        sharpeRatio: 0.15,
        maxDrawdown: 0.15,  // Negative weight (lower is better)
        calmarRatio: 0.10,
        sortinoRatio: 0.10,
        expectancy: 0.05
    };

    /**
     * Vyhodnotí fitness stratégie na základe genómu
     */
    async evaluateFitness(
        genome: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number
    ): Promise<FitnessResult> {
        
        // Vytvor stratégiu z genómu
        const strategy = genome.createStrategy();
        
        // Simuluj trading s danou stratégiou
        const tradeResults = await this.simulateTrading(
            strategy,
            genome,
            marketData,
            initialBalance
        );
        
        // Vypočítaj fitness metriky
        const fitness = this.calculateFitnessMetrics(tradeResults, initialBalance);
        
        // Vypočítaj celkové skóre
        fitness.overallScore = this.calculateOverallScore(fitness);
        
        return fitness;
    }

    /**
     * Simuluje trading s danou stratégiou
     */
    private async simulateTrading(
        strategy: SmartStrategy,
        genome: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number
    ): Promise<TradeResult[]> {
        
        const trader = new Trader(initialBalance);
        const riskManager = new RiskManager(initialBalance, conservativeRiskConfig);
        const tradeResults: TradeResult[] = [];
        const params = genome.getParameters();
        
        let currentPosition: 'long' | 'short' | null = null;
        let entryPrice = 0;
        let entryTime = 0;
        let dailyTrades = 0;
        let consecutiveLosses = 0;
        let currentDay = '';
        
        for (let i = 1; i < marketData.length; i++) {
            const currentData = marketData[i];
            const previousData = marketData[i - 1];
            
            // Reset denných obchodov
            const currentDayStr = new Date(currentData.timestamp).toDateString();
            if (currentDayStr !== currentDay) {
                currentDay = currentDayStr;
                dailyTrades = 0;
            }
            
            // Kontrola limitov
            if (dailyTrades >= params.maxDailyTrades) continue;
            if (consecutiveLosses >= params.maxConsecutiveLosses) continue;
            
            // Ak máme pozíciu, kontroluj exit podmienky
            if (currentPosition) {
                const profit = currentPosition === 'long' 
                    ? (currentData.price - entryPrice) / entryPrice
                    : (entryPrice - currentData.price) / entryPrice;
                
                const shouldExit = 
                    profit >= params.profitTarget / 100 ||  // Profit target
                    profit <= -params.stopLoss / 100 ||    // Stop loss
                    (currentData.timestamp - entryTime) > 24 * 60 * 60 * 1000; // Max hold time
                
                if (shouldExit) {
                    const tradeProfit = profit * initialBalance * 0.01; // 1% position size
                    const isWin = profit > 0;
                    
                    tradeResults.push({
                        profit: tradeProfit,
                        isWin,
                        entryPrice,
                        exitPrice: currentData.price,
                        timestamp: currentData.timestamp,
                        duration: currentData.timestamp - entryTime
                    });
                    
                    if (isWin) {
                        consecutiveLosses = 0;
                    } else {
                        consecutiveLosses++;
                    }
                    
                    currentPosition = null;
                    dailyTrades++;
                }
                continue;
            }
            
            // Ak nemáme pozíciu, hľadaj entry signál
            const decision = strategy.decideAction(currentData);
            
            if (decision && this.shouldTakeSignal(decision, params, currentData)) {
                currentPosition = decision.action === 'buy' ? 'long' : 'short';
                entryPrice = currentData.price;
                entryTime = currentData.timestamp;
            }
        }
        
        return tradeResults;
    }

    /**
     * Rozhodne či prijať trading signál na základe parametrov genómu
     */
    private shouldTakeSignal(
        decision: any,
        params: any,
        marketData: EnhancedMarketData
    ): boolean {
        // Simuluj kontroly kvality signálu
        const signalStrength = Math.random() * 100;
        const confidence = Math.random() * 100;
        
        return signalStrength >= params.minSignalStrength && 
               confidence >= params.minConfidence;
    }

    /**
     * Vypočíta fitness metriky z výsledkov obchodov
     */
    private calculateFitnessMetrics(
        tradeResults: TradeResult[],
        initialBalance: number
    ): FitnessResult {
        
        if (tradeResults.length === 0) {
            return this.getZeroFitness();
        }
        
        const wins = tradeResults.filter(t => t.isWin);
        const losses = tradeResults.filter(t => !t.isWin);
        
        const totalProfit = tradeResults.reduce((sum, t) => sum + t.profit, 0);
        const winRate = wins.length / tradeResults.length;
        
        const totalWinAmount = wins.reduce((sum, t) => sum + t.profit, 0);
        const totalLossAmount = Math.abs(losses.reduce((sum, t) => sum + t.profit, 0));
        
        const profitFactor = totalLossAmount > 0 ? totalWinAmount / totalLossAmount : totalWinAmount > 0 ? 10 : 1;
        
        const averageWin = wins.length > 0 ? totalWinAmount / wins.length : 0;
        const averageLoss = losses.length > 0 ? totalLossAmount / losses.length : 0;
        
        // Vypočítaj drawdown
        let runningBalance = initialBalance;
        let peak = initialBalance;
        let maxDrawdown = 0;
        
        for (const trade of tradeResults) {
            runningBalance += trade.profit;
            if (runningBalance > peak) {
                peak = runningBalance;
            }
            const drawdown = (peak - runningBalance) / peak;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }
        }
        
        // Vypočítaj volatilitu
        const returns = tradeResults.map(t => t.profit / initialBalance);
        const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
        const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
        const volatility = Math.sqrt(variance);
        
        // Sharpe ratio (predpokladáme risk-free rate 0)
        const sharpeRatio = volatility > 0 ? avgReturn / volatility : 0;
        
        // Sortino ratio (len downside volatility)
        const downsideReturns = returns.filter(r => r < avgReturn);
        const downsideVariance = downsideReturns.length > 0 
            ? downsideReturns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / downsideReturns.length
            : 0;
        const downsideVolatility = Math.sqrt(downsideVariance);
        const sortinoRatio = downsideVolatility > 0 ? avgReturn / downsideVolatility : 0;
        
        // Calmar ratio
        const annualizedReturn = (totalProfit / initialBalance) * (365 / 30); // Predpokladáme 30 dní dát
        const calmarRatio = maxDrawdown > 0 ? annualizedReturn / maxDrawdown : annualizedReturn;
        
        // Consecutive wins/losses
        let maxConsecutiveWins = 0;
        let maxConsecutiveLosses = 0;
        let currentWinStreak = 0;
        let currentLossStreak = 0;
        
        for (const trade of tradeResults) {
            if (trade.isWin) {
                currentWinStreak++;
                currentLossStreak = 0;
                maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWinStreak);
            } else {
                currentLossStreak++;
                currentWinStreak = 0;
                maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak);
            }
        }
        
        // Expectancy
        const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss);
        
        // Recovery factor
        const recoveryFactor = maxDrawdown > 0 ? (totalProfit / initialBalance) / maxDrawdown : 0;
        
        return {
            winRate,
            profitFactor,
            sharpeRatio,
            maxDrawdown,
            totalTrades: tradeResults.length,
            totalProfit,
            averageWin,
            averageLoss,
            consecutiveWins: maxConsecutiveWins,
            consecutiveLosses: maxConsecutiveLosses,
            profitability: totalProfit / initialBalance,
            volatility,
            calmarRatio,
            sortinoRatio,
            overallScore: 0, // Bude vypočítané neskôr
            
            tradesPerDay: tradeResults.length / 30, // Predpokladáme 30 dní
            winningDays: 0, // TODO: implementovať
            losingDays: 0,  // TODO: implementovať
            maxDailyProfit: 0, // TODO: implementovať
            maxDailyLoss: 0,   // TODO: implementovať
            recoveryFactor,
            expectancy
        };
    }

    /**
     * Vypočíta celkové fitness skóre
     */
    private calculateOverallScore(fitness: FitnessResult): number {
        let score = 0;
        
        // Pozitívne metriky
        score += fitness.winRate * this.riskWeights.winRate * 100;
        score += Math.min(fitness.profitFactor, 5) * this.riskWeights.profitFactor * 20; // Cap at 5
        score += Math.max(0, fitness.sharpeRatio) * this.riskWeights.sharpeRatio * 50;
        score += Math.max(0, fitness.calmarRatio) * this.riskWeights.calmarRatio * 20;
        score += Math.max(0, fitness.sortinoRatio) * this.riskWeights.sortinoRatio * 30;
        score += Math.max(0, fitness.expectancy) * this.riskWeights.expectancy * 1000;
        
        // Negatívne metriky (penalizácia)
        score -= fitness.maxDrawdown * this.riskWeights.maxDrawdown * 200;
        
        // Penalizácia za príliš málo obchodov
        if (fitness.totalTrades < 10) {
            score *= 0.5;
        }
        
        // Bonus za vysoký počet obchodov (do určitého limitu)
        if (fitness.totalTrades > 50 && fitness.totalTrades < 200) {
            score *= 1.1;
        }
        
        return Math.max(0, score);
    }

    /**
     * Vráti nulové fitness pre stratégie bez obchodov
     */
    private getZeroFitness(): FitnessResult {
        return {
            winRate: 0,
            profitFactor: 0,
            sharpeRatio: 0,
            maxDrawdown: 0,
            totalTrades: 0,
            totalProfit: 0,
            averageWin: 0,
            averageLoss: 0,
            consecutiveWins: 0,
            consecutiveLosses: 0,
            profitability: 0,
            volatility: 0,
            calmarRatio: 0,
            sortinoRatio: 0,
            overallScore: 0,
            tradesPerDay: 0,
            winningDays: 0,
            losingDays: 0,
            maxDailyProfit: 0,
            maxDailyLoss: 0,
            recoveryFactor: 0,
            expectancy: 0
        };
    }

    /**
     * Porovná dve fitness výsledky
     */
    compareFitness(fitness1: FitnessResult, fitness2: FitnessResult): number {
        return fitness1.overallScore - fitness2.overallScore;
    }

    /**
     * Nastaví váhy pre fitness komponenty
     */
    setFitnessWeights(weights: Partial<typeof this.riskWeights>): void {
        this.riskWeights = { ...this.riskWeights, ...weights };
    }
}
