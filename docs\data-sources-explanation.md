# Zdroje dát pre indikátory a stratégie

## 🎯 Odkiaľ sa berú indikátory?

### ✅ 1. Lokálne vypočítané indikátory (`src/indicators/technicalIndicators.ts`)

**Všetky indikátory sa počítajú LOKÁLNE** z historických cenových dát:

```typescript
// Bollinger Bands - počíta sa z cien
static calculateBollingerBands(prices: number[], period: number = 20)

// Stochastic RSI - počíta sa z RSI hodnôt  
static calculateStochasticRSI(prices: number[], rsiPeriod: number = 14)

// Williams %R - počíta sa z high/low/close
static calculateWilliamsR(highs: number[], lows: number[], closes: number[])

// ATR (Average True Range) - volatilita
static calculateATR(highs: number[], lows: number[], closes: number[])

// Fibonacci retracement levels
static calculateFibonacciLevels(high: number, low: number)

// Support/Resistance levels
static findSupportResistance(prices: number[], period: number = 20)

// Trend detection
static detectTrend(prices: number[], shortPeriod: number = 10, longPeriod: number = 30)
```

### ✅ 2. Zdroje cenových dát

#### **Aktuálne (fallback režim):**
- **Mock dáta**: Generované lokálne pre testovanie
- **API fallback**: `appConfig.apiUrl` (ak je nakonfigurované)

#### **S MCP serverom (budúce):**
- **Yahoo Finance**: Web scraping (free)
- **Alpha Vantage**: Free tier API
- **CoinGecko**: Free crypto API  
- **FRED Economic Data**: Free economic indicators

### ✅ 3. Ako funguje generovanie indikátorov

```typescript
// V DataFetcher - mock indikátory ako fallback
private generateMockIndicators(symbol: string, indicators: string[]): any {
    const mockData: any = { symbol, indicators: {} };
    
    indicators.forEach(indicator => {
        switch (indicator) {
            case 'RSI':
                mockData.indicators.RSI = Math.random() * 100; // 0-100
                break;
            case 'MACD':
                mockData.indicators.MACD = {
                    macd: Math.random() * 0.01,
                    signal: Math.random() * 0.01,
                    histogram: Math.random() * 0.005
                };
                break;
            case 'SMA':
                mockData.indicators.SMA = 1.0850 + (Math.random() - 0.5) * 0.01;
                break;
        }
    });
    
    return mockData;
}
```

## 🌐 Internetové zdroje (cez MCP server)

### **Yahoo Finance (Free)**
```typescript
// MCP server adaptér pre Yahoo Finance
async getForexData(pair: string, interval: string, period: string) {
    // Scraping Yahoo Finance bez API kľúča
    // URL: https://finance.yahoo.com/quote/EURUSD=X/history
}
```

### **Alpha Vantage (Free Tier)**
```typescript
// 5 calls/minute, 500 calls/day
// URL: https://www.alphavantage.co/query?function=FX_DAILY&from_symbol=EUR&to_symbol=USD
```

### **CoinGecko (Free)**
```typescript
// 10-50 calls/minute bez API kľúča
// URL: https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd
```

## 📊 Proces získavania dát

### **1. Hierarchia zdrojov:**
```
1. MCP Server (real-time) 
   ↓ (ak zlyhá)
2. API Fallback
   ↓ (ak zlyhá)  
3. Mock dáta (vždy funguje)
```

### **2. Výpočet indikátorov:**
```typescript
// 1. Získaj cenové dáta
const marketData = await dataFetcher.fetchMarketData('EURUSD', startDate, endDate);

// 2. Extrahuj ceny
const prices = Object.values(marketData).map(d => d.close);

// 3. Vypočítaj indikátory LOKÁLNE
const rsi = TechnicalIndicators.calculateStochasticRSI(prices, 14, 14);
const bollinger = TechnicalIndicators.calculateBollingerBands(prices, 20, 2);
const trend = TechnicalIndicators.detectTrend(prices, 10, 30);
```

## 🔒 Bezpečnosť a limity

### **Free API limity:**
- **Yahoo Finance**: Rate limiting potrebný
- **Alpha Vantage**: 5 calls/minute, 500/day
- **CoinGecko**: 10-50 calls/minute
- **FRED**: Žiadne limity (registrácia potrebná)

### **Fallback stratégia:**
```typescript
// Ak MCP server nedostupný
if (this.useMCP && this.mcpAvailable !== false) {
    try {
        // Skús MCP server
        const mcpData = await this.fetchRealTimeData(symbol);
        return this.formatMCPData(mcpData, startDate, endDate);
    } catch (error) {
        // Prepni na fallback
        this.mcpAvailable = false;
        this.useMCP = false;
    }
}

// Použij API fallback alebo mock dáta
return this.fetchMarketDataAPI(symbol, startDate, endDate);
```

## 🎯 Záver

**Indikátory sa NEPOTREBUJÚ sťahovať z internetu** - všetky sa počítajú lokálne z cenových dát!

### **Čo sa sťahuje z internetu:**
- ✅ **Cenové dáta** (OHLC - Open, High, Low, Close)
- ✅ **Volume dáta**
- ✅ **Ekonomické správy** (voliteľne)

### **Čo sa počíta lokálne:**
- ✅ **Všetky technické indikátory** (RSI, MACD, Bollinger Bands, atď.)
- ✅ **Support/Resistance levels**
- ✅ **Trend detection**
- ✅ **Fibonacci levels**
- ✅ **Volume analysis**

### **Výhody tohto prístupu:**
1. **Rýchlosť**: Indikátory sa počítajú okamžite
2. **Spoľahlivosť**: Nezávislé od internetového pripojenia
3. **Flexibilita**: Môžete pridať vlastné indikátory
4. **Bezpečnosť**: Žiadne API kľúče pre indikátory
5. **Presnosť**: Kontrola nad výpočtami

Takže **nie, indikátory sa neberú z internetu** - len základné cenové dáta sa sťahujú a potom sa všetko počíta lokálne vo vašom kóde!