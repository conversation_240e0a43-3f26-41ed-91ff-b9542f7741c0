import { ApiError } from '../utils/errors';
import { appConfig } from '../utils/config';
import { MCPTradingClient } from '../utils/mcp-client';

class DataFetcher {
    private mcpClient: MCPTradingClient;
    private useMCP: boolean = true;
    private mcpAvailable: boolean | null = null;

    constructor() {
        this.mcpClient = new MCPTradingClient();
        console.log('📊 DataFetcher inicializovaný s MCP podporou');
    }

    /**
     * Hlavná metóda na získanie trhových dát s MCP fallback
     */
    async fetchMarketData(symbol: string, startDate: string, endDate: string): Promise<any> {
        // Najprv skúsime MCP ak je dostupný
        if (this.useMCP && this.mcpAvailable !== false) {
            try {
                console.log(`🔄 Pokúšam sa získať dáta pre ${symbol} cez MCP...`);
                const mcpData = await this.fetchRealTimeData(symbol);
                this.mcpAvailable = true;
                return this.formatMCPData(mcpData, startDate, endDate);
            } catch (error) {
                console.log(`⚠️ MCP nedostupný pre ${symbol}, prepínam na API fallback`);
                this.mcpAvailable = false;
                this.useMCP = false;
            }
        }

        // Fallback na pôvodné API
        return this.fetchMarketDataAPI(symbol, startDate, endDate);
    }

    /**
     * Získanie real-time dát cez MCP
     */
    async fetchRealTimeData(symbol: string): Promise<any> {
        try {
            // Rozpoznanie typu symbolu a volanie príslušnej MCP metódy
            if (this.isForexPair(symbol)) {
                return await this.mcpClient.getForexData(symbol, '1h', '1mo');
            } else if (this.isCryptoPair(symbol)) {
                // Pre krypto môžeme použiť forex metódu alebo vytvoriť špecializovanú
                return await this.mcpClient.getForexData(symbol, '1h', '1mo');
            } else {
                // Pre akcie
                return await this.mcpClient.getStockData(symbol, '1h', '1mo');
            }
        } catch (error) {
            console.error(`❌ Chyba pri získavaní MCP dát pre ${symbol}:`, error);
            throw error;
        }
    }

    /**
     * Pôvodná API metóda ako fallback
     */
    async fetchMarketDataAPI(symbol: string, startDate: string, endDate: string): Promise<any> {
        const url = `${appConfig.apiUrl}?access_key=${appConfig.accessKey}&start_date=${startDate}&end_date=${endDate}&symbols=${symbol}&base=USD`;
        try {
            console.log(`🌐 Získavam dáta pre ${symbol} cez API fallback...`);
            const response = await fetch(url);
            if (!response.ok) {
                throw new ApiError(`Network response was not ok: ${response.statusText}`);
            }
            const data = await response.json();
            console.log(`✅ API dáta získané pre ${symbol}`);
            return data.rates;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError(`Error fetching market data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Získanie technických indikátorov cez MCP
     */
    async fetchMarketIndicators(symbol: string, indicators: string[] = ['rsi', 'macd', 'sma'], period: number = 14): Promise<any> {
        if (this.useMCP && this.mcpAvailable !== false) {
            try {
                console.log(`📊 Získavam indikátory pre ${symbol} cez MCP...`);
                const indicatorData = await this.mcpClient.getMarketIndicators(symbol, indicators, period);
                return indicatorData;
            } catch (error) {
                console.error(`❌ Chyba pri získavaní indikátorov pre ${symbol}:`, error);
                // Fallback na mock indikátory
                return this.generateMockIndicators(symbol, indicators);
            }
        }

        // Fallback na mock indikátory
        return this.generateMockIndicators(symbol, indicators);
    }

    /**
     * Test MCP pripojenia
     */
    async testMCPConnection(): Promise<boolean> {
        try {
            const isConnected = await this.mcpClient.testConnection();
            this.mcpAvailable = isConnected;
            this.useMCP = isConnected;
            
            if (isConnected) {
                console.log('✅ MCP pripojenie úspešné - používam real-time dáta');
            } else {
                console.log('❌ MCP pripojenie zlyhalo - používam API fallback');
            }
            
            return isConnected;
        } catch (error) {
            console.error('❌ Test MCP pripojenia zlyhal:', error);
            this.mcpAvailable = false;
            this.useMCP = false;
            return false;
        }
    }

    /**
     * Formátovanie MCP dát do štandardného formátu
     */
    private formatMCPData(mcpData: any, startDate: string, endDate: string): any {
        if (!mcpData || !mcpData.data) {
            throw new Error('Neplatné MCP dáta');
        }

        // Konverzia MCP formátu na štandardný formát
        const formattedData: any = {};
        const start = new Date(startDate);
        const end = new Date(endDate);

        mcpData.data.forEach((item: any) => {
            const itemDate = new Date(item.timestamp);
            if (itemDate >= start && itemDate <= end) {
                const dateKey = itemDate.toISOString().split('T')[0];
                formattedData[dateKey] = {
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.volume || 0
                };
            }
        });

        return formattedData;
    }

    /**
     * Rozpoznanie forex páru
     */
    private isForexPair(symbol: string): boolean {
        const forexPairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'];
        return forexPairs.includes(symbol.toUpperCase());
    }

    /**
     * Rozpoznanie krypto páru
     */
    private isCryptoPair(symbol: string): boolean {
        const cryptoPairs = ['BTCUSD', 'ETHUSD', 'ADAUSD', 'DOTUSD'];
        return cryptoPairs.includes(symbol.toUpperCase());
    }

    /**
     * Generovanie mock indikátorov ako fallback
     */
    private generateMockIndicators(symbol: string, indicators: string[]): any {
        const mockData: any = { symbol, indicators: {} };
        
        indicators.forEach(indicator => {
            switch (indicator) {
                case 'RSI':
                    mockData.indicators.RSI = Math.random() * 100;
                    break;
                case 'MACD':
                    mockData.indicators.MACD = {
                        macd: Math.random() * 0.01,
                        signal: Math.random() * 0.01,
                        histogram: Math.random() * 0.005
                    };
                    break;
                case 'SMA':
                    mockData.indicators.SMA = 1.0850 + (Math.random() - 0.5) * 0.01;
                    break;
                default:
                    mockData.indicators[indicator] = Math.random();
            }
        });

        console.log(`🎲 Generované mock indikátory pre ${symbol}`);
        return mockData;
    }

    /**
     * Získanie stavu MCP pripojenia
     */
    getMCPStatus(): { available: boolean | null; using: boolean } {
        return {
            available: this.mcpAvailable,
            using: this.useMCP
        };
    }

    /**
     * Manuálne prepnutie MCP režimu
     */
    toggleMCP(enabled: boolean): void {
        this.useMCP = enabled;
        console.log(`🔄 MCP režim ${enabled ? 'zapnutý' : 'vypnutý'}`);
    }
}

export default DataFetcher;