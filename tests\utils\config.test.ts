// tests/utils/config.test.ts

import { appConfig } from '../../src/utils/config';

describe('Configuration Service', () => {
    it('should load configuration correctly', () => {
        expect(appConfig).toBeDefined();
        expect(typeof appConfig.initialBalance).toBe('number');
        expect(typeof appConfig.apiUrl).toBe('string');
    });

    it('should have valid values', () => {
        expect(appConfig.initialBalance).toBeGreaterThan(0);
        expect(appConfig.apiUrl.startsWith('http')).toBe(true);
    });
});