# Nastavenie slovenskej komunikácie

## Prehľad

Tento dokument obsahuje inštrukcie na nastavenie slovenskej komunikácie s AI asistentom pri zachovaní anglických konvencií pre kód.

## Rýchle nastavenie

### 1. Použitie prompt súboru

Prompt súbor sa nachádza v [`prompts/slovak-communication-prompt.md`](../prompts/slovak-communication-prompt.md)

**Krátka verzia promptu:**
```
Komunikuj so mnou po slovensky, ale vš<PERSON>ky kódy, skripty a technickú dokumentáciu píš v angličtine. Vysvetlenia a diskusie veď po slovensky, ale dodržiavaj anglické konvencie pre programovanie.
```

### 2. Skript na automatické nastavenie

Vytvorte súbor `scripts/set-slovak-communication.js`:

```javascript
#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up Slovak communication preferences
 * This script helps configure the development environment for Slovak communication
 */

const fs = require('fs');
const path = require('path');

const PROMPT_CONTENT = `
Komunikuj so mnou po slovensky, ale všetky kódy, skripty a technickú dokumentáciu píš v angličtine. 
Vysvetlenia a diskusie veď po slovensky, ale dodržiavaj anglické konvencie pre programovanie.

Pravidlá:
- Komunikácia: Slovenčina
- Kód: Angličtina
- Komentáre v kóde: Angličtina
- Technická dokumentácia: Angličtina
- Vysvetlenia kódu: Slovenčina
`;

function setupSlovakCommunication() {
    console.log('🇸🇰 Nastavujem slovenskú komunikáciu...');
    
    // Create prompts directory if it doesn't exist
    const promptsDir = path.join(__dirname, '..', 'prompts');
    if (!fs.existsSync(promptsDir)) {
        fs.mkdirSync(promptsDir, { recursive: true });
        console.log('✅ Vytvoril som priečinok prompts/');
    }
    
    // Write the prompt file
    const promptFile = path.join(promptsDir, 'current-session-prompt.txt');
    fs.writeFileSync(promptFile, PROMPT_CONTENT.trim());
    console.log('✅ Vytvoril som prompt súbor');
    
    // Display the prompt
    console.log('\n📋 Skopírujte tento prompt do vašej AI konverzácie:');
    console.log('=' .repeat(60));
    console.log(PROMPT_CONTENT.trim());
    console.log('=' .repeat(60));
    
    console.log('\n🎉 Nastavenie dokončené!');
    console.log('💡 Tip: Tento prompt môžete použiť na začiatku každej novej konverzácie');
}

// Run the setup
setupSlovakCommunication();
```

### 3. Spustenie skriptu

```bash
# Vytvorte skript
touch scripts/set-slovak-communication.js

# Skopírujte obsah zo sekcie vyššie

# Spustite skript
node scripts/set-slovak-communication.js
```

## Príklady použitia

### ✅ Správne použitie

**Komunikácia:**
```
Vytvorím novú funkciu na spracovanie obchodných dát. Táto funkcia bude prijímať pole obchodov a vráti ich v štandardizovanom formáte.
```

**Kód:**
```javascript
function processTradeData(trades) {
    return trades.map(trade => ({
        symbol: trade.symbol,
        price: parseFloat(trade.price),
        timestamp: new Date(trade.timestamp),
        volume: trade.volume || 0
    }));
}
```

### ❌ Nesprávne použitie

**Komunikácia v angličtine:**
```
I will create a new function to process trade data...
```

**Kód so slovenskými názvami:**
```javascript
function spracujObchodneData(obchody) {
    return obchody.map(obchod => ({
        symbol: obchod.symbol,
        cena: parseFloat(obchod.cena)
    }));
}
```

## Výhody tohto prístupu

1. **Prirodzená komunikácia** - Diskusia prebieha v slovenčine
2. **Štandardný kód** - Kód zostáva v angličtine pre lepšiu kompatibilitu
3. **Medzinárodná spolupráca** - Kód je zrozumiteľný pre všetkých vývojárov
4. **Dokumentácia** - Technická dokumentácia v angličtine, vysvetlenia v slovenčine

## Dodatočné nastavenia

### Pre VS Code

Vytvorte súbor `.vscode/settings.json`:

```json
{
    "cSpell.language": "en,sk",
    "cSpell.words": [
        "slovenčina",
        "komunikácia",
        "nastavenie"
    ]
}
```

### Pre Git commit správy

Odporúčam používať anglické commit správy pre konzistentnosť:

```bash
# Správne
git commit -m "Add Slovak communication prompt"

# Namiesto
git commit -m "Pridaj slovenský komunikačný prompt"
```

## Záver

Toto nastavenie vám umožní prirodzenú komunikáciu v slovenčine pri zachovaní profesionálnych štandardov pre kód a dokumentáciu.