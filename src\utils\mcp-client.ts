import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

/**
 * MCP Trading Client - wrapper pre komunikáciu s trading-data-server
 * Poskytuje prístup k real-time trading dátam cez MCP protokol
 */
export class MCPTradingClient {
  private client: Client | null = null;
  private transport: StdioClientTransport | null = null;
  private connected = false;
  private connectionAttempts = 0;
  private maxRetries = 3;

  constructor() {
    console.log('🔧 MCPTradingClient inicializovaný');
  }

  /**
   * Pripojenie k MCP trading-data-server
   */
  async connect(): Promise<void> {
    if (this.connected) {
      console.log('✅ MCP client už je pripojený');
      return;
    }

    try {
      console.log('🔌 Pripájam sa k MCP trading-data-server...');
      
      this.transport = new StdioClientTransport({
        command: 'node',
        args: ['D:\\MCP\\trading-data-server\\build\\index.js']
      });
      
      this.client = new Client({
        name: 'trading-agent-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      });

      await this.client.connect(this.transport);
      this.connected = true;
      this.connectionAttempts = 0;
      console.log('✅ MCP Trading Client úspešne pripojený');
      
    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Chyba pri pripojení MCP client (pokus ${this.connectionAttempts}/${this.maxRetries}):`, error);
      
      if (this.connectionAttempts >= this.maxRetries) {
        throw new Error(`MCP pripojenie zlyhalo po ${this.maxRetries} pokusoch`);
      }
      
      // Retry po krátkom čakaní
      await new Promise(resolve => setTimeout(resolve, 1000));
      return this.connect();
    }
  }

  /**
   * Získanie forex dát pre menový pár
   */
  async getForexData(pair: string, interval: string = '1h', period: string = '1mo'): Promise<any> {
    if (!this.connected || !this.client) {
      await this.connect();
    }

    try {
      console.log(`📡 Získavam forex dáta pre ${pair} (${interval}, ${period})`);
      
      // Použitie callTool metódy namiesto request
      const result = await this.client!.callTool({
        name: 'get_forex_data',
        arguments: { pair, interval, period }
      });
      
      // Spracovanie odpovede
      const resultAny = result as any;
      if (resultAny.content && Array.isArray(resultAny.content) && resultAny.content.length > 0) {
        const textContent = resultAny.content.find((c: any) => c.type === 'text');
        if (textContent && 'text' in textContent) {
          const data = JSON.parse(textContent.text);
          console.log(`✅ Forex dáta získané pre ${pair}: ${data.data?.length || 0} záznamov`);
          return data;
        }
      }
      
      throw new Error('Neplatná odpoveď z MCP servera');
      
    } catch (error) {
      console.error(`❌ Chyba pri získavaní forex dát pre ${pair}:`, error);
      throw error;
    }
  }

  /**
   * Získanie akciových dát
   */
  async getStockData(symbol: string, interval: string = '1h', period: string = '1mo'): Promise<any> {
    if (!this.connected || !this.client) {
      await this.connect();
    }

    try {
      console.log(`📈 Získavam akciové dáta pre ${symbol}`);
      
      const result = await this.client!.callTool({
        name: 'get_stock_data',
        arguments: { symbol, interval, period }
      });
      
      const resultAny = result as any;
      if (resultAny.content && Array.isArray(resultAny.content) && resultAny.content.length > 0) {
        const textContent = resultAny.content.find((c: any) => c.type === 'text');
        if (textContent && 'text' in textContent) {
          const data = JSON.parse(textContent.text);
          console.log(`✅ Akciové dáta získané pre ${symbol}`);
          return data;
        }
      }
      
      throw new Error('Neplatná odpoveď z MCP servera');
      
    } catch (error) {
      console.error(`❌ Chyba pri získavaní akciových dát pre ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Získanie technických indikátorov
   */
  async getMarketIndicators(symbol: string, indicators: string[], period: number = 14): Promise<any> {
    if (!this.connected || !this.client) {
      await this.connect();
    }

    try {
      // Konverzia indikátorov na lowercase formát požadovaný serverom
      const lowercaseIndicators = indicators.map(indicator => indicator.toLowerCase());
      console.log(`📊 Vypočítavam indikátory pre ${symbol}: ${lowercaseIndicators.join(', ')}`);
      
      const result = await this.client!.callTool({
        name: 'get_market_indicators',
        arguments: { symbol, indicators: lowercaseIndicators, period }
      });
      
      const resultAny = result as any;
      if (resultAny.content && Array.isArray(resultAny.content) && resultAny.content.length > 0) {
        const textContent = resultAny.content.find((c: any) => c.type === 'text');
        if (textContent && 'text' in textContent) {
          try {
            const data = JSON.parse(textContent.text);
            console.log(`✅ Indikátory vypočítané pre ${symbol}`);
            return data;
          } catch (parseError) {
            console.error(`❌ Chyba pri parsovaní JSON odpovede: ${textContent.text.substring(0, 100)}...`);
            throw new Error(`Neplatná JSON odpoveď z MCP servera: ${parseError}`);
          }
        }
      }
      
      throw new Error('Neplatná odpoveď z MCP servera');
      
    } catch (error) {
      console.error(`❌ Chyba pri výpočte indikátorov pre ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Test pripojenia k MCP serveru
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.connect();
      
      // Test základného volania
      const testResult = await this.getForexData('EURUSD', '1h', '1d');
      console.log('🧪 Test pripojenia úspešný');
      return true;
      
    } catch (error) {
      console.error('🧪 Test pripojenia zlyhal:', error);
      return false;
    }
  }

  /**
   * Odpojenie od MCP servera
   */
  async disconnect(): Promise<void> {
    if (this.transport) {
      try {
        await this.transport.close();
        this.connected = false;
        this.client = null;
        this.transport = null;
        console.log('🔌 MCP Trading Client odpojený');
      } catch (error) {
        console.error('❌ Chyba pri odpojení MCP client:', error);
      }
    }
  }

  /**
   * Získanie stavu pripojenia
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * Získanie štatistík pripojenia
   */
  getConnectionStats(): { connected: boolean; attempts: number; maxRetries: number } {
    return {
      connected: this.connected,
      attempts: this.connectionAttempts,
      maxRetries: this.maxRetries
    };
  }
}

// Singleton instance pre globálne použitie
export const mcpTradingClient = new MCPTradingClient();