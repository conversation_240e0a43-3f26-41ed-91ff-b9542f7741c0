import { log } from '../utils/logger';
import { InsufficientBalanceError, TradeExecutionError } from '../utils/errors';

class Trader {
    private balance: number;

    constructor(initialBalance: number) {
        this.balance = initialBalance;
    }

    buy(amount: number, price: number): void {
        const cost = amount * price;
        if (cost > this.balance) {
            throw new InsufficientBalanceError();
        }
        this.balance -= cost;
        log(`Bought ${amount} units at ${price} each.`);
    }

    sell(amount: number, price: number): void {
        this.balance += amount * price;
        log(`Sold ${amount} units at ${price} each.`);
    }

    executeTrade(amount: number, price: number, action: 'buy' | 'sell'): void {
        try {
            if (action === 'buy') {
                this.buy(amount, price);
            } else if (action === 'sell') {
                this.sell(amount, price);
            } else {
                throw new TradeExecutionError('Invalid trade action.');
            }
        } catch (error) {
            if (error instanceof InsufficientBalanceError) {
                log(`Trade failed: ${error.message}`);
            } else if (error instanceof Error) {
                throw new TradeExecutionError(`Failed to execute trade: ${error.message}`);
            } else {
                throw new TradeExecutionError('An unknown error occurred during trade execution.');
            }
        }
    }

    getBalance(): number {
        return this.balance;
    }
}

export default Trader;