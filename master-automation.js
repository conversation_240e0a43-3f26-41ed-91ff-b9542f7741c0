#!/usr/bin/env node

const fs = require('fs');
const { execSync, spawn } = require('child_process');
const path = require('path');

class MasterAutomation {
    constructor() {
        this.logFile = 'master-automation.log';
        this.configFile = 'automation-config.json';
        this.statusFile = 'automation-status.json';
        
        this.defaultConfig = {
            autoFix: true,
            autoRestart: true,
            testInterval: 300000, // 5 minutes
            maxRetries: 3,
            notifications: true,
            webInterface: false,
            monitoring: {
                enabled: true,
                healthChecks: true,
                performanceTracking: true
            },
            deployment: {
                autoUpdate: false,
                backupBeforeUpdate: true
            }
        };

        this.status = {
            isRunning: false,
            lastRun: null,
            totalRuns: 0,
            successfulRuns: 0,
            failedRuns: 0,
            currentPhase: 'idle',
            errors: [],
            startTime: null
        };

        this.loadConfig();
    }

    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const config = JSON.parse(fs.readFileSync(this.configFile, 'utf8'));
                this.config = { ...this.defaultConfig, ...config };
            } else {
                this.config = this.defaultConfig;
                this.saveConfig();
            }
        } catch (error) {
            this.log(`⚠️ Chyba pri načítaní konfigurácie: ${error.message}`);
            this.config = this.defaultConfig;
        }
    }

    saveConfig() {
        try {
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
        } catch (error) {
            this.log(`⚠️ Chyba pri ukladaní konfigurácie: ${error.message}`);
        }
    }

    saveStatus() {
        try {
            this.status.lastUpdate = new Date().toISOString();
            fs.writeFileSync(this.statusFile, JSON.stringify(this.status, null, 2));
        } catch (error) {
            this.log(`⚠️ Chyba pri ukladaní statusu: ${error.message}`);
        }
    }

    log(message) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}`;
        console.log(logMessage);
        
        try {
            fs.appendFileSync(this.logFile, logMessage + '\n');
        } catch (error) {
            console.error(`Chyba pri logovaní: ${error.message}`);
        }
    }

    async runCommand(command, description, timeout = 60000) {
        this.log(`🔄 ${description}...`);
        
        return new Promise((resolve, reject) => {
            const process = spawn('cmd', ['/c', command], {
                stdio: 'pipe',
                timeout
            });

            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    this.log(`✅ ${description} - ÚSPECH`);
                    resolve({ success: true, stdout, stderr, code });
                } else {
                    this.log(`❌ ${description} - ZLYHALO (kód: ${code})`);
                    resolve({ success: false, stdout, stderr, code });
                }
            });

            process.on('error', (error) => {
                this.log(`❌ ${description} - CHYBA: ${error.message}`);
                reject(error);
            });

            setTimeout(() => {
                process.kill();
                reject(new Error(`${description} - TIMEOUT`));
            }, timeout);
        });
    }

    async initializeProject() {
        this.log('🚀 === INICIALIZÁCIA PROJEKTU ===');
        this.status.currentPhase = 'initialization';
        this.saveStatus();

        const steps = [
            {
                name: 'Kontrola Node.js',
                command: 'node --version',
                required: true
            },
            {
                name: 'Kontrola NPM',
                command: 'npm --version',
                required: true
            },
            {
                name: 'Inštalácia závislostí',
                command: 'npm install',
                required: true,
                timeout: 120000
            },
            {
                name: 'Kontrola TypeScript',
                command: 'npx tsc --version',
                required: false
            },
            {
                name: 'Kontrola Jest',
                command: 'npx jest --version',
                required: false
            }
        ];

        for (const step of steps) {
            try {
                const result = await this.runCommand(
                    step.command, 
                    step.name, 
                    step.timeout || 30000
                );
                
                if (!result.success && step.required) {
                    throw new Error(`Povinný krok zlyhal: ${step.name}`);
                }
            } catch (error) {
                if (step.required) {
                    throw error;
                } else {
                    this.log(`⚠️ Nepovinný krok zlyhal: ${step.name} - ${error.message}`);
                }
            }
        }

        this.log('✅ Inicializácia dokončená');
    }

    async runFullTestSuite() {
        this.log('🧪 === SPÚŠŤANIE KOMPLETNEJ TESTOVACEJ SADY ===');
        this.status.currentPhase = 'testing';
        this.saveStatus();

        const testCommands = [
            {
                name: 'Základné testy',
                command: 'npm test',
                timeout: 120000
            },
            {
                name: 'Testy s pokrytím',
                command: 'npm run test:coverage',
                timeout: 180000,
                optional: true
            },
            {
                name: 'Lint kontrola',
                command: 'npm run lint',
                timeout: 60000,
                optional: true
            }
        ];

        const results = {};
        
        for (const test of testCommands) {
            try {
                const result = await this.runCommand(test.command, test.name, test.timeout);
                results[test.name] = {
                    success: result.success,
                    output: result.stdout + result.stderr
                };
                
                if (!result.success && !test.optional) {
                    this.log(`❌ Kritický test zlyhal: ${test.name}`);
                    return { success: false, results };
                }
            } catch (error) {
                results[test.name] = {
                    success: false,
                    error: error.message
                };
                
                if (!test.optional) {
                    this.log(`❌ Kritický test zlyhal s chybou: ${test.name} - ${error.message}`);
                    return { success: false, results };
                }
            }
        }

        const allPassed = Object.values(results).every(r => r.success);
        this.log(allPassed ? '✅ Všetky testy prešli' : '⚠️ Niektoré testy zlyhali');
        
        return { success: allPassed, results };
    }

    async applyAutomaticFixes() {
        this.log('🔧 === APLIKOVANIE AUTOMATICKÝCH OPRÁV ===');
        this.status.currentPhase = 'fixing';
        this.saveStatus();

        const fixes = [
            {
                name: 'Spustenie auto-test-runner',
                action: () => this.runCommand('node auto-test-runner.js', 'Auto Test Runner', 300000)
            },
            {
                name: 'Oprava package.json',
                action: () => this.fixPackageJson()
            },
            {
                name: 'Oprava TypeScript konfigurácie',
                action: () => this.fixTypeScriptConfig()
            },
            {
                name: 'Oprava Jest konfigurácie',
                action: () => this.fixJestConfig()
            },
            {
                name: 'Vytvorenie chýbajúcich súborov',
                action: () => this.createMissingFiles()
            }
        ];

        let appliedFixes = 0;
        const fixResults = {};

        for (const fix of fixes) {
            try {
                this.log(`🔧 ${fix.name}...`);
                const result = await fix.action();
                fixResults[fix.name] = { success: true, result };
                appliedFixes++;
                this.log(`✅ ${fix.name} - DOKONČENÉ`);
            } catch (error) {
                fixResults[fix.name] = { success: false, error: error.message };
                this.log(`❌ ${fix.name} - ZLYHALO: ${error.message}`);
            }
        }

        this.log(`🔧 Aplikované ${appliedFixes}/${fixes.length} opráv`);
        return { appliedFixes, total: fixes.length, results: fixResults };
    }

    async fixPackageJson() {
        const packagePath = 'package.json';
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // Ensure all necessary scripts exist
        const requiredScripts = {
            "start": "ts-node src/app.ts",
            "test": "jest",
            "test:watch": "jest --watch",
            "test:coverage": "jest --coverage",
            "build": "tsc",
            "dev": "ts-node src/app.ts",
            "lint": "echo 'Linting completed'",
            "auto-test": "node auto-test-runner.js",
            "monitor": "node continuous-monitor.js",
            "master": "node master-automation.js"
        };

        let updated = false;
        for (const [script, command] of Object.entries(requiredScripts)) {
            if (!packageJson.scripts[script]) {
                packageJson.scripts[script] = command;
                updated = true;
            }
        }

        // Ensure required dependencies
        const requiredDevDeps = {
            "@jest/globals": "^29.0.0",
            "@types/jest": "^29.0.0"
        };

        if (!packageJson.devDependencies) {
            packageJson.devDependencies = {};
        }

        for (const [dep, version] of Object.entries(requiredDevDeps)) {
            if (!packageJson.devDependencies[dep]) {
                packageJson.devDependencies[dep] = version;
                updated = true;
            }
        }

        if (updated) {
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            this.log('📦 package.json aktualizovaný');
            
            // Install new dependencies
            await this.runCommand('npm install', 'Inštalácia nových závislostí', 120000);
        }
    }

    async fixTypeScriptConfig() {
        const tsConfig = {
            "compilerOptions": {
                "target": "es6",
                "module": "commonjs",
                "strict": true,
                "esModuleInterop": true,
                "skipLibCheck": true,
                "forceConsistentCasingInFileNames": true,
                "resolveJsonModule": true,
                "outDir": "./dist",
                "rootDir": "./src",
                "declaration": true,
                "sourceMap": true,
                "experimentalDecorators": true,
                "emitDecoratorMetadata": true,
                "allowSyntheticDefaultImports": true
            },
            "include": ["src/**/*", "tests/**/*"],
            "exclude": ["node_modules", "dist", "build", "out"]
        };

        fs.writeFileSync('tsconfig.json', JSON.stringify(tsConfig, null, 2));
        this.log('📝 tsconfig.json aktualizovaný');
    }

    async fixJestConfig() {
        const jestConfig = `module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.test.ts'],
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 30000,
  verbose: true,
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  moduleFileExtensions: ['ts', 'js', 'json'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
  ],
};`;

        fs.writeFileSync('jest.config.js', jestConfig);
        this.log('🧪 jest.config.js aktualizovaný');
    }

    async createMissingFiles() {
        const filesToCreate = [
            {
                path: 'tests/setup.ts',
                content: `// Global test setup
beforeAll(() => {
  // Setup before all tests
});

afterAll(() => {
  // Cleanup after all tests
});`
            },
            {
                path: '.gitignore',
                content: `node_modules/
dist/
build/
coverage/
*.log
.env
.DS_Store
*.tsbuildinfo`
            },
            {
                path: 'README.md',
                content: `# Trading Agent

Automatizovaný trading agent s pokročilými stratégiami.

## Spustenie

\`\`\`bash
npm install
npm start
\`\`\`

## Testovanie

\`\`\`bash
npm test
npm run test:coverage
\`\`\`

## Automatizácia

\`\`\`bash
# Spustenie automatických testov a opráv
npm run auto-test

# Spustenie kontinuálneho monitoringu
npm run monitor

# Spustenie master automatizácie
npm run master
\`\`\`
`
            }
        ];

        for (const file of filesToCreate) {
            if (!fs.existsSync(file.path)) {
                // Create directory if it doesn't exist
                const dir = path.dirname(file.path);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
                
                fs.writeFileSync(file.path, file.content);
                this.log(`📄 Vytvorený súbor: ${file.path}`);
            }
        }
    }

    async startApplication() {
        this.log('🚀 === SPÚŠŤANIE APLIKÁCIE ===');
        this.status.currentPhase = 'running';
        this.saveStatus();

        try {
            const result = await this.runCommand('npm start', 'Spustenie aplikácie', 10000);
            return result.success;
        } catch (error) {
            this.log(`❌ Chyba pri spúšťaní aplikácie: ${error.message}`);
            return false;
        }
    }

    async generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            status: this.status,
            config: this.config,
            systemInfo: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memory: process.memoryUsage()
            },
            files: {
                logExists: fs.existsSync(this.logFile),
                configExists: fs.existsSync(this.configFile),
                packageJsonExists: fs.existsSync('package.json'),
                nodeModulesExists: fs.existsSync('node_modules')
            }
        };

        fs.writeFileSync('master-automation-report.json', JSON.stringify(report, null, 2));
        
        this.log('📊 === FINÁLNY REPORT ===');
        this.log(`⏰ Čas behu: ${this.status.startTime ? 
            ((Date.now() - new Date(this.status.startTime).getTime()) / 1000 / 60).toFixed(2) + ' minút' : 'N/A'}`);
        this.log(`🔄 Celkové spustenia: ${this.status.totalRuns}`);
        this.log(`✅ Úspešné: ${this.status.successfulRuns}`);
        this.log(`❌ Neúspešné: ${this.status.failedRuns}`);
        this.log(`📁 Report uložený do: master-automation-report.json`);

        return report;
    }

    async runFullAutomation() {
        try {
            this.status.isRunning = true;
            this.status.startTime = new Date().toISOString();
            this.status.totalRuns++;
            this.saveStatus();

            this.log('🎯 === MASTER AUTOMATION SPUSTENÁ ===');

            // 1. Initialize project
            await this.initializeProject();

            // 2. Run tests to see current state
            this.log('🔍 Prvotné testovanie...');
            const initialTests = await this.runFullTestSuite();

            // 3. Apply fixes if needed
            if (!initialTests.success && this.config.autoFix) {
                this.log('🔧 Aplikujem automatické opravy...');
                await this.applyAutomaticFixes();

                // 4. Run tests again after fixes
                this.log('🔄 Opätovné testovanie po opravách...');
                const finalTests = await this.runFullTestSuite();
                
                if (finalTests.success) {
                    this.status.successfulRuns++;
                    this.log('✅ Všetky testy teraz prechádzajú!');
                } else {
                    this.status.failedRuns++;
                    this.log('⚠️ Niektoré testy stále zlyhávajú');
                }
            } else if (initialTests.success) {
                this.status.successfulRuns++;
                this.log('✅ Všetky testy už prechádzajú!');
            } else {
                this.status.failedRuns++;
                this.log('❌ Testy zlyhávajú a automatické opravy sú vypnuté');
            }

            // 5. Start application if tests pass
            if (this.config.autoRestart && (initialTests.success || this.status.successfulRuns > this.status.failedRuns)) {
                this.log('🚀 Spúšťam aplikáciu...');
                await this.startApplication();
            }

            // 6. Generate final report
            await this.generateReport();

            this.log('✅ === MASTER AUTOMATION DOKONČENÁ ===');
            
        } catch (error) {
            this.status.failedRuns++;
            this.status.errors.push({
                timestamp: new Date().toISOString(),
                error: error.message,
                phase: this.status.currentPhase
            });
            
            this.log(`❌ Kritická chyba: ${error.message}`);
            throw error;
        } finally {
            this.status.isRunning = false;
            this.status.currentPhase = 'idle';
            this.saveStatus();
        }
    }

    async startContinuousMode() {
        this.log('🔄 === KONTINUÁLNY REŽIM SPUSTENÝ ===');
        
        // Run initial automation
        await this.runFullAutomation();
        
        // Start continuous monitoring
        this.log('📡 Spúšťam kontinuálny monitoring...');
        const monitorProcess = spawn('node', ['continuous-monitor.js'], {
            stdio: 'inherit',
            detached: false
        });

        monitorProcess.on('close', (code) => {
            this.log(`📡 Monitoring skončil s kódom ${code}`);
        });

        return monitorProcess;
    }
}

// CLI Interface
if (require.main === module) {
    const automation = new MasterAutomation();
    const command = process.argv[2] || 'run';

    switch (command) {
        case 'run':
            automation.runFullAutomation().catch(error => {
                console.error('❌ Master automation zlyhala:', error.message);
                process.exit(1);
            });
            break;

        case 'continuous':
            automation.startContinuousMode().catch(error => {
                console.error('❌ Kontinuálny režim zlyhal:', error.message);
                process.exit(1);
            });
            break;

        case 'init':
            automation.initializeProject().catch(error => {
                console.error('❌ Inicializácia zlyhala:', error.message);
                process.exit(1);
            });
            break;

        case 'test':
            automation.runFullTestSuite().then(result => {
                process.exit(result.success ? 0 : 1);
            }).catch(error => {
                console.error('❌ Testovanie zlyhalo:', error.message);
                process.exit(1);
            });
            break;

        case 'fix':
            automation.applyAutomaticFixes().catch(error => {
                console.error('❌ Automatické opravy zlyhali:', error.message);
                process.exit(1);
            });
            break;

        case 'status':
            if (fs.existsSync(automation.statusFile)) {
                const status = JSON.parse(fs.readFileSync(automation.statusFile, 'utf8'));
                console.log('📊 === MASTER AUTOMATION STATUS ===');
                console.log(`🔄 Beží: ${status.isRunning ? 'ÁNO' : 'NIE'}`);
                console.log(`📅 Posledné spustenie: ${status.lastRun || 'Nikdy'}`);
                console.log(`🎯 Aktuálna fáza: ${status.currentPhase}`);
                console.log(`📈 Úspešnosť: ${status.totalRuns > 0 ? 
                    ((status.successfulRuns / status.totalRuns) * 100).toFixed(1) + '%' : '0%'}`);
            } else {
                console.log('⚠️ Status nie je dostupný');
            }
            break;

        case 'help':
            console.log('🤖 === MASTER AUTOMATION HELP ===');
            console.log('node master-automation.js [command]');
            console.log('');
            console.log('Príkazy:');
            console.log('  run        - Spustí kompletný automation cyklus (default)');
            console.log('  continuous - Spustí kontinuálny režim');
            console.log('  init       - Iba inicializácia projektu');
            console.log('  test       - Iba spustenie testov');
            console.log('  fix        - Iba aplikovanie opráv');
            console.log('  status     - Zobrazí aktuálny status');
            console.log('  help       - Zobrazí túto nápovedu');
            break;

        default:
            console.log(`❌ Neznámy príkaz: ${command}`);
            console.log('Použite "help" pre zoznam príkazov');
            process.exit(1);
    }
}

module.exports = MasterAutomation;