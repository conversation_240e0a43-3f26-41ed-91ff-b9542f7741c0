// tests/indicators/technicalIndicators.test.ts

import { TechnicalIndicators } from '../../src/indicators/technicalIndicators';

describe('TechnicalIndicators', () => {
    const samplePrices = [1.2000, 1.2010, 1.2005, 1.2015, 1.2020, 1.2018, 1.2025, 1.2030, 1.2028, 1.2035];
    const sampleHighs = [1.2005, 1.2015, 1.2010, 1.2020, 1.2025, 1.2023, 1.2030, 1.2035, 1.2033, 1.2040];
    const sampleLows = [1.1995, 1.2005, 1.2000, 1.2010, 1.2015, 1.2013, 1.2020, 1.2025, 1.2023, 1.2030];

    describe('Bollinger Bands', () => {
        it('should calculate Bollinger Bands correctly', () => {
            const bb = TechnicalIndicators.calculateBollingerBands(samplePrices);
            
            expect(bb.upper).toBeGreaterThan(bb.middle);
            expect(bb.middle).toBeGreaterThan(bb.lower);
            expect(bb.position).toBeGreaterThanOrEqual(-1);
            expect(bb.position).toBeLessThanOrEqual(1);
        });

        it('should handle insufficient data', () => {
            const bb = TechnicalIndicators.calculateBollingerBands([1.2000]);
            
            expect(bb.upper).toBe(1.2000);
            expect(bb.middle).toBe(1.2000);
            expect(bb.lower).toBe(1.2000);
            expect(bb.position).toBe(0);
        });
    });

    describe('Stochastic RSI', () => {
        it('should calculate Stochastic RSI', () => {
            const stochRSI = TechnicalIndicators.calculateStochasticRSI(samplePrices);
            
            expect(stochRSI.value).toBeGreaterThanOrEqual(0);
            expect(stochRSI.value).toBeLessThanOrEqual(100);
            expect(['buy', 'sell', 'neutral']).toContain(stochRSI.signal);
            expect(stochRSI.strength).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Williams %R', () => {
        it('should calculate Williams %R', () => {
            const williamsR = TechnicalIndicators.calculateWilliamsR(sampleHighs, sampleLows, samplePrices);
            
            expect(williamsR.value).toBeGreaterThanOrEqual(-100);
            expect(williamsR.value).toBeLessThanOrEqual(0);
            expect(['buy', 'sell', 'neutral']).toContain(williamsR.signal);
        });
    });

    describe('ATR', () => {
        it('should calculate ATR', () => {
            const atr = TechnicalIndicators.calculateATR(sampleHighs, sampleLows, samplePrices);
            
            expect(atr).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Trend Detection', () => {
        it('should detect trend correctly', () => {
            const trend = TechnicalIndicators.detectTrend(samplePrices);
            
            expect(['uptrend', 'downtrend', 'sideways']).toContain(trend.trend);
            expect(trend.strength).toBeGreaterThanOrEqual(0);
            expect(trend.strength).toBeLessThanOrEqual(100);
        });
    });

    describe('Support and Resistance', () => {
        it('should find support and resistance levels', () => {
            const sr = TechnicalIndicators.findSupportResistance(samplePrices);
            
            expect(Array.isArray(sr.support)).toBe(true);
            expect(Array.isArray(sr.resistance)).toBe(true);
        });
    });

    describe('Volume Analysis', () => {
        it('should analyze volume correctly', () => {
            const volumes = [1000, 1100, 950, 1200, 1050, 1300, 1150, 1400, 1250, 1500];
            const volumeAnalysis = TechnicalIndicators.analyzeVolume(volumes, samplePrices);
            
            expect(['increasing', 'decreasing', 'stable']).toContain(volumeAnalysis.volumeTrend);
            expect(typeof volumeAnalysis.volumePriceConfirmation).toBe('boolean');
            expect(volumeAnalysis.strength).toBeGreaterThanOrEqual(0);
        });
    });
});