// tests/evolution/framework/StrategyGenome.ts
// Reprezentácia genómu trading stratégie pre evolúciu

import { SmartStrategy } from '../../../src/agent/smartStrategy';
import { OptimizedSmartStrategy } from '../../../src/agent/optimizedSmartStrategy';

export interface StrategyParameters {
    // Základné parametre signálov
    minConfidence: number;              // 60-95
    minSignalStrength: number;          // 50-90
    maxRiskLevel: 'low' | 'medium' | 'high';
    
    // Technické parametre
    trendConfirmationPeriod: number;    // 3-10
    volatilityThreshold: number;        // 0.01-0.05
    
    // Boolean flagy
    volumeConfirmation: boolean;
    multiTimeframeConfirmation: boolean;
    supportResistanceRespect: boolean;
    fibonacciLevels: boolean;
    marketConditionFilter: boolean;
    
    // Risk management
    maxDailyTrades: number;             // 3-15
    maxConsecutiveLosses: number;       // 2-5
    profitTarget: number;               // 1.5-3.0
    stopLoss: number;                   // 0.5-1.5
    
    // Pokročilé parametre
    sessionTimeFilter?: boolean;
    newsFilter?: boolean;
    correlationFilter?: boolean;
    
    // ICT koncepty
    orderBlockDetection?: boolean;
    fairValueGapDetection?: boolean;
    liquidityPoolDetection?: boolean;
    
    // Optimalizačné parametre
    adaptivePeriods?: boolean;
    dynamicRiskAdjustment?: boolean;
    marketRegimeDetection?: boolean;
}

export class StrategyGenome {
    private parameters: StrategyParameters;
    private fitness?: number;
    private age: number = 0;
    private id: string;

    constructor(parameters?: Partial<StrategyParameters>) {
        this.id = this.generateId();
        this.parameters = this.initializeParameters(parameters);
    }

    /**
     * Inicializuje parametre s defaultnými hodnotami
     */
    private initializeParameters(params?: Partial<StrategyParameters>): StrategyParameters {
        return {
            // Základné parametre
            minConfidence: params?.minConfidence ?? 75,
            minSignalStrength: params?.minSignalStrength ?? 70,
            maxRiskLevel: params?.maxRiskLevel ?? 'medium',
            
            // Technické parametre
            trendConfirmationPeriod: params?.trendConfirmationPeriod ?? 5,
            volatilityThreshold: params?.volatilityThreshold ?? 0.02,
            
            // Boolean flagy
            volumeConfirmation: params?.volumeConfirmation ?? true,
            multiTimeframeConfirmation: params?.multiTimeframeConfirmation ?? true,
            supportResistanceRespect: params?.supportResistanceRespect ?? true,
            fibonacciLevels: params?.fibonacciLevels ?? true,
            marketConditionFilter: params?.marketConditionFilter ?? true,
            
            // Risk management
            maxDailyTrades: params?.maxDailyTrades ?? 5,
            maxConsecutiveLosses: params?.maxConsecutiveLosses ?? 2,
            profitTarget: params?.profitTarget ?? 2.0,
            stopLoss: params?.stopLoss ?? 1.0,
            
            // Pokročilé parametre
            sessionTimeFilter: params?.sessionTimeFilter ?? false,
            newsFilter: params?.newsFilter ?? false,
            correlationFilter: params?.correlationFilter ?? false,
            
            // ICT koncepty
            orderBlockDetection: params?.orderBlockDetection ?? false,
            fairValueGapDetection: params?.fairValueGapDetection ?? false,
            liquidityPoolDetection: params?.liquidityPoolDetection ?? false,
            
            // Optimalizačné parametre
            adaptivePeriods: params?.adaptivePeriods ?? false,
            dynamicRiskAdjustment: params?.dynamicRiskAdjustment ?? false,
            marketRegimeDetection: params?.marketRegimeDetection ?? false
        };
    }

    /**
     * Vytvorí genóm z existujúcej stratégie
     */
    static fromStrategy(strategy: SmartStrategy | OptimizedSmartStrategy): StrategyGenome {
        // Extrahuj parametre zo stratégie
        const params: Partial<StrategyParameters> = {};
        
        if (strategy instanceof SmartStrategy) {
            // Základné parametre SmartStrategy
            params.minConfidence = 75;
            params.minSignalStrength = 70;
            params.maxRiskLevel = 'medium';
            params.trendConfirmationPeriod = 5;
            params.volatilityThreshold = 0.02;
        } else if (strategy instanceof OptimizedSmartStrategy) {
            // Optimalizované parametre
            params.minConfidence = 85;
            params.minSignalStrength = 80;
            params.maxRiskLevel = 'low';
            params.trendConfirmationPeriod = 3;
            params.volatilityThreshold = 0.015;
            params.maxDailyTrades = 3;
            params.maxConsecutiveLosses = 2;
        }
        
        return new StrategyGenome(params);
    }

    /**
     * Vytvorí trading stratégiu z genómu
     */
    createStrategy(): SmartStrategy {
        // Pre teraz vytvoríme SmartStrategy s parametrami z genómu
        // V budúcnosti môžeme vytvoriť dynamickú stratégiu
        const strategy = new SmartStrategy();
        
        // Aplikuj parametre na stratégiu (ak má setter metódy)
        // strategy.setMinConfidence(this.parameters.minConfidence);
        // strategy.setMinSignalStrength(this.parameters.minSignalStrength);
        
        return strategy;
    }

    /**
     * Klonuje genóm
     */
    clone(): StrategyGenome {
        const cloned = new StrategyGenome(this.parameters);
        cloned.fitness = this.fitness;
        cloned.age = this.age;
        return cloned;
    }

    /**
     * Mutuje parametre genómu
     */
    mutate(mutationRate: number): StrategyGenome {
        const mutated = this.clone();
        const params = { ...mutated.parameters };
        
        // Mutuj numerické parametre
        if (Math.random() < mutationRate) {
            params.minConfidence = this.mutateNumeric(params.minConfidence, 60, 95, 5);
        }
        
        if (Math.random() < mutationRate) {
            params.minSignalStrength = this.mutateNumeric(params.minSignalStrength, 50, 90, 5);
        }
        
        if (Math.random() < mutationRate) {
            params.trendConfirmationPeriod = Math.round(this.mutateNumeric(params.trendConfirmationPeriod, 3, 10, 1));
        }
        
        if (Math.random() < mutationRate) {
            params.volatilityThreshold = this.mutateNumeric(params.volatilityThreshold, 0.01, 0.05, 0.005);
        }
        
        if (Math.random() < mutationRate) {
            params.maxDailyTrades = Math.round(this.mutateNumeric(params.maxDailyTrades, 3, 15, 2));
        }
        
        if (Math.random() < mutationRate) {
            params.maxConsecutiveLosses = Math.round(this.mutateNumeric(params.maxConsecutiveLosses, 2, 5, 1));
        }
        
        if (Math.random() < mutationRate) {
            params.profitTarget = this.mutateNumeric(params.profitTarget, 1.5, 3.0, 0.2);
        }
        
        if (Math.random() < mutationRate) {
            params.stopLoss = this.mutateNumeric(params.stopLoss, 0.5, 1.5, 0.1);
        }
        
        // Mutuj kategorické parametre
        if (Math.random() < mutationRate) {
            const riskLevels: ('low' | 'medium' | 'high')[] = ['low', 'medium', 'high'];
            params.maxRiskLevel = riskLevels[Math.floor(Math.random() * riskLevels.length)];
        }
        
        // Mutuj boolean parametre
        const booleanParams: (keyof StrategyParameters)[] = [
            'volumeConfirmation', 'multiTimeframeConfirmation', 'supportResistanceRespect',
            'fibonacciLevels', 'marketConditionFilter', 'sessionTimeFilter', 'newsFilter',
            'correlationFilter', 'orderBlockDetection', 'fairValueGapDetection',
            'liquidityPoolDetection', 'adaptivePeriods', 'dynamicRiskAdjustment',
            'marketRegimeDetection'
        ];
        
        booleanParams.forEach(param => {
            if (Math.random() < mutationRate) {
                (params as any)[param] = Math.random() > 0.5;
            }
        });
        
        mutated.parameters = params;
        mutated.age++;
        
        return mutated;
    }

    /**
     * Mutuje numerický parameter
     */
    private mutateNumeric(value: number, min: number, max: number, stdDev: number): number {
        const mutation = this.gaussianRandom() * stdDev;
        const newValue = value + mutation;
        return Math.max(min, Math.min(max, newValue));
    }

    /**
     * Generuje náhodné číslo s normálnym rozdelením
     */
    private gaussianRandom(): number {
        let u = 0, v = 0;
        while(u === 0) u = Math.random(); // Converting [0,1) to (0,1)
        while(v === 0) v = Math.random();
        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    }

    /**
     * Kríženie s iným genómom
     */
    crossover(other: StrategyGenome): [StrategyGenome, StrategyGenome] {
        const child1Params = { ...this.parameters };
        const child2Params = { ...other.parameters };
        
        // Uniform crossover pre numerické parametre
        const numericParams: (keyof StrategyParameters)[] = [
            'minConfidence', 'minSignalStrength', 'trendConfirmationPeriod',
            'volatilityThreshold', 'maxDailyTrades', 'maxConsecutiveLosses',
            'profitTarget', 'stopLoss'
        ];
        
        numericParams.forEach(param => {
            if (Math.random() < 0.5) {
                (child1Params as any)[param] = (other.parameters as any)[param];
                (child2Params as any)[param] = (this.parameters as any)[param];
            }
        });
        
        // Single-point crossover pre boolean parametre
        const booleanParams: (keyof StrategyParameters)[] = [
            'volumeConfirmation', 'multiTimeframeConfirmation', 'supportResistanceRespect',
            'fibonacciLevels', 'marketConditionFilter'
        ];
        
        const crossoverPoint = Math.floor(Math.random() * booleanParams.length);
        
        booleanParams.forEach((param, index) => {
            if (index >= crossoverPoint) {
                (child1Params as any)[param] = (other.parameters as any)[param];
                (child2Params as any)[param] = (this.parameters as any)[param];
            }
        });
        
        return [
            new StrategyGenome(child1Params),
            new StrategyGenome(child2Params)
        ];
    }

    /**
     * Vypočíta vzdialenosť medzi genómami
     */
    distance(other: StrategyGenome): number {
        let distance = 0;
        
        // Numerické parametre
        distance += Math.abs(this.parameters.minConfidence - other.parameters.minConfidence) / 95;
        distance += Math.abs(this.parameters.minSignalStrength - other.parameters.minSignalStrength) / 90;
        distance += Math.abs(this.parameters.trendConfirmationPeriod - other.parameters.trendConfirmationPeriod) / 10;
        distance += Math.abs(this.parameters.volatilityThreshold - other.parameters.volatilityThreshold) / 0.05;
        
        // Boolean parametre
        const booleanDiff = [
            this.parameters.volumeConfirmation !== other.parameters.volumeConfirmation,
            this.parameters.multiTimeframeConfirmation !== other.parameters.multiTimeframeConfirmation,
            this.parameters.supportResistanceRespect !== other.parameters.supportResistanceRespect,
            this.parameters.fibonacciLevels !== other.parameters.fibonacciLevels,
            this.parameters.marketConditionFilter !== other.parameters.marketConditionFilter
        ].filter(diff => diff).length;
        
        distance += booleanDiff / 5;
        
        return distance;
    }

    // Getters a Setters
    getParameters(): StrategyParameters {
        return { ...this.parameters };
    }

    setParameters(parameters: Partial<StrategyParameters>): void {
        this.parameters = { ...this.parameters, ...parameters };
    }

    getFitness(): number | undefined {
        return this.fitness;
    }

    setFitness(fitness: number): void {
        this.fitness = fitness;
    }

    getAge(): number {
        return this.age;
    }

    getId(): string {
        return this.id;
    }

    private generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    /**
     * Serializácia do JSON
     */
    toJSON(): any {
        return {
            id: this.id,
            parameters: this.parameters,
            fitness: this.fitness,
            age: this.age
        };
    }

    /**
     * Deserializácia z JSON
     */
    static fromJSON(json: any): StrategyGenome {
        const genome = new StrategyGenome(json.parameters);
        genome.fitness = json.fitness;
        genome.age = json.age;
        genome.id = json.id;
        return genome;
    }
}
