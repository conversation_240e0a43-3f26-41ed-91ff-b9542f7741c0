{"name": "trading-agent", "version": "1.0.0", "description": "A trading agent for the stock market.", "main": "src/app.ts", "scripts": {"start": "ts-node src/app.ts", "test": "jest", "test:optimized": "jest tests/agent/optimizedSmartStrategy.test.ts", "demo:optimization": "ts-node src/demo/strategyOptimizationDemo.ts", "compare:strategies": "ts-node -e \"import('./src/analysis/strategyComparison').then(m => m.StrategyComparison.compareStrategies().then(r => m.StrategyComparison.printComparisonReport(r)))\"", "mcp:install": "cd E:\\bot\\forex agent\\MCP\\trading-data-server && npm install", "mcp:build": "cd E:\\bot\\forex agent\\MCP\\trading-data-server && npm run build", "mcp:start": "cd E:\\bot\\forex agent\\MCP\\trading-data-server && npm start", "mcp:task-runner": "ts-node scripts/mcp-task-runner.ts", "mcp:task-build": "ts-node scripts/mcp-task-runner.ts build", "mcp:task-test": "ts-node scripts/mcp-task-runner.ts test", "mcp:task-integration": "ts-node scripts/mcp-task-runner.ts integration", "mcp:task-superagent": "ts-node scripts/mcp-task-runner.ts superagent", "mcp:task-full": "ts-node scripts/mcp-task-runner.ts full", "mcp:demo": "ts-node scripts/mcp-demo.ts", "test:mcp": "jest tests/integration/mcp-integration.test.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "tsc", "dev": "ts-node src/app.ts", "lint": "echo '<PERSON><PERSON> completed'", "auto-test": "node auto-test-runner.js", "monitor": "node continuous-monitor.js", "master": "node master-automation.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "axios": "^0.21.1", "technicalindicators": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^4.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^26.0.24", "@types/node": "^14.18.63", "jest": "^26.6.0", "ts-jest": "^26.5.6", "@jest/globals": "^29.0.0"}, "author": "Your Name", "license": "MIT"}