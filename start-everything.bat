@echo off
echo ========================================
echo    TRADING AGENT - AUTOMATICKY START
echo ========================================
echo.

echo [1/4] Kontrolujem Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Node.js nie je nainstalovany!
    echo Prosim nainstalujte Node.js z https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js je dostupny

echo.
echo [2/4] Kontrolujem NPM...
npm --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: NPM nie je dostupny!
    pause
    exit /b 1
)
echo ✓ NPM je dostupny

echo.
echo [3/4] Instalujem zavislosti...
if not exist node_modules (
    echo Instalujem zavislosti...
    npm install
    if errorlevel 1 (
        echo CHYBA: Nepodarilo sa nainstalovat zavislosti!
        pause
        exit /b 1
    )
) else (
    echo ✓ Zavislosti uz su nainstalovane
)

echo.
echo [4/4] Spustam master automation...
echo ========================================
echo.

node master-automation.js run

echo.
echo ========================================
echo Stlacte lubovolnu klavesu pre ukoncenie...
pause >nul