import Trader from './agent/trader';
import { DataProcessor } from './data/processor';
import { log } from './utils/logger';
import { SmartStrategy } from './agent/smartStrategy';
import { AppError } from './utils/errors';
import { appConfig } from './utils/config';
import { testScenarios } from './data/enhancedMockData';
import { RiskManager } from './risk/riskManager';
import { conservativeRiskConfig } from './risk/riskConfig';

const initializeTradingAgent = async () => {
    try {
        log('🚀 Inicializácia SMART Trading Agent pre 80% Win Rate...');

        // Testuj na rôznych scenároch
        const scenarios = [
            { name: 'Optimáln<PERSON> podmienky', data: testScenarios.optimalConditions },
            { name: 'Silný rastúci trend', data: testScenarios.strongUptrend },
            { name: 'Silný klesajúci trend', data: testScenarios.strongDowntrend },
            { name: '<PERSON><PERSON><PERSON><PERSON><PERSON> podmienky', data: testScenarios.mixedConditions }
        ];

        let overallTotalTrades = 0;
        let overallTotalWins = 0;
        let overallTotalPnL = 0;
        const scenarioResults: any[] = [];

        for (const scenario of scenarios) {
            log(`\n🎯 === TESTOVANIE SCENÁRA: ${scenario.name.toUpperCase()} ===`);
            
            // Reset pre nový scenár
            const trader = new Trader(appConfig.initialBalance);
            const dataProcessor = new DataProcessor();
            const smartStrategy = new SmartStrategy();
            const riskManager = new RiskManager(appConfig.initialBalance, conservativeRiskConfig);
            
            log(`💰 Počiatočný zostatok: ${appConfig.initialBalance}`);
            log(`🧠 Stratégia: Smart Strategy (cieľ 80% win rate)`);
            log(`⚙️ Risk Management: Conservative profil`);
            log(`📊 Max riziko na obchod: ${conservativeRiskConfig.maxRiskPerTrade}%`);
            log(`🛡️ Max denná strata: ${conservativeRiskConfig.maxDailyLoss}%`);
            
            log('📈 Spracovávam trhové dáta...');
            const processedData = dataProcessor.processData(scenario.data);

            // Simulácia cien pre risk management
            const currentPrices: { [symbol: string]: number } = {};
            let scenarioTrades = 0;
            let scenarioWins = 0;

            for (let i = 0; i < processedData.length; i++) {
                const dataPoint = processedData[i];
                currentPrices['EURUSD'] = dataPoint.price;

                // Aktualizuj pozície a skontroluj stop loss/take profit
                riskManager.updatePositions(currentPrices);

                // Skontroluj, či môžeme obchodovať
                if (!riskManager.canTrade()) {
                    log('🚫 Obchodovanie pozastavené kvôli risk management pravidlám');
                    break;
                }

                // Rozhoduj pomocou Smart Strategy
                const order = smartStrategy.decideAction(dataPoint);

                if (order) {
                    const entryPrice = dataPoint.price;
                    const stopLoss = riskManager.calculateStopLoss(entryPrice, order.action);
                    const takeProfit = riskManager.calculateTakeProfit(entryPrice, order.action);

                    // Validuj obchod
                    if (riskManager.validateTrade(entryPrice, stopLoss, takeProfit, order.action)) {
                        // Vypočítaj optimálnu veľkosť pozície
                        const optimalSize = riskManager.calculatePositionSize(entryPrice, stopLoss);
                        const finalAmount = Math.min(order.amount, optimalSize);

                        if (finalAmount > 0) {
                            log(`✅ Vykonávam obchod: ${order.action.toUpperCase()} ${finalAmount} jednotiek za cenu ${entryPrice}`);
                            log(`🛡️ Stop Loss: ${stopLoss.toFixed(4)}, 🎯 Take Profit: ${takeProfit.toFixed(4)}`);
                            
                            // Vykonaj obchod v trader-i
                            trader.executeTrade(finalAmount, entryPrice, order.action);
                            
                            // Pridaj pozíciu do risk manager-a
                            riskManager.addPosition('EURUSD', finalAmount, entryPrice, order.action);
                            scenarioTrades++;
                        } else {
                            log('⚠️ Obchod zamietnutý: Vypočítaná veľkosť pozície je 0');
                        }
                    } else {
                        log('❌ Obchod zamietnutý risk management validáciou');
                    }
                }

                // Zobraz progress každých 20 bodov
                if (i % 20 === 0 && i > 0) {
                    const metrics = riskManager.getRiskMetrics();
                    log(`📊 Progress ${i}/${processedData.length}: P&L: ${metrics.totalPnL.toFixed(2)}, Win Rate: ${metrics.winRate.toFixed(1)}%`);
                }
            }

            // Finálne štatistiky scenára
            log(`\n📊 === VÝSLEDKY SCENÁRA: ${scenario.name.toUpperCase()} ===`);
            const finalMetrics = riskManager.getRiskMetrics();
            const finalBalance = riskManager.getCurrentBalance();
            const totalReturn = ((finalBalance - appConfig.initialBalance) / appConfig.initialBalance) * 100;

            scenarioWins = finalMetrics.winningTrades;
            const scenarioWinRate = finalMetrics.winRate;
            const scenarioPnL = finalMetrics.totalPnL;

            log(`💰 Finálny zostatok: ${finalBalance.toFixed(2)}`);
            log(`📈 Výnos scenára: ${scenarioPnL.toFixed(2)} (${totalReturn.toFixed(2)}%)`);
            log(`📊 Obchody scenára: ${finalMetrics.totalTrades}`);
            log(`✅ Úspešné obchody: ${finalMetrics.winningTrades}`);
            log(`❌ Neúspešné obchody: ${finalMetrics.losingTrades}`);
            log(`🎯 Win Rate scenára: ${scenarioWinRate.toFixed(1)}%`);
            log(`📉 Maximálny Drawdown: ${finalMetrics.currentDrawdown.toFixed(2)}%`);

            // Pridaj do celkových štatistík
            overallTotalTrades += finalMetrics.totalTrades;
            overallTotalWins += finalMetrics.winningTrades;
            overallTotalPnL += scenarioPnL;

            scenarioResults.push({
                name: scenario.name,
                trades: finalMetrics.totalTrades,
                wins: finalMetrics.winningTrades,
                winRate: scenarioWinRate,
                pnl: scenarioPnL,
                return: totalReturn
            });

            const openPositions = riskManager.getOpenPositions();
            if (openPositions.length > 0) {
                log(`⚠️ Zostávajúce otvorené pozície: ${openPositions.length}`);
            }
        }

        // CELKOVÉ FINÁLNE VÝSLEDKY
        log('\n🏆 === CELKOVÉ FINÁLNE VÝSLEDKY SMART STRATEGY ===');
        const overallWinRate = overallTotalTrades > 0 ? (overallTotalWins / overallTotalTrades) * 100 : 0;
        
        log(`📊 Celkový počet obchodov: ${overallTotalTrades}`);
        log(`✅ Celkový počet výhier: ${overallTotalWins}`);
        log(`❌ Celkový počet prehier: ${overallTotalTrades - overallTotalWins}`);
        log(`🎯 CELKOVÝ WIN RATE: ${overallWinRate.toFixed(1)}%`);
        log(`💰 Celkový P&L: ${overallTotalPnL.toFixed(2)}`);

        // Detailné výsledky podľa scenárov
        log('\n📋 === DETAILNÉ VÝSLEDKY PODĽA SCENÁROV ===');
        scenarioResults.forEach(result => {
            log(`${result.name}:`);
            log(`  📊 Obchody: ${result.trades}, Win Rate: ${result.winRate.toFixed(1)}%`);
            log(`  💰 P&L: ${result.pnl.toFixed(2)} (${result.return.toFixed(2)}%)`);
        });

        // Hodnotenie výkonnosti
        log('\n🎖️ === HODNOTENIE VÝKONNOSTI ===');
        if (overallWinRate >= 80) {
            log('🏆 VÝBORNÉ! Dosiahnutý cieľ 80%+ win rate!');
        } else if (overallWinRate >= 70) {
            log('🥈 VEĽMI DOBRÉ! Blízko k cieľu, potrebné ďalšie optimalizácie.');
        } else if (overallWinRate >= 60) {
            log('🥉 DOBRÉ! Slušný výkon, ale potrebné výrazné zlepšenia.');
        } else {
            log('❌ POTREBNÉ ZLEPŠENIA! Win rate je pod očakávaním.');
        }

        log('\n🎉 Smart Trading Agent testovanie dokončené!');

    } catch (error) {
        if (error instanceof AppError) {
            log(`❌ Chyba: ${error.constructor.name}: ${error.message}`);
        } else if (error instanceof Error) {
            log(`❌ Neočakávaná chyba: ${error.message}`);
        } else {
            log('❌ Neznáma chyba.');
        }
    }
};

initializeTradingAgent();