// src/agent/smartStrategy.ts

import { TradingStrategy, TradeOrder } from './strategy';
import { TechnicalIndicators, IndicatorResult } from '../indicators/technicalIndicators';

export interface MarketCondition {
    trend: 'uptrend' | 'downtrend' | 'sideways';
    volatility: 'low' | 'medium' | 'high';
    momentum: 'strong' | 'weak' | 'neutral';
    volume: 'high' | 'low' | 'normal';
}

export interface SignalStrength {
    buy: number;    // 0-100
    sell: number;   // 0-100
    confidence: number; // 0-100
}

export interface SmartAnalysis {
    signals: SignalStrength;
    marketCondition: MarketCondition;
    riskLevel: 'low' | 'medium' | 'high';
    recommendation: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell';
    reasoning: string[];
}

export class SmartStrategy implements TradingStrategy {
    private priceHistory: number[] = [];
    private highHistory: number[] = [];
    private lowHistory: number[] = [];
    private volumeHistory: number[] = [];
    private analysisHistory: SmartAnalysis[] = [];
    
    // Konfigurácia stratégie pre vysoký win rate
    private readonly config = {
        minConfidence: 75,          // Minimálna dôvera pre obchod (75%)
        minSignalStrength: 70,      // Minimálna sila signálu (70%)
        maxRiskLevel: 'medium' as const, // Maximálna úroveň rizika
        trendConfirmationPeriod: 5, // Počet období pre potvrdenie trendu
        volatilityThreshold: 0.02,  // Prah volatility (2%)
        volumeConfirmation: true,   // Vyžadovať potvrdenie objemom
        multiTimeframeConfirmation: true, // Potvrdenie na viacerých timeframe-och
        supportResistanceRespect: true,   // Rešpektovať support/resistance
        fibonacciLevels: true,      // Používať Fibonacci úrovne
        marketConditionFilter: true // Filtrovať podľa trhových podmienok
    };

    decideAction(marketData: any): TradeOrder | null {
        // Aktualizuj históriu dát
        this.updateHistory(marketData);
        
        // Potrebujeme dostatok dát pre analýzu
        if (this.priceHistory.length < 50) {
            return null;
        }

        // Vykonaj kompletnú analýzu
        const analysis = this.performSmartAnalysis();
        this.analysisHistory.push(analysis);
        
        // Loguj analýzu
        this.logAnalysis(analysis, marketData);
        
        // Rozhoduj na základe analýzy
        return this.makeDecision(analysis);
    }

    private updateHistory(marketData: any): void {
        this.priceHistory.push(marketData.price);
        this.highHistory.push(marketData.high || marketData.price * 1.001);
        this.lowHistory.push(marketData.low || marketData.price * 0.999);
        this.volumeHistory.push(marketData.volume || 1000);
        
        // Udržuj len posledných 200 záznamov
        const maxHistory = 200;
        if (this.priceHistory.length > maxHistory) {
            this.priceHistory = this.priceHistory.slice(-maxHistory);
            this.highHistory = this.highHistory.slice(-maxHistory);
            this.lowHistory = this.lowHistory.slice(-maxHistory);
            this.volumeHistory = this.volumeHistory.slice(-maxHistory);
        }
    }

    private performSmartAnalysis(): SmartAnalysis {
        const signals = this.calculateSignalStrength();
        const marketCondition = this.analyzeMarketCondition();
        const riskLevel = this.assessRiskLevel();
        const recommendation = this.generateRecommendation(signals, marketCondition, riskLevel);
        const reasoning = this.generateReasoning(signals, marketCondition, riskLevel);

        return {
            signals,
            marketCondition,
            riskLevel,
            recommendation,
            reasoning
        };
    }

    private calculateSignalStrength(): SignalStrength {
        let buySignals = 0;
        let sellSignals = 0;
        let totalWeight = 0;
        const reasons: string[] = [];

        // 1. Bollinger Bands analýza (váha: 15%)
        const bb = TechnicalIndicators.calculateBollingerBands(this.priceHistory);
        const bbWeight = 15;
        if (bb.position < -0.8) {
            buySignals += bbWeight * 0.8;
            reasons.push(`BB: Cena blízko dolného pásma (${bb.position.toFixed(2)})`);
        } else if (bb.position > 0.8) {
            sellSignals += bbWeight * 0.8;
            reasons.push(`BB: Cena blízko horného pásma (${bb.position.toFixed(2)})`);
        }
        totalWeight += bbWeight;

        // 2. Stochastic RSI (váha: 20%)
        const stochRSI = TechnicalIndicators.calculateStochasticRSI(this.priceHistory);
        const stochWeight = 20;
        if (stochRSI.signal === 'buy' && stochRSI.strength! > 50) {
            buySignals += stochWeight * (stochRSI.strength! / 100);
            reasons.push(`StochRSI: ${stochRSI.signal} signál (${stochRSI.value.toFixed(1)})`);
        } else if (stochRSI.signal === 'sell' && stochRSI.strength! > 50) {
            sellSignals += stochWeight * (stochRSI.strength! / 100);
            reasons.push(`StochRSI: ${stochRSI.signal} signál (${stochRSI.value.toFixed(1)})`);
        }
        totalWeight += stochWeight;

        // 3. Williams %R (váha: 15%)
        const williamsR = TechnicalIndicators.calculateWilliamsR(
            this.highHistory, this.lowHistory, this.priceHistory
        );
        const williamsWeight = 15;
        if (williamsR.signal === 'buy' && williamsR.strength! > 40) {
            buySignals += williamsWeight * (williamsR.strength! / 100);
            reasons.push(`Williams %R: ${williamsR.signal} signál (${williamsR.value.toFixed(1)})`);
        } else if (williamsR.signal === 'sell' && williamsR.strength! > 40) {
            sellSignals += williamsWeight * (williamsR.strength! / 100);
            reasons.push(`Williams %R: ${williamsR.signal} signál (${williamsR.value.toFixed(1)})`);
        }
        totalWeight += williamsWeight;

        // 4. Trend analýza (váha: 25%)
        const trend = TechnicalIndicators.detectTrend(this.priceHistory);
        const trendWeight = 25;
        if (trend.trend === 'uptrend' && trend.strength > 60) {
            buySignals += trendWeight * (trend.strength / 100);
            reasons.push(`Trend: ${trend.trend} (sila: ${trend.strength.toFixed(1)}%)`);
        } else if (trend.trend === 'downtrend' && trend.strength > 60) {
            sellSignals += trendWeight * (trend.strength / 100);
            reasons.push(`Trend: ${trend.trend} (sila: ${trend.strength.toFixed(1)}%)`);
        }
        totalWeight += trendWeight;

        // 5. Support/Resistance analýza (váha: 15%)
        const sr = TechnicalIndicators.findSupportResistance(this.priceHistory);
        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        const srWeight = 15;
        
        // Kontrola blízkosti k support/resistance
        const nearSupport = sr.support.some(level => Math.abs(currentPrice - level) / currentPrice < 0.005);
        const nearResistance = sr.resistance.some(level => Math.abs(currentPrice - level) / currentPrice < 0.005);
        
        if (nearSupport) {
            buySignals += srWeight * 0.7;
            reasons.push('S/R: Blízko support úrovne');
        } else if (nearResistance) {
            sellSignals += srWeight * 0.7;
            reasons.push('S/R: Blízko resistance úrovne');
        }
        totalWeight += srWeight;

        // 6. Volume potvrdenie (váha: 10%)
        if (this.volumeHistory.length >= 10) {
            const volumeAnalysis = TechnicalIndicators.analyzeVolume(this.volumeHistory, this.priceHistory);
            const volumeWeight = 10;
            
            if (volumeAnalysis.volumePriceConfirmation) {
                if (volumeAnalysis.volumeTrend === 'increasing') {
                    const priceChange = this.priceHistory[this.priceHistory.length - 1] - this.priceHistory[this.priceHistory.length - 5];
                    if (priceChange > 0) {
                        buySignals += volumeWeight * 0.8;
                        reasons.push('Volume: Potvrdenie rastúcej ceny');
                    } else {
                        sellSignals += volumeWeight * 0.8;
                        reasons.push('Volume: Potvrdenie klesajúcej ceny');
                    }
                }
            }
            totalWeight += volumeWeight;
        }

        // Normalizuj signály na 0-100 škálu
        const normalizedBuy = Math.min((buySignals / totalWeight) * 100, 100);
        const normalizedSell = Math.min((sellSignals / totalWeight) * 100, 100);
        
        // Vypočítaj celkovú dôveru
        const confidence = Math.max(normalizedBuy, normalizedSell);

        return {
            buy: normalizedBuy,
            sell: normalizedSell,
            confidence
        };
    }

    private analyzeMarketCondition(): MarketCondition {
        const trend = TechnicalIndicators.detectTrend(this.priceHistory);
        const atr = TechnicalIndicators.calculateATR(this.highHistory, this.lowHistory, this.priceHistory);
        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        
        // Volatilita
        const volatilityPercent = (atr / currentPrice) * 100;
        let volatility: 'low' | 'medium' | 'high';
        if (volatilityPercent < 1) volatility = 'low';
        else if (volatilityPercent < 3) volatility = 'medium';
        else volatility = 'high';

        // Momentum
        const recentPrices = this.priceHistory.slice(-10);
        const momentum = (recentPrices[recentPrices.length - 1] - recentPrices[0]) / recentPrices[0] * 100;
        let momentumLevel: 'strong' | 'weak' | 'neutral';
        if (Math.abs(momentum) > 2) momentumLevel = 'strong';
        else if (Math.abs(momentum) > 0.5) momentumLevel = 'weak';
        else momentumLevel = 'neutral';

        // Volume
        let volume: 'high' | 'low' | 'normal' = 'normal';
        if (this.volumeHistory.length >= 20) {
            const recentVolume = this.volumeHistory.slice(-5).reduce((sum, v) => sum + v, 0) / 5;
            const avgVolume = this.volumeHistory.slice(-20).reduce((sum, v) => sum + v, 0) / 20;
            const volumeRatio = recentVolume / avgVolume;
            
            if (volumeRatio > 1.5) volume = 'high';
            else if (volumeRatio < 0.7) volume = 'low';
        }

        return {
            trend: trend.trend,
            volatility,
            momentum: momentumLevel,
            volume
        };
    }

    private assessRiskLevel(): 'low' | 'medium' | 'high' {
        const marketCondition = this.analyzeMarketCondition();
        
        // Vysoká volatilita = vysoké riziko
        if (marketCondition.volatility === 'high') return 'high';
        
        // Sideways trend s vysokým momentum = stredné riziko
        if (marketCondition.trend === 'sideways' && marketCondition.momentum === 'strong') {
            return 'medium';
        }
        
        // Silný trend s potvrdením = nízke riziko
        if ((marketCondition.trend === 'uptrend' || marketCondition.trend === 'downtrend') &&
            marketCondition.momentum === 'strong' && marketCondition.volume === 'high') {
            return 'low';
        }
        
        return 'medium';
    }

    private generateRecommendation(
        signals: SignalStrength, 
        marketCondition: MarketCondition, 
        riskLevel: 'low' | 'medium' | 'high'
    ): 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell' {
        
        // Ak je riziko príliš vysoké, drž
        if (riskLevel === 'high') return 'hold';
        
        // Ak je dôvera nízka, drž
        if (signals.confidence < this.config.minConfidence) return 'hold';
        
        // Silné signály s vysokou dôverou
        if (signals.confidence > 85) {
            if (signals.buy > signals.sell && signals.buy > 80) return 'strong_buy';
            if (signals.sell > signals.buy && signals.sell > 80) return 'strong_sell';
        }
        
        // Stredné signály
        if (signals.confidence > this.config.minConfidence) {
            if (signals.buy > signals.sell && signals.buy > this.config.minSignalStrength) return 'buy';
            if (signals.sell > signals.buy && signals.sell > this.config.minSignalStrength) return 'sell';
        }
        
        return 'hold';
    }

    private generateReasoning(
        signals: SignalStrength, 
        marketCondition: MarketCondition, 
        riskLevel: 'low' | 'medium' | 'high'
    ): string[] {
        const reasoning: string[] = [];
        
        reasoning.push(`Dôvera signálu: ${signals.confidence.toFixed(1)}%`);
        reasoning.push(`Buy sila: ${signals.buy.toFixed(1)}%, Sell sila: ${signals.sell.toFixed(1)}%`);
        reasoning.push(`Trend: ${marketCondition.trend}, Volatilita: ${marketCondition.volatility}`);
        reasoning.push(`Momentum: ${marketCondition.momentum}, Volume: ${marketCondition.volume}`);
        reasoning.push(`Úroveň rizika: ${riskLevel}`);
        
        return reasoning;
    }

    private makeDecision(analysis: SmartAnalysis): TradeOrder | null {
        // Prísne kritériá pre vysoký win rate
        if (analysis.riskLevel === 'high') {
            return null;
        }
        
        if (analysis.signals.confidence < this.config.minConfidence) {
            return null;
        }
        
        // Rozhoduj len pri silných signáloch
        if (analysis.recommendation === 'strong_buy' || analysis.recommendation === 'buy') {
            if (analysis.signals.buy > this.config.minSignalStrength) {
                return { action: 'buy', amount: this.calculatePositionSize(analysis) };
            }
        }
        
        if (analysis.recommendation === 'strong_sell' || analysis.recommendation === 'sell') {
            if (analysis.signals.sell > this.config.minSignalStrength) {
                return { action: 'sell', amount: this.calculatePositionSize(analysis) };
            }
        }
        
        return null;
    }

    private calculatePositionSize(analysis: SmartAnalysis): number {
        // Základná veľkosť
        let baseSize = 10;
        
        // Upravuj veľkosť na základe dôvery
        const confidenceMultiplier = analysis.signals.confidence / 100;
        
        // Upravuj veľkosť na základe rizika
        const riskMultiplier = analysis.riskLevel === 'low' ? 1.2 : 
                              analysis.riskLevel === 'medium' ? 1.0 : 0.8;
        
        // Upravuj veľkosť na základe trhových podmienok
        const marketMultiplier = analysis.marketCondition.volatility === 'low' ? 1.1 : 
                                analysis.marketCondition.volatility === 'medium' ? 1.0 : 0.9;
        
        const finalSize = Math.round(baseSize * confidenceMultiplier * riskMultiplier * marketMultiplier);
        
        return Math.max(finalSize, 5); // Minimálne 5 jednotiek
    }

    private logAnalysis(analysis: SmartAnalysis, marketData: any): void {
        console.log(`🧠 === SMART ANALÝZA ===`);
        console.log(`💹 Cena: ${marketData.price}`);
        console.log(`🎯 Odporúčanie: ${analysis.recommendation.toUpperCase()}`);
        console.log(`📊 Buy: ${analysis.signals.buy.toFixed(1)}%, Sell: ${analysis.signals.sell.toFixed(1)}%`);
        console.log(`🔒 Dôvera: ${analysis.signals.confidence.toFixed(1)}%`);
        console.log(`⚠️ Riziko: ${analysis.riskLevel}`);
        console.log(`📈 Trend: ${analysis.marketCondition.trend}`);
        console.log(`📋 Dôvody:`);
        analysis.reasoning.forEach(reason => console.log(`   • ${reason}`));
    }

    // Getter pre štatistiky
    getAnalysisHistory(): SmartAnalysis[] {
        return [...this.analysisHistory];
    }

    getWinRateStats(): {
        totalSignals: number;
        correctPredictions: number;
        winRate: number;
    } {
        // Toto by sa implementovalo s backtesting dátami
        return {
            totalSignals: this.analysisHistory.length,
            correctPredictions: 0,
            winRate: 0
        };
    }
}