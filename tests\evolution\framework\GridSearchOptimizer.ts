// tests/evolution/framework/GridSearchOptimizer.ts
// Grid search optimalizácia pre trading stratégie

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface GridSearchResult {
    bestParameters: any;
    bestFitness: FitnessResult;
    allResults: { parameters: any; fitness: FitnessResult }[];
    searchSpace: any;
    totalCombinations: number;
    executionTime: number;
}

export class GridSearchOptimizer {
    /**
     * Vykonaj grid search optimalizáciu
     */
    async optimize(
        parameterSpace: { [key: string]: any[] },
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<GridSearchResult> {
        const startTime = Date.now();
        
        // Vygeneruj všetky kombinácie parametrov
        const combinations = this.generateAllCombinations(parameterSpace);
        console.log(`🔍 Grid Search: Testing ${combinations.length} parameter combinations...`);
        
        const results: { parameters: any; fitness: FitnessResult }[] = [];
        let bestResult: { parameters: any; fitness: FitnessResult } | null = null;
        
        // Testuj každú kombináciu
        for (let i = 0; i < combinations.length; i++) {
            const params = combinations[i];
            
            if (i % 10 === 0) {
                console.log(`  Progress: ${i}/${combinations.length} (${((i/combinations.length)*100).toFixed(1)}%)`);
            }
            
            try {
                const genome = new StrategyGenome(params);
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                
                const result = { parameters: params, fitness };
                results.push(result);
                
                // Aktualizuj najlepší výsledok
                if (!bestResult || fitness.overallScore > bestResult.fitness.overallScore) {
                    bestResult = result;
                }
            } catch (error) {
                console.warn(`Error evaluating parameters:`, params, error);
                // Pridaj neplatný výsledok
                results.push({
                    parameters: params,
                    fitness: this.getInvalidFitness()
                });
            }
        }
        
        const executionTime = Date.now() - startTime;
        console.log(`✅ Grid Search completed in ${executionTime}ms`);
        
        if (!bestResult) {
            throw new Error('No valid parameter combinations found');
        }
        
        return {
            bestParameters: bestResult.parameters,
            bestFitness: bestResult.fitness,
            allResults: results,
            searchSpace: parameterSpace,
            totalCombinations: combinations.length,
            executionTime
        };
    }

    /**
     * Vykonaj grid search s validáciou
     */
    async optimizeWithValidation(
        parameterSpace: { [key: string]: any[] },
        trainData: EnhancedMarketData[],
        validationData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<GridSearchResult & { validationResults: any[] }> {
        // Najprv optimalizuj na trénovacích dátach
        const trainResult = await this.optimize(parameterSpace, trainData, initialBalance, fitnessEvaluator);
        
        // Potom validuj najlepších N kandidátov na validačných dátach
        const topCandidates = trainResult.allResults
            .sort((a, b) => b.fitness.overallScore - a.fitness.overallScore)
            .slice(0, Math.min(10, trainResult.allResults.length)); // Top 10 kandidátov
        
        console.log(`🔬 Validating top ${topCandidates.length} candidates...`);
        
        const validationResults = [];
        let bestValidationResult = null;
        
        for (const candidate of topCandidates) {
            const genome = new StrategyGenome(candidate.parameters);
            const validationFitness = await fitnessEvaluator.evaluateFitness(
                genome, 
                validationData, 
                initialBalance
            );
            
            const validationResult = {
                parameters: candidate.parameters,
                trainFitness: candidate.fitness,
                validationFitness,
                generalizationGap: candidate.fitness.overallScore - validationFitness.overallScore
            };
            
            validationResults.push(validationResult);
            
            if (!bestValidationResult || 
                validationFitness.overallScore > bestValidationResult.validationFitness.overallScore) {
                bestValidationResult = validationResult;
            }
        }
        
        return {
            ...trainResult,
            bestParameters: bestValidationResult!.parameters,
            bestFitness: bestValidationResult!.validationFitness,
            validationResults
        };
    }

    /**
     * Hierarchický grid search - najprv hrubý, potom jemný
     */
    async hierarchicalOptimize(
        parameterSpace: { [key: string]: { min: number; max: number; steps: number } },
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        refinementLevels: number = 2
    ): Promise<GridSearchResult> {
        let currentSpace = parameterSpace;
        let bestResult = null;
        
        for (let level = 0; level < refinementLevels; level++) {
            console.log(`🔍 Hierarchical Grid Search - Level ${level + 1}/${refinementLevels}`);
            
            // Konvertuj na diskrétny priestor
            const discreteSpace = this.convertToDiscreteSpace(currentSpace);
            
            // Vykonaj grid search
            const result = await this.optimize(discreteSpace, marketData, initialBalance, fitnessEvaluator);
            
            if (!bestResult || result.bestFitness.overallScore > bestResult.bestFitness.overallScore) {
                bestResult = result;
            }
            
            // Pre ďalší level, zúž priestor okolo najlepšieho výsledku
            if (level < refinementLevels - 1) {
                currentSpace = this.refineSearchSpace(currentSpace, result.bestParameters);
            }
        }
        
        return bestResult!;
    }

    /**
     * Adaptívny grid search - pridáva body tam, kde je vysoká variabilita
     */
    async adaptiveGridSearch(
        parameterSpace: { [key: string]: any[] },
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        maxIterations: number = 100
    ): Promise<GridSearchResult> {
        const startTime = Date.now();
        
        // Začni s počiatočnou mriežkou
        let currentSpace = parameterSpace;
        const allResults: { parameters: any; fitness: FitnessResult }[] = [];
        let bestResult = null;
        
        for (let iteration = 0; iteration < maxIterations; iteration++) {
            console.log(`🔄 Adaptive Grid Search - Iteration ${iteration + 1}/${maxIterations}`);
            
            // Vygeneruj kombinácie pre aktuálny priestor
            const combinations = this.generateAllCombinations(currentSpace);
            
            // Testuj len nové kombinácie
            const newCombinations = combinations.filter(combo => 
                !allResults.some(result => this.parametersEqual(result.parameters, combo))
            );
            
            if (newCombinations.length === 0) {
                console.log('No new combinations to test, stopping early');
                break;
            }
            
            // Testuj nové kombinácie
            for (const params of newCombinations) {
                const genome = new StrategyGenome(params);
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                
                const result = { parameters: params, fitness };
                allResults.push(result);
                
                if (!bestResult || fitness.overallScore > bestResult.fitness.overallScore) {
                    bestResult = result;
                }
            }
            
            // Adaptuj priestor na základe výsledkov
            currentSpace = this.adaptSearchSpace(allResults, parameterSpace);
        }
        
        const executionTime = Date.now() - startTime;
        
        return {
            bestParameters: bestResult!.parameters,
            bestFitness: bestResult!.fitness,
            allResults,
            searchSpace: parameterSpace,
            totalCombinations: allResults.length,
            executionTime
        };
    }

    /**
     * Vygeneruj všetky kombinácie parametrov
     */
    private generateAllCombinations(parameterSpace: { [key: string]: any[] }): any[] {
        const keys = Object.keys(parameterSpace);
        const combinations: any[] = [];
        
        const generateRecursive = (index: number, current: any) => {
            if (index === keys.length) {
                combinations.push({ ...current });
                return;
            }
            
            const key = keys[index];
            const values = parameterSpace[key];
            
            for (const value of values) {
                current[key] = value;
                generateRecursive(index + 1, current);
            }
        };
        
        generateRecursive(0, {});
        return combinations;
    }

    /**
     * Konvertuj kontinuálny priestor na diskrétny
     */
    private convertToDiscreteSpace(
        parameterSpace: { [key: string]: { min: number; max: number; steps: number } }
    ): { [key: string]: any[] } {
        const discreteSpace: { [key: string]: any[] } = {};
        
        for (const [key, config] of Object.entries(parameterSpace)) {
            const values = [];
            const step = (config.max - config.min) / (config.steps - 1);
            
            for (let i = 0; i < config.steps; i++) {
                values.push(config.min + i * step);
            }
            
            discreteSpace[key] = values;
        }
        
        return discreteSpace;
    }

    /**
     * Zúž priestor okolo najlepšieho výsledku
     */
    private refineSearchSpace(
        originalSpace: { [key: string]: { min: number; max: number; steps: number } },
        bestParameters: any
    ): { [key: string]: { min: number; max: number; steps: number } } {
        const refinedSpace: { [key: string]: { min: number; max: number; steps: number } } = {};
        
        for (const [key, config] of Object.entries(originalSpace)) {
            const bestValue = bestParameters[key];
            const range = config.max - config.min;
            const newRange = range * 0.3; // Zúž na 30% pôvodného rozsahu
            
            refinedSpace[key] = {
                min: Math.max(config.min, bestValue - newRange / 2),
                max: Math.min(config.max, bestValue + newRange / 2),
                steps: config.steps
            };
        }
        
        return refinedSpace;
    }

    /**
     * Adaptuj priestor na základe výsledkov
     */
    private adaptSearchSpace(
        results: { parameters: any; fitness: FitnessResult }[],
        originalSpace: { [key: string]: any[] }
    ): { [key: string]: any[] } {
        // Zjednodušená implementácia - zameraj sa na oblasti s vysokou fitness
        const topResults = results
            .sort((a, b) => b.fitness.overallScore - a.fitness.overallScore)
            .slice(0, Math.min(10, results.length));
        
        const adaptedSpace: { [key: string]: any[] } = {};
        
        for (const key of Object.keys(originalSpace)) {
            const topValues = topResults.map(r => r.parameters[key]);
            const uniqueValues = [...new Set(topValues)];
            
            // Pridaj susedné hodnoty
            const expandedValues = new Set(uniqueValues);
            
            for (const value of uniqueValues) {
                const originalValues = originalSpace[key];
                const index = originalValues.indexOf(value);
                
                if (index > 0) expandedValues.add(originalValues[index - 1]);
                if (index < originalValues.length - 1) expandedValues.add(originalValues[index + 1]);
            }
            
            adaptedSpace[key] = Array.from(expandedValues);
        }
        
        return adaptedSpace;
    }

    /**
     * Porovnaj parametre
     */
    private parametersEqual(params1: any, params2: any): boolean {
        const keys1 = Object.keys(params1);
        const keys2 = Object.keys(params2);
        
        if (keys1.length !== keys2.length) return false;
        
        for (const key of keys1) {
            if (params1[key] !== params2[key]) return false;
        }
        
        return true;
    }

    /**
     * Vráti neplatný fitness výsledok
     */
    private getInvalidFitness(): FitnessResult {
        return {
            winRate: 0,
            profitFactor: 0,
            sharpeRatio: -999,
            maxDrawdown: 1,
            totalTrades: 0,
            totalProfit: -999999,
            averageWin: 0,
            averageLoss: 0,
            consecutiveWins: 0,
            consecutiveLosses: 999,
            profitability: -1,
            volatility: 999,
            calmarRatio: -999,
            sortinoRatio: -999,
            overallScore: -999,
            tradesPerDay: 0,
            winningDays: 0,
            losingDays: 999,
            maxDailyProfit: 0,
            maxDailyLoss: -999999,
            recoveryFactor: -999,
            expectancy: -999
        };
    }
}
