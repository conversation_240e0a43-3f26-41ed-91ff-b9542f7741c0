// src/indicators/technicalIndicators.ts

export interface CandleData {
    open: number;
    high: number;
    low: number;
    close: number;
    volume?: number;
    timestamp?: Date;
}

export interface IndicatorResult {
    value: number;
    signal?: 'buy' | 'sell' | 'neutral';
    strength?: number; // 0-100
}

export class TechnicalIndicators {
    
    // Bollinger Bands
    static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): {
        upper: number;
        middle: number;
        lower: number;
        position: number; // -1 to 1, kde je cena relatívne k pásmam
    } {
        if (prices.length < period) {
            const lastPrice = prices[prices.length - 1] || 0;
            return { upper: lastPrice, middle: lastPrice, lower: lastPrice, position: 0 };
        }

        const recentPrices = prices.slice(-period);
        const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;
        
        const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
        const standardDeviation = Math.sqrt(variance);
        
        const upper = sma + (standardDeviation * stdDev);
        const lower = sma - (standardDeviation * stdDev);
        const currentPrice = prices[prices.length - 1];
        
        // Pozícia ceny v rámci pásiem (-1 = na dolnom pásme, 1 = na hornom pásme)
        const position = (currentPrice - sma) / (standardDeviation * stdDev);
        
        return { upper, middle: sma, lower, position };
    }

    // Stochastic RSI
    static calculateStochasticRSI(prices: number[], rsiPeriod: number = 14, stochPeriod: number = 14): IndicatorResult {
        if (prices.length < rsiPeriod + stochPeriod) {
            return { value: 50, signal: 'neutral', strength: 0 };
        }

        // Najprv vypočítaj RSI
        const rsiValues = this.calculateRSIValues(prices, rsiPeriod);
        
        if (rsiValues.length < stochPeriod) {
            return { value: 50, signal: 'neutral', strength: 0 };
        }

        // Potom aplikuj Stochastic na RSI hodnoty
        const recentRSI = rsiValues.slice(-stochPeriod);
        const highestRSI = Math.max(...recentRSI);
        const lowestRSI = Math.min(...recentRSI);
        const currentRSI = rsiValues[rsiValues.length - 1];
        
        const stochRSI = ((currentRSI - lowestRSI) / (highestRSI - lowestRSI)) * 100;
        
        let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
        let strength = 0;
        
        if (stochRSI < 20) {
            signal = 'buy';
            strength = (20 - stochRSI) * 5; // Čím nižšie, tým silnejší signál
        } else if (stochRSI > 80) {
            signal = 'sell';
            strength = (stochRSI - 80) * 5;
        }
        
        return { value: stochRSI, signal, strength: Math.min(strength, 100) };
    }

    // Williams %R
    static calculateWilliamsR(highs: number[], lows: number[], closes: number[], period: number = 14): IndicatorResult {
        if (highs.length < period || lows.length < period || closes.length < period) {
            return { value: -50, signal: 'neutral', strength: 0 };
        }

        const recentHighs = highs.slice(-period);
        const recentLows = lows.slice(-period);
        const currentClose = closes[closes.length - 1];
        
        const highestHigh = Math.max(...recentHighs);
        const lowestLow = Math.min(...recentLows);
        
        const williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
        
        let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
        let strength = 0;
        
        if (williamsR < -80) {
            signal = 'buy';
            strength = Math.abs(williamsR + 80) * 5;
        } else if (williamsR > -20) {
            signal = 'sell';
            strength = (20 + williamsR) * 5;
        }
        
        return { value: williamsR, signal, strength: Math.min(strength, 100) };
    }

    // ATR (Average True Range)
    static calculateATR(highs: number[], lows: number[], closes: number[], period: number = 14): number {
        if (highs.length < 2 || lows.length < 2 || closes.length < 2) {
            return 0;
        }

        const trueRanges: number[] = [];
        
        for (let i = 1; i < Math.min(highs.length, lows.length, closes.length); i++) {
            const high = highs[i];
            const low = lows[i];
            const prevClose = closes[i - 1];
            
            const tr1 = high - low;
            const tr2 = Math.abs(high - prevClose);
            const tr3 = Math.abs(low - prevClose);
            
            trueRanges.push(Math.max(tr1, tr2, tr3));
        }
        
        if (trueRanges.length < period) {
            return trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length;
        }
        
        const recentTR = trueRanges.slice(-period);
        return recentTR.reduce((sum, tr) => sum + tr, 0) / period;
    }

    // Fibonacci Retracement levels
    static calculateFibonacciLevels(high: number, low: number): {
        level236: number;
        level382: number;
        level500: number;
        level618: number;
        level786: number;
    } {
        const range = high - low;
        
        return {
            level236: high - (range * 0.236),
            level382: high - (range * 0.382),
            level500: high - (range * 0.500),
            level618: high - (range * 0.618),
            level786: high - (range * 0.786)
        };
    }

    // Support and Resistance levels
    static findSupportResistance(prices: number[], period: number = 20): {
        support: number[];
        resistance: number[];
    } {
        if (prices.length < period * 2) {
            return { support: [], resistance: [] };
        }

        const support: number[] = [];
        const resistance: number[] = [];
        
        for (let i = period; i < prices.length - period; i++) {
            const currentPrice = prices[i];
            const leftPrices = prices.slice(i - period, i);
            const rightPrices = prices.slice(i + 1, i + period + 1);
            
            // Kontrola pre support (lokálne minimum)
            const isSupport = leftPrices.every(p => p >= currentPrice) && 
                             rightPrices.every(p => p >= currentPrice);
            
            // Kontrola pre resistance (lokálne maximum)
            const isResistance = leftPrices.every(p => p <= currentPrice) && 
                                rightPrices.every(p => p <= currentPrice);
            
            if (isSupport) support.push(currentPrice);
            if (isResistance) resistance.push(currentPrice);
        }
        
        return { support, resistance };
    }

    // Trend detection
    static detectTrend(prices: number[], shortPeriod: number = 10, longPeriod: number = 30): {
        trend: 'uptrend' | 'downtrend' | 'sideways';
        strength: number; // 0-100
        angle: number; // uhol trendu v stupňoch
    } {
        if (prices.length < longPeriod) {
            return { trend: 'sideways', strength: 0, angle: 0 };
        }

        const shortMA = this.calculateSMA(prices.slice(-shortPeriod));
        const longMA = this.calculateSMA(prices.slice(-longPeriod));
        
        // Vypočítaj sklon trendu
        const recentPrices = prices.slice(-shortPeriod);
        const slope = this.calculateSlope(recentPrices);
        const angle = Math.atan(slope) * (180 / Math.PI);
        
        let trend: 'uptrend' | 'downtrend' | 'sideways' = 'sideways';
        let strength = 0;
        
        const maDiff = ((shortMA - longMA) / longMA) * 100;
        
        if (maDiff > 0.5 && angle > 5) {
            trend = 'uptrend';
            strength = Math.min(Math.abs(maDiff) * 10 + Math.abs(angle), 100);
        } else if (maDiff < -0.5 && angle < -5) {
            trend = 'downtrend';
            strength = Math.min(Math.abs(maDiff) * 10 + Math.abs(angle), 100);
        }
        
        return { trend, strength, angle };
    }

    // Volume analysis
    static analyzeVolume(volumes: number[], prices: number[]): {
        volumeTrend: 'increasing' | 'decreasing' | 'stable';
        volumePriceConfirmation: boolean;
        strength: number;
    } {
        if (volumes.length < 10 || prices.length < 10) {
            return { volumeTrend: 'stable', volumePriceConfirmation: false, strength: 0 };
        }

        const recentVolumes = volumes.slice(-5);
        const previousVolumes = volumes.slice(-10, -5);
        
        const recentAvgVolume = recentVolumes.reduce((sum, v) => sum + v, 0) / recentVolumes.length;
        const previousAvgVolume = previousVolumes.reduce((sum, v) => sum + v, 0) / previousVolumes.length;
        
        const volumeChange = ((recentAvgVolume - previousAvgVolume) / previousAvgVolume) * 100;
        
        let volumeTrend: 'increasing' | 'decreasing' | 'stable' = 'stable';
        if (volumeChange > 10) volumeTrend = 'increasing';
        else if (volumeChange < -10) volumeTrend = 'decreasing';
        
        // Kontrola potvrdenia ceny objemom
        const recentPrices = prices.slice(-5);
        const priceChange = ((recentPrices[recentPrices.length - 1] - recentPrices[0]) / recentPrices[0]) * 100;
        
        const volumePriceConfirmation = (priceChange > 0 && volumeTrend === 'increasing') ||
                                       (priceChange < 0 && volumeTrend === 'increasing');
        
        return {
            volumeTrend,
            volumePriceConfirmation,
            strength: Math.min(Math.abs(volumeChange), 100)
        };
    }

    // Helper methods
    private static calculateRSIValues(prices: number[], period: number): number[] {
        const rsiValues: number[] = [];
        
        for (let i = period; i <= prices.length; i++) {
            const periodPrices = prices.slice(i - period, i);
            let gains = 0;
            let losses = 0;
            
            for (let j = 1; j < periodPrices.length; j++) {
                const change = periodPrices[j] - periodPrices[j - 1];
                if (change > 0) gains += change;
                else losses += Math.abs(change);
            }
            
            const avgGain = gains / (period - 1);
            const avgLoss = losses / (period - 1);
            
            if (avgLoss === 0) {
                rsiValues.push(100);
            } else {
                const rs = avgGain / avgLoss;
                const rsi = 100 - (100 / (1 + rs));
                rsiValues.push(rsi);
            }
        }
        
        return rsiValues;
    }

    private static calculateSMA(prices: number[]): number {
        return prices.reduce((sum, price) => sum + price, 0) / prices.length;
    }

    private static calculateSlope(prices: number[]): number {
        const n = prices.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = prices.reduce((sum, price) => sum + price, 0);
        const sumXY = prices.reduce((sum, price, index) => sum + (price * index), 0);
        const sumXX = prices.reduce((sum, _, index) => sum + (index * index), 0);
        
        return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    }
}