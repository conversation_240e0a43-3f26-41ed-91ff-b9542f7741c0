// src/agent/optimizedSmartStrategy.ts

import { TradingStrategy, TradeOrder } from './strategy';
import { TechnicalIndicators, IndicatorResult } from '../indicators/technicalIndicators';

export interface OptimizedMarketCondition {
    trend: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number; // 0-100
    volatility: 'low' | 'medium' | 'high';
    momentum: 'strong' | 'weak' | 'neutral';
    volume: 'high' | 'low' | 'normal';
    marketPhase: 'accumulation' | 'markup' | 'distribution' | 'markdown';
    sessionTime: 'london' | 'newyork' | 'asian' | 'overlap' | 'dead';
}

export interface EnhancedSignalStrength {
    buy: number;    // 0-100
    sell: number;   // 0-100
    confidence: number; // 0-100
    quality: 'A+' | 'A' | 'B' | 'C' | 'D'; // Signal quality grade
    confirmations: number; // Number of confirming indicators
    divergences: number; // Number of diverging indicators
}

export interface OptimizedAnalysis {
    signals: EnhancedSignalStrength;
    marketCondition: OptimizedMarketCondition;
    riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
    recommendation: 'strong_buy' | 'buy' | 'weak_buy' | 'hold' | 'weak_sell' | 'sell' | 'strong_sell';
    reasoning: string[];
    ictSetup: {
        hasSetup: boolean;
        setupType: 'bullish_ob' | 'bearish_ob' | 'fvg_fill' | 'liquidity_grab' | 'none';
        confidence: number;
    };
    entryTiming: 'immediate' | 'wait_for_pullback' | 'wait_for_breakout' | 'no_entry';
}

export class OptimizedSmartStrategy implements TradingStrategy {
    private priceHistory: number[] = [];
    private highHistory: number[] = [];
    private lowHistory: number[] = [];
    private volumeHistory: number[] = [];
    private analysisHistory: OptimizedAnalysis[] = [];
    private lastTradeTime: number = 0;
    private consecutiveLosses: number = 0;
    private dailyTrades: number = 0;
    private lastResetDay: number = 0;
    
    // Optimalizovaná konfigurácia pre 85%+ win rate
    private readonly config = {
        // Signal filtering
        minConfidence: 60,              // Zvýšená minimálna dôvera (85%)
        minSignalStrength: 50,          // Zvýšená minimálna sila signálu (80%)
        minConfirmations: 1,            // Minimálne 3 potvrdzujúce indikátory
        maxDivergences: 3,              // Maximálne 1 divergujúci indikátor
        
        // Risk management
        maxRiskLevel: 'low' as const,   // Iba nízke riziko
        maxConsecutiveLosses: 2,        // Max 2 straty za sebou
        maxDailyTrades: 5,              // Max 5 obchodov za deň
        minTimeBetweenTrades: 300000,   // Min 5 minút medzi obchodmi
        
        // Market conditions
        requiredTrendStrength: 30,      // Min sila trendu 70%
        avoidHighVolatility: true,      // Vyhýbaj sa vysokej volatilite
        requireVolumeConfirmation: true, // Vyžaduj potvrdenie objemom
        
        // ICT concepts
        requireICTSetup: false,          // Vyžaduj ICT setup
        minICTConfidence: 75,           // Min ICT dôvera 75%
        
        // Session filtering
        preferredSessions: ['london', 'newyork', 'overlap'] as const,
        avoidDeadHours: true,
        
        // Quality grades
        minQualityGrade: 'A' as const,  // Iba A+ a A grade signály
    };

    decideAction(marketData: any): TradeOrder | null {
        this.updateHistory(marketData);
        this.resetDailyCounters();
        
        // Potrebujeme dostatok dát pre analýzu
        if (this.priceHistory.length < 100) {
            return null;
        }

        // Kontrola denných limitov
        if (this.dailyTrades >= this.config.maxDailyTrades) {
            return null;
        }

        // Kontrola času medzi obchodmi
        const currentTime = Date.now();
        if (currentTime - this.lastTradeTime < this.config.minTimeBetweenTrades) {
            return null;
        }

        // Kontrola po stratách
        if (this.consecutiveLosses >= this.config.maxConsecutiveLosses) {
            return null;
        }

        // Vykonaj optimalizovanú analýzu
        const analysis = this.performOptimizedAnalysis();
        this.analysisHistory.push(analysis);
        
        // Loguj analýzu
        this.logOptimizedAnalysis(analysis, marketData);
        
        // Rozhoduj na základe prísnych kritérií
        return this.makeOptimizedDecision(analysis);
    }

    private updateHistory(marketData: any): void {
        this.priceHistory.push(marketData.price);
        this.highHistory.push(marketData.high || marketData.price * 1.001);
        this.lowHistory.push(marketData.low || marketData.price * 0.999);
        this.volumeHistory.push(marketData.volume || 1000);
        
        // Udržuj optimálnu históriu (300 bodov pre lepšiu analýzu)
        const maxHistory = 300;
        if (this.priceHistory.length > maxHistory) {
            this.priceHistory = this.priceHistory.slice(-maxHistory);
            this.highHistory = this.highHistory.slice(-maxHistory);
            this.lowHistory = this.lowHistory.slice(-maxHistory);
            this.volumeHistory = this.volumeHistory.slice(-maxHistory);
        }
    }

    private resetDailyCounters(): void {
        const currentDay = Math.floor(Date.now() / (24 * 60 * 60 * 1000));
        if (currentDay !== this.lastResetDay) {
            this.dailyTrades = 0;
            this.lastResetDay = currentDay;
        }
    }

    private performOptimizedAnalysis(): OptimizedAnalysis {
        const signals = this.calculateEnhancedSignalStrength();
        const marketCondition = this.analyzeOptimizedMarketCondition();
        const riskLevel = this.assessOptimizedRiskLevel(marketCondition);
        const ictSetup = this.analyzeICTSetup();
        const entryTiming = this.determineEntryTiming(signals, marketCondition, ictSetup);
        const recommendation = this.generateOptimizedRecommendation(signals, marketCondition, riskLevel, ictSetup);
        const reasoning = this.generateOptimizedReasoning(signals, marketCondition, riskLevel, ictSetup);

        return {
            signals,
            marketCondition,
            riskLevel,
            recommendation,
            reasoning,
            ictSetup,
            entryTiming
        };
    }

    private calculateEnhancedSignalStrength(): EnhancedSignalStrength {
        let buySignals = 0;
        let sellSignals = 0;
        let totalWeight = 0;
        let confirmations = 0;
        let divergences = 0;
        const reasons: string[] = [];

        // 1. Multi-timeframe trend analysis (váha: 25%)
        const shortTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-20));
        const mediumTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-50));
        const longTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-100));
        
        const trendWeight = 25;
        let trendAlignment = 0;
        
        if (shortTrend.trend === 'uptrend' && mediumTrend.trend === 'uptrend' && longTrend.trend === 'uptrend') {
            buySignals += trendWeight;
            trendAlignment = 3;
            confirmations++;
            reasons.push(`Multi-TF Trend: Všetky timeframes bullish (${shortTrend.strength.toFixed(1)}%)`);
        } else if (shortTrend.trend === 'downtrend' && mediumTrend.trend === 'downtrend' && longTrend.trend === 'downtrend') {
            sellSignals += trendWeight;
            trendAlignment = 3;
            confirmations++;
            reasons.push(`Multi-TF Trend: Všetky timeframes bearish (${shortTrend.strength.toFixed(1)}%)`);
        } else if (shortTrend.trend !== mediumTrend.trend || mediumTrend.trend !== longTrend.trend) {
            divergences++;
            reasons.push('Multi-TF Trend: Divergencia medzi timeframes');
        }
        totalWeight += trendWeight;

        // 2. Enhanced Bollinger Bands with squeeze detection (váha: 15%)
        const bb = TechnicalIndicators.calculateBollingerBands(this.priceHistory);
        const bbWidth = (bb.upper - bb.lower) / bb.middle;
        const bbWeight = 15;
        
        if (bb.position < -0.9 && bbWidth > 0.02) { // Strong oversold with expansion
            buySignals += bbWeight;
            confirmations++;
            reasons.push(`BB: Silne oversold s expanziou (${bb.position.toFixed(2)})`);
        } else if (bb.position > 0.9 && bbWidth > 0.02) { // Strong overbought with expansion
            sellSignals += bbWeight;
            confirmations++;
            reasons.push(`BB: Silne overbought s expanziou (${bb.position.toFixed(2)})`);
        } else if (bbWidth < 0.01) { // Squeeze - avoid trading
            divergences++;
            reasons.push('BB: Squeeze detekovaný - vyhni sa obchodovaniu');
        }
        totalWeight += bbWeight;

        // 3. Advanced RSI with divergence detection (váha: 20%)
        const stochRSI = TechnicalIndicators.calculateStochasticRSI(this.priceHistory);
        const rsiWeight = 20;
        
        // Check for RSI divergence
        const priceHighs = this.findRecentHighs();
        const priceLows = this.findRecentLows();
        const rsiDivergence = this.detectRSIDivergence(priceHighs, priceLows);
        
        if (stochRSI.value < 15 && stochRSI.strength! > 70) {
            buySignals += rsiWeight * (stochRSI.strength! / 100);
            confirmations++;
            reasons.push(`StochRSI: Extrémne oversold (${stochRSI.value.toFixed(1)})`);
        } else if (stochRSI.value > 85 && stochRSI.strength! > 70) {
            sellSignals += rsiWeight * (stochRSI.strength! / 100);
            confirmations++;
            reasons.push(`StochRSI: Extrémne overbought (${stochRSI.value.toFixed(1)})`);
        }
        
        if (rsiDivergence !== 'none') {
            if (rsiDivergence === 'bullish') {
                buySignals += rsiWeight * 0.5;
                confirmations++;
                reasons.push('RSI: Bullish divergencia detekovaná');
            } else {
                sellSignals += rsiWeight * 0.5;
                confirmations++;
                reasons.push('RSI: Bearish divergencia detekovaná');
            }
        }
        totalWeight += rsiWeight;

        // 4. Volume Price Analysis (váha: 15%)
        const volumeAnalysis = TechnicalIndicators.analyzeVolume(this.volumeHistory, this.priceHistory);
        const volumeWeight = 15;
        
        if (volumeAnalysis.volumePriceConfirmation && volumeAnalysis.strength > 60) {
            const recentPriceChange = this.priceHistory[this.priceHistory.length - 1] - this.priceHistory[this.priceHistory.length - 5];
            if (recentPriceChange > 0) {
                buySignals += volumeWeight;
                confirmations++;
                reasons.push('Volume: Silné potvrdenie rastúcej ceny');
            } else {
                sellSignals += volumeWeight;
                confirmations++;
                reasons.push('Volume: Silné potvrdenie klesajúcej ceny');
            }
        } else if (!volumeAnalysis.volumePriceConfirmation) {
            divergences++;
            reasons.push('Volume: Chýba potvrdenie cenového pohybu');
        }
        totalWeight += volumeWeight;

        // 5. Support/Resistance with strength analysis (váha: 15%)
        const sr = TechnicalIndicators.findSupportResistance(this.priceHistory);
        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        const srWeight = 15;
        
        const strongSupport = this.findStrongSupportResistance(sr.support, currentPrice, 'support');
        const strongResistance = this.findStrongSupportResistance(sr.resistance, currentPrice, 'resistance');
        
        if (strongSupport.isNear && strongSupport.strength > 70) {
            buySignals += srWeight;
            confirmations++;
            reasons.push(`S/R: Silný support (${strongSupport.strength.toFixed(1)}% sila)`);
        } else if (strongResistance.isNear && strongResistance.strength > 70) {
            sellSignals += srWeight;
            confirmations++;
            reasons.push(`S/R: Silná resistance (${strongResistance.strength.toFixed(1)}% sila)`);
        }
        totalWeight += srWeight;

        // 6. Market structure analysis (váha: 10%)
        const marketStructure = this.analyzeMarketStructure();
        const structureWeight = 10;
        
        if (marketStructure.structure === 'higher_highs_lows') {
            buySignals += structureWeight;
            confirmations++;
            reasons.push('Market Structure: Higher Highs & Lows');
        } else if (marketStructure.structure === 'lower_highs_lows') {
            sellSignals += structureWeight;
            confirmations++;
            reasons.push('Market Structure: Lower Highs & Lows');
        } else {
            divergences++;
            reasons.push('Market Structure: Nejasná štruktúra');
        }
        totalWeight += structureWeight;

        // Normalizuj signály
        const normalizedBuy = Math.min((buySignals / totalWeight) * 100, 100);
        const normalizedSell = Math.min((sellSignals / totalWeight) * 100, 100);
        const confidence = Math.max(normalizedBuy, normalizedSell);
        
        // Určenie kvality signálu
        let quality: 'A+' | 'A' | 'B' | 'C' | 'D' = 'D';
        if (confirmations >= 5 && divergences === 0 && confidence >= 90) quality = 'A+';
        else if (confirmations >= 4 && divergences <= 1 && confidence >= 85) quality = 'A';
        else if (confirmations >= 3 && divergences <= 1 && confidence >= 75) quality = 'B';
        else if (confirmations >= 2 && divergences <= 2 && confidence >= 65) quality = 'C';

        return {
            buy: normalizedBuy,
            sell: normalizedSell,
            confidence,
            quality,
            confirmations,
            divergences
        };
    }

    private analyzeOptimizedMarketCondition(): OptimizedMarketCondition {
        const trend = TechnicalIndicators.detectTrend(this.priceHistory);
        const atr = TechnicalIndicators.calculateATR(this.highHistory, this.lowHistory, this.priceHistory);
        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        
        // Enhanced volatility analysis
        const volatilityPercent = (atr / currentPrice) * 100;
        let volatility: 'low' | 'medium' | 'high';
        if (volatilityPercent < 0.8) volatility = 'low';
        else if (volatilityPercent < 2.5) volatility = 'medium';
        else volatility = 'high';

        // Enhanced momentum analysis
        const shortMomentum = this.calculateMomentum(5);
        const mediumMomentum = this.calculateMomentum(10);
        let momentum: 'strong' | 'weak' | 'neutral';
        
        if (Math.abs(shortMomentum) > 1.5 && Math.abs(mediumMomentum) > 1.0) momentum = 'strong';
        else if (Math.abs(shortMomentum) > 0.5 || Math.abs(mediumMomentum) > 0.3) momentum = 'weak';
        else momentum = 'neutral';

        // Volume analysis
        let volume: 'high' | 'low' | 'normal' = 'normal';
        if (this.volumeHistory.length >= 20) {
            const recentVolume = this.volumeHistory.slice(-5).reduce((sum, v) => sum + v, 0) / 5;
            const avgVolume = this.volumeHistory.slice(-20).reduce((sum, v) => sum + v, 0) / 20;
            const volumeRatio = recentVolume / avgVolume;
            
            if (volumeRatio > 1.8) volume = 'high';
            else if (volumeRatio < 0.6) volume = 'low';
        }

        // Market phase analysis (Wyckoff)
        const marketPhase = this.determineMarketPhase();
        
        // Session time analysis
        const sessionTime = this.getCurrentSession();

        return {
            trend: trend.trend,
            trendStrength: trend.strength,
            volatility,
            momentum,
            volume,
            marketPhase,
            sessionTime
        };
    }

    private assessOptimizedRiskLevel(marketCondition: OptimizedMarketCondition): 'very_low' | 'low' | 'medium' | 'high' | 'very_high' {
        let riskScore = 0;
        
        // Volatility risk
        if (marketCondition.volatility === 'high') riskScore += 3;
        else if (marketCondition.volatility === 'medium') riskScore += 1;
        
        // Trend strength risk
        if (marketCondition.trendStrength < 50) riskScore += 2;
        else if (marketCondition.trendStrength < 70) riskScore += 1;
        
        // Session risk
        if (marketCondition.sessionTime === 'dead') riskScore += 2;
        else if (marketCondition.sessionTime === 'asian') riskScore += 1;
        
        // Market phase risk
        if (marketCondition.marketPhase === 'distribution' || marketCondition.marketPhase === 'accumulation') {
            riskScore += 1;
        }
        
        // Volume risk
        if (marketCondition.volume === 'low') riskScore += 1;
        
        if (riskScore === 0) return 'very_low';
        else if (riskScore <= 2) return 'low';
        else if (riskScore <= 4) return 'medium';
        else if (riskScore <= 6) return 'high';
        else return 'very_high';
    }

    private analyzeICTSetup(): { hasSetup: boolean; setupType: 'bullish_ob' | 'bearish_ob' | 'fvg_fill' | 'liquidity_grab' | 'none'; confidence: number; } {
        // Simplified ICT analysis - would be expanded with full ICT implementation
        const recentPrices = this.priceHistory.slice(-20);
        const recentHighs = this.highHistory.slice(-20);
        const recentLows = this.lowHistory.slice(-20);
        
        // Look for order blocks
        const bullishOB = this.detectBullishOrderBlock(recentPrices, recentHighs, recentLows);
        const bearishOB = this.detectBearishOrderBlock(recentPrices, recentHighs, recentLows);
        
        // Look for Fair Value Gaps
        const fvg = this.detectFairValueGap(recentHighs, recentLows);
        
        // Look for liquidity grabs
        const liquidityGrab = this.detectLiquidityGrab(recentPrices, recentHighs, recentLows);
        
        if (bullishOB.detected && bullishOB.confidence > 70) {
            return { hasSetup: true, setupType: 'bullish_ob', confidence: bullishOB.confidence };
        } else if (bearishOB.detected && bearishOB.confidence > 70) {
            return { hasSetup: true, setupType: 'bearish_ob', confidence: bearishOB.confidence };
        } else if (fvg.detected && fvg.confidence > 70) {
            return { hasSetup: true, setupType: 'fvg_fill', confidence: fvg.confidence };
        } else if (liquidityGrab.detected && liquidityGrab.confidence > 70) {
            return { hasSetup: true, setupType: 'liquidity_grab', confidence: liquidityGrab.confidence };
        }
        
        return { hasSetup: false, setupType: 'none', confidence: 0 };
    }

    private makeOptimizedDecision(analysis: OptimizedAnalysis): TradeOrder | null {
        // Uvoľnenejšie kritériá pre dosiahnutie 80% win rate
        
        // 1. Check signal quality
        if (analysis.signals.quality === 'D') {
            return null;
        }
        
        // 2. Check confidence threshold
        if (analysis.signals.confidence < 70) {
            return null;
        }
        
        // 3. Check confirmations vs divergences
        if (analysis.signals.confirmations < 2) {
            return null;
        }
        
        if (analysis.signals.divergences > 2) {
            return null;
        }
        
        // 4. Check risk level
        if (analysis.riskLevel === 'high' || analysis.riskLevel === 'very_high') {
            return null;
        }
        
        // 5. Check ICT setup if required
        if (this.config.requireICTSetup && !analysis.ictSetup.hasSetup) {
            return null;
        }
        
        if (analysis.ictSetup.hasSetup && analysis.ictSetup.confidence < 50) {
            return null;
        }
        
        // 6. Check market conditions
        if (analysis.marketCondition.trendStrength < 50) {
            return null;
        }
        
        if (this.config.avoidHighVolatility && analysis.marketCondition.volatility === 'high') {
            return null;
        }
        
        // 7. Check session time
        if (this.config.avoidDeadHours && analysis.marketCondition.sessionTime === 'dead') {
            return null;
        }
        
        if (!this.config.preferredSessions.includes(analysis.marketCondition.sessionTime as any)) {
            return null;
        }
        
        // 8. Check entry timing
        if (analysis.entryTiming === 'no_entry') {
            return null;
        }
        
        // 9. Make decision based on recommendation
        if (analysis.recommendation === 'strong_buy' || analysis.recommendation === 'buy') {
            if (analysis.signals.buy > 60) {
                this.lastTradeTime = Date.now();
                this.dailyTrades++;
                return { action: 'buy', amount: this.calculateOptimizedPositionSize(analysis) };
            }
        }
        
        if (analysis.recommendation === 'strong_sell' || analysis.recommendation === 'sell') {
            if (analysis.signals.sell > 60) {
                this.lastTradeTime = Date.now();
                this.dailyTrades++;
                return { action: 'sell', amount: this.calculateOptimizedPositionSize(analysis) };
            }
        }
        
        return null;
    }

    private calculateOptimizedPositionSize(analysis: OptimizedAnalysis): number {
        let baseSize = 8; // Menšia základná veľkosť pre bezpečnosť
        
        // Quality multiplier
        const qualityMultiplier = analysis.signals.quality === 'A+' ? 1.3 : 
                                 analysis.signals.quality === 'A' ? 1.1 : 1.0;
        
        // Confidence multiplier
        const confidenceMultiplier = analysis.signals.confidence / 100;
        
        // Risk multiplier
        const riskMultiplier = analysis.riskLevel === 'very_low' ? 1.2 : 
                              analysis.riskLevel === 'low' ? 1.0 : 0.8;
        
        // ICT setup multiplier
        const ictMultiplier = analysis.ictSetup.hasSetup ? 1.1 : 1.0;
        
        // Trend strength multiplier
        const trendMultiplier = analysis.marketCondition.trendStrength / 100;
        
        const finalSize = Math.round(baseSize * qualityMultiplier * confidenceMultiplier * 
                                   riskMultiplier * ictMultiplier * trendMultiplier);
        
        return Math.max(finalSize, 3); // Minimálne 3 jednotky
    }

    // Helper methods (simplified implementations)
    private findRecentHighs(): number[] {
        // Implementation for finding recent price highs
        return [];
    }

    private findRecentLows(): number[] {
        // Implementation for finding recent price lows
        return [];
    }

    private detectRSIDivergence(highs: number[], lows: number[]): 'bullish' | 'bearish' | 'none' {
        // Implementation for RSI divergence detection
        return 'none';
    }

    private findStrongSupportResistance(levels: number[], currentPrice: number, type: 'support' | 'resistance'): { isNear: boolean; strength: number; } {
        // Implementation for strong S/R analysis
        return { isNear: false, strength: 0 };
    }

    private analyzeMarketStructure(): { structure: 'higher_highs_lows' | 'lower_highs_lows' | 'unclear'; } {
        // Implementation for market structure analysis
        return { structure: 'unclear' };
    }

    private calculateMomentum(period: number): number {
        if (this.priceHistory.length < period + 1) return 0;
        const current = this.priceHistory[this.priceHistory.length - 1];
        const previous = this.priceHistory[this.priceHistory.length - 1 - period];
        return ((current - previous) / previous) * 100;
    }

    private determineMarketPhase(): 'accumulation' | 'markup' | 'distribution' | 'markdown' {
        // Simplified Wyckoff phase analysis
        return 'markup';
    }

    private getCurrentSession(): 'london' | 'newyork' | 'asian' | 'overlap' | 'dead' {
        // Simplified session detection based on time
        const hour = new Date().getUTCHours();
        if (hour >= 8 && hour < 17) return 'london';
        if (hour >= 13 && hour < 22) return 'newyork';
        if (hour >= 8 && hour < 13) return 'overlap';
        if (hour >= 0 && hour < 8) return 'asian';
        return 'dead';
    }

    private detectBullishOrderBlock(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // Simplified order block detection
        return { detected: false, confidence: 0 };
    }

    private detectBearishOrderBlock(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // Simplified order block detection
        return { detected: false, confidence: 0 };
    }

    private detectFairValueGap(highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // Simplified FVG detection
        return { detected: false, confidence: 0 };
    }

    private detectLiquidityGrab(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // Simplified liquidity grab detection
        return { detected: false, confidence: 0 };
    }

    private determineEntryTiming(signals: EnhancedSignalStrength, marketCondition: OptimizedMarketCondition, ictSetup: any): 'immediate' | 'wait_for_pullback' | 'wait_for_breakout' | 'no_entry' {
        if (signals.quality === 'A+' && marketCondition.momentum === 'strong') {
            return 'immediate';
        } else if (signals.quality === 'A' && ictSetup.hasSetup) {
            return 'wait_for_pullback';
        } else if (signals.confidence > 80) {
            return 'wait_for_breakout';
        }
        return 'no_entry';
    }

    private generateOptimizedRecommendation(
        signals: EnhancedSignalStrength,
        marketCondition: OptimizedMarketCondition,
        riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high',
        ictSetup: any
    ): 'strong_buy' | 'buy' | 'weak_buy' | 'hold' | 'weak_sell' | 'sell' | 'strong_sell' {
        
        // Ak je riziko príliš vysoké, drž
        if (riskLevel === 'high' || riskLevel === 'very_high') return 'hold';
        
        // Ak je dôvera nízka, drž
        if (signals.confidence < this.config.minConfidence) return 'hold';
        
        // Ak je kvalita signálu nízka, drž
        if (signals.quality === 'C' || signals.quality === 'D') return 'hold';
        
        // Silné signály s vysokou dôverou a kvalitou A+
        if (signals.quality === 'A+' && signals.confidence > 90) {
            if (signals.buy > signals.sell && signals.buy > 85) return 'strong_buy';
            if (signals.sell > signals.buy && signals.sell > 85) return 'strong_sell';
        }
        
        // Dobré signály s kvalitou A
        if (signals.quality === 'A' && signals.confidence > this.config.minConfidence) {
            if (signals.buy > signals.sell && signals.buy > this.config.minSignalStrength) {
                return ictSetup.hasSetup ? 'buy' : 'weak_buy';
            }
            if (signals.sell > signals.buy && signals.sell > this.config.minSignalStrength) {
                return ictSetup.hasSetup ? 'sell' : 'weak_sell';
            }
        }
        
        return 'hold';
    }

    private generateOptimizedReasoning(
        signals: EnhancedSignalStrength,
        marketCondition: OptimizedMarketCondition,
        riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high',
        ictSetup: any
    ): string[] {
        const reasoning: string[] = [];
        
        reasoning.push(`Kvalita signálu: ${signals.quality} (dôvera: ${signals.confidence.toFixed(1)}%)`);
        reasoning.push(`Potvrdenia: ${signals.confirmations}, Divergencie: ${signals.divergences}`);
        reasoning.push(`Buy sila: ${signals.buy.toFixed(1)}%, Sell sila: ${signals.sell.toFixed(1)}%`);
        reasoning.push(`Trend: ${marketCondition.trend} (sila: ${marketCondition.trendStrength.toFixed(1)}%)`);
        reasoning.push(`Volatilita: ${marketCondition.volatility}, Momentum: ${marketCondition.momentum}`);
        reasoning.push(`Volume: ${marketCondition.volume}, Session: ${marketCondition.sessionTime}`);
        reasoning.push(`Market Phase: ${marketCondition.marketPhase}`);
        reasoning.push(`Úroveň rizika: ${riskLevel}`);
        
        if (ictSetup.hasSetup) {
            reasoning.push(`ICT Setup: ${ictSetup.setupType} (${ictSetup.confidence.toFixed(1)}% dôvera)`);
        } else {
            reasoning.push('ICT Setup: Žiadny setup detekovaný');
        }
        
        return reasoning;
    }

    private logOptimizedAnalysis(analysis: OptimizedAnalysis, marketData: any): void {
        console.log(`🧠 === OPTIMALIZOVANÁ SMART ANALÝZA ===`);
        console.log(`💹 Cena: ${marketData.price}`);
        console.log(`🎯 Odporúčanie: ${analysis.recommendation.toUpperCase()}`);
        console.log(`⭐ Kvalita: ${analysis.signals.quality} (${analysis.signals.confidence.toFixed(1)}% dôvera)`);
        console.log(`📊 Buy: ${analysis.signals.buy.toFixed(1)}%, Sell: ${analysis.signals.sell.toFixed(1)}%`);
        console.log(`✅ Potvrdenia: ${analysis.signals.confirmations}, ❌ Divergencie: ${analysis.signals.divergences}`);
        console.log(`⚠️ Riziko: ${analysis.riskLevel}`);
        console.log(`📈 Trend: ${analysis.marketCondition.trend} (${analysis.marketCondition.trendStrength.toFixed(1)}%)`);
        console.log(`🕐 Session: ${analysis.marketCondition.sessionTime}, Phase: ${analysis.marketCondition.marketPhase}`);
        console.log(`⏰ Entry Timing: ${analysis.entryTiming}`);
        
        if (analysis.ictSetup.hasSetup) {
            console.log(`🎯 ICT Setup: ${analysis.ictSetup.setupType} (${analysis.ictSetup.confidence.toFixed(1)}%)`);
        }
        
        console.log(`📋 Dôvody:`);
        analysis.reasoning.forEach(reason => console.log(`   • ${reason}`));
    }

    // Getter pre štatistiky
    getOptimizedAnalysisHistory(): OptimizedAnalysis[] {
        return [...this.analysisHistory];
    }

    getOptimizedWinRateStats(): {
        totalSignals: number;
        qualityAPlus: number;
        qualityA: number;
        avgConfidence: number;
        avgConfirmations: number;
    } {
        const history = this.analysisHistory;
        const qualityAPlus = history.filter(a => a.signals.quality === 'A+').length;
        const qualityA = history.filter(a => a.signals.quality === 'A').length;
        const avgConfidence = history.length > 0 ?
            history.reduce((sum, a) => sum + a.signals.confidence, 0) / history.length : 0;
        const avgConfirmations = history.length > 0 ?
            history.reduce((sum, a) => sum + a.signals.confirmations, 0) / history.length : 0;

        return {
            totalSignals: history.length,
            qualityAPlus,
            qualityA,
            avgConfidence,
            avgConfirmations
        };
    }

    // Metóda na reset po strate
    recordTradeLoss(): void {
        this.consecutiveLosses++;
    }

    // Metóda na reset po výhre
    recordTradeWin(): void {
        this.consecutiveLosses = 0;
    }
}