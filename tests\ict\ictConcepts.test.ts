import { ictConcepts, ICTConceptsConfig } from '../../src/ict/ictConcepts';
import * as assert from 'assert';

describe('ictConcepts', () => {
  it('should return 0 for now', () => {
    const data = [1, 2, 3, 4, 5];
    const defaultConfig: ICTConceptsConfig = {
      i_mode: 'Present',
      showMS: true,
      len: 5,
      iMSS: true,
      cMSSbl: '#00e6a1',
      cMSSbr: '#e60400',
      cBOSbl: '#00e6a1',
      cBOSbr: '#e60400',
      iBOS: true,
      sDispl: false,
      sVimbl: true,
      visVim: 2,
      cVimbl: '#06b2d0',
      showOB: true,
      length: 10,
      showBull: 1,
      showBear: 1,
      useBody: true,
      bullCss: '#3e89fa',
      bullBrkCss: '#4785f985',
      bearCss: '#FF3131',
      bearBrkCss: '#f9ff5785',
      showLabels: false,
      showLq: true,
      a: 2.5,
      visLiq: 2,
      cLIQ_B: '#fa451c',
      cLIQ_S: '#1ce4fa',
      shwFVG: true,
      i_BPR: false,
      i_FVG: 'FVG',
      visBxs: 2,
      cFVGbl: '#00e676',
      cFVGblBR: '#808000',
      cFVGbr: '#ff5252',
      cFVGbrBR: '#FF0000',
      iNWOG: true,
      cNWOG1: '#ff525228',
      cNWOG2: '#b2b5be50',
      maxNWOG: 3,
      iNDOG: false,
      cNDOG1: '#ff980020',
      cNDOG2: '#4dd0e165',
      maxNDOG: 1,
      iFib: 'NONE',
      iExt: false,
      showKZ: false,
      showNy: true,
      nyCss: '#ff5d0093',
      showLdno: true,
      ldnoCss: '#00bcd493',
      showLdnc: true,
      ldncCss: '#2157f393',
      showAsia: true,
      asiaCss: '#e91e6393',
    };
    const actual = ictConcepts(data, defaultConfig);
    assert.strictEqual(typeof actual, typeof [], 'The result should be an array');
  });
});