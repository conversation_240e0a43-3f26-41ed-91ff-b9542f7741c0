import { MCPTradingClient } from '../../src/utils/mcp-client';

describe('MCP Basic Integration Tests', () => {
  let mcpClient: MCPTradingClient;

  beforeAll(() => {
    mcpClient = new MCPTradingClient();
  });

  afterAll(async () => {
    await mcpClient.disconnect();
  });

  test('should create MCP client instance', () => {
    expect(mcpClient).toBeDefined();
    expect(mcpClient.isConnected()).toBe(false);
  });

  test('should get connection stats', () => {
    const stats = mcpClient.getConnectionStats();
    expect(stats).toHaveProperty('connected');
    expect(stats).toHaveProperty('attempts');
    expect(stats).toHaveProperty('maxRetries');
    expect(stats.connected).toBe(false);
    expect(stats.maxRetries).toBe(3);
  });

  test('should handle connection failure gracefully', async () => {
    // Tento test o<PERSON>, že MCP server nie je spustený
    const isConnected = await mcpClient.testConnection();
    expect(isConnected).toBe(false);
  });

  test('should handle forex data request failure', async () => {
    // Bez spusteného MCP servera by malo zlyhať
    await expect(mcpClient.getForexData('EURUSD')).rejects.toThrow();
  });

  test('should handle stock data request failure', async () => {
    // Bez spusteného MCP servera by malo zlyhať
    await expect(mcpClient.getStockData('AAPL')).rejects.toThrow();
  });

  test('should handle indicators request failure', async () => {
    // Bez spusteného MCP servera by malo zlyhať
    await expect(mcpClient.getMarketIndicators('EURUSD', ['RSI', 'MACD'])).rejects.toThrow();
  });
});