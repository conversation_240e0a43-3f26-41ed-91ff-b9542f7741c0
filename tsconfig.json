{"compilerOptions": {"target": "es6", "module": "commonjs", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": ".", "declaration": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "build", "out"]}