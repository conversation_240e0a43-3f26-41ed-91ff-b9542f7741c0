// src/demo/strategyOptimizationDemo.ts

import { StrategyComparison } from '../analysis/strategyComparison';
import { log } from '../utils/logger';

async function runStrategyOptimizationDemo() {
    try {
        log('🚀 === DEMO: OPTIMALIZÁCIA SMART STRATEGY PRE VYŠŠÍ WIN RATE ===\n');
        
        log('Tento demo porovnáva pôvodnú Smart Strategy s optimalizovanou verziou.');
        log('Cieľ: Dosiahnuť 85%+ win rate pomocou prísnejš<PERSON>ch filtrov a lepšej analýzy.\n');
        
        log('🔧 KĽÚČOVÉ OPTIMALIZÁCIE:');
        log('1. Zvýšená minimálna dôvera signálu z 75% na 85%');
        log('2. Vyžadované minimálne 3 potvrdzujúce indikátory');
        log('3. Maximálne 1 divergujúci indikátor');
        log('4. <PERSON><PERSON><PERSON><PERSON><PERSON> hodnotenie signálov (A+, A, B, C, D)');
        log('5. ICT koncepty integrácia');
        log('6. Multi-timeframe analýza');
        log('7. Pokročilá session time filtrácia');
        log('8. Prísne risk management pravidlá');
        log('9. Denné limity obchodov (max 5)');
        log('10. Ochrana po stratách (max 2 za sebou)\n');
        
        const initialBalance = 10000;
        log(`💰 Počiatočný zostatok: ${initialBalance}`);
        log('📊 Testovanie na 6 rôznych trhových scenároch...\n');
        
        // Spusti porovnanie
        const comparisonResult = await StrategyComparison.compareStrategies(initialBalance);
        
        // Zobraz výsledky
        StrategyComparison.printComparisonReport(comparisonResult);
        
        // Dodatočné analýzy
        log('\n📈 === DODATOČNÉ ANALÝZY ===\n');
        
        // Analýza kvality signálov
        const originalQuality = comparisonResult.originalStrategy.qualityMetrics;
        const optimizedQuality = comparisonResult.optimizedStrategy.qualityMetrics;
        
        log('🎯 KVALITA SIGNÁLOV:');
        log(`Pôvodná stratégia:`);
        log(`  • Priemerná dôvera: ${originalQuality.avgConfidence.toFixed(1)}%`);
        log(`  • Priemerné potvrdenia: ${originalQuality.avgConfirmations.toFixed(1)}`);
        
        log(`Optimalizovaná stratégia:`);
        log(`  • Priemerná dôvera: ${optimizedQuality.avgConfidence.toFixed(1)}%`);
        log(`  • Priemerné potvrdenia: ${optimizedQuality.avgConfirmations.toFixed(1)}`);
        log(`  • Vysoká kvalita signálov: ${optimizedQuality.highQualitySignals}`);
        
        // Risk analýza
        log('\n⚠️ RISK ANALÝZA:');
        const originalRisk = comparisonResult.originalStrategy.riskMetrics;
        const optimizedRisk = comparisonResult.optimizedStrategy.riskMetrics;
        
        log(`Pôvodná stratégia:`);
        log(`  • Max drawdown: ${comparisonResult.originalStrategy.maxDrawdown.toFixed(2)}%`);
        log(`  • Max straty za sebou: ${originalRisk.maxConsecutiveLosses}`);
        log(`  • Sharpe ratio: ${originalRisk.sharpeRatio.toFixed(2)}`);
        
        log(`Optimalizovaná stratégia:`);
        log(`  • Max drawdown: ${comparisonResult.optimizedStrategy.maxDrawdown.toFixed(2)}%`);
        log(`  • Max straty za sebou: ${optimizedRisk.maxConsecutiveLosses}`);
        log(`  • Sharpe ratio: ${optimizedRisk.sharpeRatio.toFixed(2)}`);
        
        // Efektivita obchodovania
        log('\n📊 EFEKTIVITA OBCHODOVANIA:');
        const originalTrades = comparisonResult.originalStrategy.totalTrades;
        const optimizedTrades = comparisonResult.optimizedStrategy.totalTrades;
        const originalWinRate = comparisonResult.originalStrategy.winRate;
        const optimizedWinRate = comparisonResult.optimizedStrategy.winRate;
        
        log(`Počet obchodov:`);
        log(`  • Pôvodná: ${originalTrades}`);
        log(`  • Optimalizovaná: ${optimizedTrades}`);
        log(`  • Zmena: ${optimizedTrades - originalTrades} (${((optimizedTrades - originalTrades) / originalTrades * 100).toFixed(1)}%)`);
        
        log(`Kvalita vs. Kvantita:`);
        const originalQualityScore = originalWinRate * originalTrades / 100;
        const optimizedQualityScore = optimizedWinRate * optimizedTrades / 100;
        log(`  • Pôvodná: ${originalQualityScore.toFixed(1)} úspešných obchodov`);
        log(`  • Optimalizovaná: ${optimizedQualityScore.toFixed(1)} úspešných obchodov`);
        log(`  • Zlepšenie: ${(optimizedQualityScore - originalQualityScore).toFixed(1)} (+${((optimizedQualityScore - originalQualityScore) / originalQualityScore * 100).toFixed(1)}%)`);
        
        // Odporúčania
        log('\n💡 === ODPORÚČANIA PRE ĎALŠIE ZLEPŠENIA ===\n');
        
        if (comparisonResult.optimizedStrategy.winRate >= 85) {
            log('🎉 CIEĽ DOSIAHNUTÝ! Win rate 85%+ bol dosiahnutý.');
            log('Odporúčania pre ďalšie optimalizácie:');
            log('• Implementovať pokročilé ICT koncepty (Order Blocks, FVG, Liquidity)');
            log('• Pridať sentiment analýzu');
            log('• Optimalizovať position sizing na základe volatility');
            log('• Implementovať adaptive risk management');
        } else if (comparisonResult.optimizedStrategy.winRate >= 80) {
            log('✅ VEĽMI DOBRÉ! Win rate 80%+ dosiahnutý, blízko k cieľu.');
            log('Odporúčania:');
            log('• Zvýšiť minimálnu dôveru na 90%');
            log('• Pridať viac konfirmačných indikátorov');
            log('• Implementovať market regime detection');
        } else if (comparisonResult.optimizedStrategy.winRate >= 75) {
            log('👍 DOBRÉ ZLEPŠENIE! Win rate 75%+ dosiahnutý.');
            log('Odporúčania:');
            log('• Analyzovať neúspešné obchody');
            log('• Pridať viac filtrov pre trhové podmienky');
            log('• Optimalizovať entry timing');
        } else {
            log('⚠️ POTREBNÉ ĎALŠIE OPTIMALIZÁCIE!');
            log('Odporúčania:');
            log('• Zvýšiť prísnosť filtrov');
            log('• Analyzovať a opraviť slabé body');
            log('• Pridať viac konfirmačných signálov');
        }
        
        // Implementačné kroky
        log('\n🔧 === ĎALŠIE KROKY PRE IMPLEMENTÁCIU ===\n');
        log('1. Spustiť testy: npm test');
        log('2. Integrovať optimalizovanú stratégiu do hlavnej aplikácie');
        log('3. Nastaviť live monitoring výkonnosti');
        log('4. Implementovať A/B testing medzi stratégiami');
        log('5. Vytvoriť dashboard pre sledovanie metrík');
        log('6. Nastaviť alerting pre neočakávané výsledky');
        
        log('\n🎯 === ZÁVER ===\n');
        log('Optimalizovaná Smart Strategy implementuje pokročilé techniky');
        log('pre dosiahnutie vyššieho win rate pri zachovaní nízkého rizika.');
        log('Kľúčom je kvalita nad kvantitou - menej obchodov, ale lepších.\n');
        
        log('🚀 Demo dokončené! Optimalizovaná stratégia je pripravená na použitie.');
        
    } catch (error) {
        log(`❌ Chyba počas demo: ${error instanceof Error ? error.message : 'Neznáma chyba'}`);
    }
}

// Spusti demo ak je súbor spustený priamo
if (require.main === module) {
    runStrategyOptimizationDemo();
}

export { runStrategyOptimizationDemo };