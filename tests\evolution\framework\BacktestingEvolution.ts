// tests/evolution/framework/BacktestingEvolution.ts
// Komplexný backtesting framework pre evolúciu trading stratégií

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { AgentEvolutionFramework } from './AgentEvolutionFramework';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface BacktestingConfig {
    walkForwardPeriods: number;
    trainTestSplit: number;
    validationSplit: number;
    rebalancePeriod: number;
    minTrainingSamples: number;
}

export interface WalkForwardResult {
    periods: {
        trainPerformance: FitnessResult;
        testPerformance: FitnessResult;
        optimizedParameters: any;
        period: number;
    }[];
    overallPerformance: FitnessResult;
    stabilityMetrics: {
        consistency: number;
        volatility: number;
        maxDrawdown: number;
    };
}

export interface CrossValidationResult {
    foldResults: {
        fold: number;
        trainScore: number;
        validationScore: number;
        parameters: any;
    }[];
    meanScore: number;
    stdScore: number;
    confidenceInterval: [number, number];
}

export interface MonteCarloResult {
    simulations: {
        simulation: number;
        score: number;
        parameters: any;
    }[];
    statistics: {
        mean: number;
        std: number;
        min: number;
        max: number;
        percentiles: { [key: string]: number };
    };
    confidenceIntervals: { [key: string]: [number, number] };
}

export class BacktestingEvolution {
    private config: BacktestingConfig;

    constructor(config: BacktestingConfig) {
        this.config = config;
    }

    /**
     * Walk-forward optimalizácia
     */
    async walkForwardOptimization(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            windowSize: number;
            stepSize: number;
            optimizationPeriods: number;
        }
    ): Promise<WalkForwardResult> {
        const periods = [];
        const { windowSize, stepSize, optimizationPeriods } = options;
        
        for (let i = 0; i < optimizationPeriods; i++) {
            const startIdx = i * stepSize;
            const trainEndIdx = startIdx + windowSize;
            const testEndIdx = Math.min(trainEndIdx + stepSize, marketData.length);
            
            if (testEndIdx >= marketData.length) break;
            
            const trainData = marketData.slice(startIdx, trainEndIdx);
            const testData = marketData.slice(trainEndIdx, testEndIdx);
            
            console.log(`🔄 Walk-Forward Period ${i + 1}: Train [${startIdx}-${trainEndIdx}], Test [${trainEndIdx}-${testEndIdx}]`);
            
            // Optimalizuj na trénovacích dátach
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: 15,
                generations: 5,
                mutationRate: 0.15,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });
            
            const evolutionResult = await evolutionFramework.evolve(trainData, initialBalance);
            
            // Testuj na out-of-sample dátach
            const testPerformance = await fitnessEvaluator.evaluateFitness(
                evolutionResult.bestIndividual,
                testData,
                initialBalance
            );
            
            periods.push({
                trainPerformance: evolutionResult.bestFitness,
                testPerformance,
                optimizedParameters: evolutionResult.bestIndividual.getParameters(),
                period: i + 1
            });
        }
        
        // Vypočítaj celkovú výkonnosť a stabilitu
        const overallPerformance = this.calculateOverallPerformance(periods);
        const stabilityMetrics = this.calculateStabilityMetrics(periods);
        
        return {
            periods,
            overallPerformance,
            stabilityMetrics
        };
    }

    /**
     * Detekcia overfittingu
     */
    async detectOverfitting(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            complexityLevels: { populationSize: number; generations: number }[];
        }
    ): Promise<{
        complexityResults: {
            complexity: number;
            trainScore: number;
            testScore: number;
            gap: number;
        }[];
        optimalComplexity: number;
        overfittingScore: number;
    }> {
        const splitIdx = Math.floor(marketData.length * 0.7);
        const trainData = marketData.slice(0, splitIdx);
        const testData = marketData.slice(splitIdx);
        
        const complexityResults = [];
        
        for (let i = 0; i < options.complexityLevels.length; i++) {
            const level = options.complexityLevels[i];
            
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: level.populationSize,
                generations: level.generations,
                mutationRate: 0.1,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });
            
            const result = await evolutionFramework.evolve(trainData, initialBalance);
            
            const testPerformance = await fitnessEvaluator.evaluateFitness(
                result.bestIndividual,
                testData,
                initialBalance
            );
            
            const trainScore = result.bestFitness.overallScore;
            const testScore = testPerformance.overallScore;
            const gap = trainScore - testScore;
            
            complexityResults.push({
                complexity: i + 1,
                trainScore,
                testScore,
                gap
            });
        }
        
        // Nájdi optimálnu komplexnosť (najmenší gap pri rozumnej výkonnosti)
        const optimalComplexity = this.findOptimalComplexity(complexityResults);
        const overfittingScore = this.calculateOverfittingScore(complexityResults);
        
        return {
            complexityResults,
            optimalComplexity,
            overfittingScore
        };
    }

    /**
     * K-fold cross-validation
     */
    async kFoldCrossValidation(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            k: number;
            stratified?: boolean;
            shuffle?: boolean;
        }
    ): Promise<CrossValidationResult> {
        const { k, shuffle = true } = options;
        let data = [...marketData];
        
        if (shuffle) {
            data = this.shuffleArray(data);
        }
        
        const foldSize = Math.floor(data.length / k);
        const foldResults = [];
        
        for (let fold = 0; fold < k; fold++) {
            const testStart = fold * foldSize;
            const testEnd = fold === k - 1 ? data.length : (fold + 1) * foldSize;
            
            const testData = data.slice(testStart, testEnd);
            const trainData = [...data.slice(0, testStart), ...data.slice(testEnd)];
            
            console.log(`📁 Fold ${fold + 1}/${k}: Train ${trainData.length}, Test ${testData.length}`);
            
            // Tréning
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: 12,
                generations: 4,
                mutationRate: 0.1,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });
            
            const result = await evolutionFramework.evolve(trainData, initialBalance);
            
            // Validácia
            const validationPerformance = await fitnessEvaluator.evaluateFitness(
                result.bestIndividual,
                testData,
                initialBalance
            );
            
            foldResults.push({
                fold: fold + 1,
                trainScore: result.bestFitness.overallScore,
                validationScore: validationPerformance.overallScore,
                parameters: result.bestIndividual.getParameters()
            });
        }
        
        // Vypočítaj štatistiky
        const validationScores = foldResults.map(f => f.validationScore);
        const meanScore = validationScores.reduce((sum, score) => sum + score, 0) / validationScores.length;
        const variance = validationScores.reduce((sum, score) => sum + Math.pow(score - meanScore, 2), 0) / validationScores.length;
        const stdScore = Math.sqrt(variance);
        
        // 95% confidence interval
        const tValue = 2.262; // t-value for 95% CI with small sample
        const marginOfError = tValue * (stdScore / Math.sqrt(k));
        const confidenceInterval: [number, number] = [meanScore - marginOfError, meanScore + marginOfError];
        
        return {
            foldResults,
            meanScore,
            stdScore,
            confidenceInterval
        };
    }

    /**
     * Time series cross-validation
     */
    async timeSeriesCrossValidation(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            initialTrainSize: number;
            testSize: number;
            gap: number;
            maxSplits: number;
        }
    ): Promise<{
        splits: {
            split: number;
            trainStart: number;
            trainEnd: number;
            testStart: number;
            testEnd: number;
            score: number;
        }[];
        meanScore: number;
        temporalStability: number;
    }> {
        const { initialTrainSize, testSize, gap, maxSplits } = options;
        const splits = [];
        
        for (let i = 0; i < maxSplits; i++) {
            const trainStart = 0;
            const trainEnd = initialTrainSize + i * testSize;
            const testStart = trainEnd + gap;
            const testEnd = testStart + testSize;
            
            if (testEnd > marketData.length) break;
            
            const trainData = marketData.slice(trainStart, trainEnd);
            const testData = marketData.slice(testStart, testEnd);
            
            // Evolúcia na trénovacích dátach
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: 10,
                generations: 3,
                mutationRate: 0.1,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });
            
            const result = await evolutionFramework.evolve(trainData, initialBalance);
            
            // Test na out-of-sample dátach
            const testPerformance = await fitnessEvaluator.evaluateFitness(
                result.bestIndividual,
                testData,
                initialBalance
            );
            
            splits.push({
                split: i + 1,
                trainStart,
                trainEnd,
                testStart,
                testEnd,
                score: testPerformance.overallScore
            });
        }
        
        const scores = splits.map(s => s.score);
        const meanScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        
        // Vypočítaj temporálnu stabilitu (koeficient variácie)
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - meanScore, 2), 0) / scores.length;
        const stdDev = Math.sqrt(variance);
        const temporalStability = meanScore !== 0 ? 1 - (stdDev / Math.abs(meanScore)) : 0;
        
        return {
            splits,
            meanScore,
            temporalStability: Math.max(0, temporalStability)
        };
    }

    /**
     * Monte Carlo backtesting
     */
    async monteCarloBacktest(
        strategy: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            simulations: number;
            noiseLevel: number;
            bootstrapSamples?: boolean;
        }
    ): Promise<MonteCarloResult> {
        const { simulations, noiseLevel, bootstrapSamples = false } = options;
        const simulationResults = [];

        for (let sim = 0; sim < simulations; sim++) {
            let testData: EnhancedMarketData[];

            if (bootstrapSamples) {
                // Bootstrap sampling
                testData = this.bootstrapSample(marketData);
            } else {
                // Add noise to original data
                testData = this.addNoiseToData(marketData, noiseLevel);
            }

            const performance = await fitnessEvaluator.evaluateFitness(strategy, testData, initialBalance);

            simulationResults.push({
                simulation: sim + 1,
                score: performance.overallScore,
                parameters: strategy.getParameters()
            });
        }

        // Vypočítaj štatistiky
        const scores = simulationResults.map(s => s.score);
        const statistics = this.calculateStatistics(scores);
        const confidenceIntervals = this.calculateConfidenceIntervals(scores);

        return {
            simulations: simulationResults,
            statistics,
            confidenceIntervals
        };
    }

    /**
     * Test robustnosti voči šumu
     */
    async testRobustness(
        strategy: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            noiseLevels: number[];
            simulationsPerLevel: number;
        }
    ): Promise<{
        noiseLevels: {
            noiseLevel: number;
            meanScore: number;
            stdScore: number;
            simulations: number[];
        }[];
        robustnessScore: number;
    }> {
        const { noiseLevels, simulationsPerLevel } = options;
        const results = [];

        for (const noiseLevel of noiseLevels) {
            const levelResults = [];

            for (let sim = 0; sim < simulationsPerLevel; sim++) {
                const noisyData = this.addNoiseToData(marketData, noiseLevel);
                const performance = await fitnessEvaluator.evaluateFitness(strategy, noisyData, initialBalance);
                levelResults.push(performance.overallScore);
            }

            const meanScore = levelResults.reduce((sum, score) => sum + score, 0) / levelResults.length;
            const variance = levelResults.reduce((sum, score) => sum + Math.pow(score - meanScore, 2), 0) / levelResults.length;
            const stdScore = Math.sqrt(variance);

            results.push({
                noiseLevel,
                meanScore,
                stdScore,
                simulations: levelResults
            });
        }

        // Vypočítaj robustness score (pokles výkonnosti s rastúcim šumom)
        const baselineScore = results[0].meanScore;
        const finalScore = results[results.length - 1].meanScore;
        const robustnessScore = baselineScore > 0 ? Math.max(0, finalScore / baselineScore) : 0;

        return {
            noiseLevels: results,
            robustnessScore
        };
    }

    /**
     * Out-of-sample validácia
     */
    async outOfSampleValidation(
        trainData: EnhancedMarketData[],
        testData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options?: {
            evolutionConfig?: any;
        }
    ): Promise<{
        trainPerformance: FitnessResult;
        testPerformance: FitnessResult;
        generalizationGap: number;
        optimizedStrategy: StrategyGenome;
    }> {
        const evolutionConfig = options?.evolutionConfig || {
            populationSize: 15,
            generations: 5,
            mutationRate: 0.1,
            crossoverRate: 0.8,
            elitismRate: 0.2,
            tournamentSize: 3
        };

        // Optimalizuj na trénovacích dátach
        const evolutionFramework = new AgentEvolutionFramework(evolutionConfig);
        const result = await evolutionFramework.evolve(trainData, initialBalance);

        // Testuj na out-of-sample dátach
        const testPerformance = await fitnessEvaluator.evaluateFitness(
            result.bestIndividual,
            testData,
            initialBalance
        );

        const generalizationGap = result.bestFitness.overallScore - testPerformance.overallScore;

        return {
            trainPerformance: result.bestFitness,
            testPerformance,
            generalizationGap,
            optimizedStrategy: result.bestIndividual
        };
    }

    /**
     * Performance attribution analýza
     */
    async performanceAttribution(
        strategy: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<{
        totalReturn: number;
        factorContributions: { [factor: string]: number };
        parameterSensitivity: { [parameter: string]: number };
    }> {
        const basePerformance = await fitnessEvaluator.evaluateFitness(strategy, marketData, initialBalance);
        const totalReturn = basePerformance.profitability;

        // Analýza faktorov (zjednodušená implementácia)
        const factorContributions = {
            'Signal Quality': totalReturn * 0.4,
            'Risk Management': totalReturn * 0.3,
            'Market Timing': totalReturn * 0.2,
            'Other': totalReturn * 0.1
        };

        // Parameter sensitivity
        const parameters = strategy.getParameters();
        const parameterSensitivity: { [parameter: string]: number } = {};

        for (const [paramName, paramValue] of Object.entries(parameters)) {
            if (typeof paramValue === 'number') {
                // Test malej zmeny parametra
                const modifiedParams = { ...parameters };
                (modifiedParams as any)[paramName] = paramValue * 1.05; // +5%

                const modifiedStrategy = new StrategyGenome(modifiedParams);
                const modifiedPerformance = await fitnessEvaluator.evaluateFitness(
                    modifiedStrategy,
                    marketData.slice(0, 100), // Menší dataset pre rýchlosť
                    initialBalance
                );

                const sensitivity = Math.abs(modifiedPerformance.overallScore - basePerformance.overallScore) / basePerformance.overallScore;
                parameterSensitivity[paramName] = sensitivity;
            }
        }

        return {
            totalReturn,
            factorContributions,
            parameterSensitivity
        };
    }

    /**
     * Sensitivity analýza
     */
    async sensitivityAnalysis(
        strategy: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            parameters: string[];
            perturbationRange: number;
        }
    ): Promise<{
        parameterSensitivities: { [parameter: string]: number };
        mostSensitiveParameter: string;
        stabilityScore: number;
    }> {
        const baseParams = strategy.getParameters();
        const basePerformance = await fitnessEvaluator.evaluateFitness(strategy, marketData, initialBalance);

        const sensitivities: { [parameter: string]: number } = {};

        for (const paramName of options.parameters) {
            const paramValue = (baseParams as any)[paramName];

            if (typeof paramValue === 'number') {
                const perturbation = paramValue * options.perturbationRange;

                // Test pozitívnej zmeny
                const upParams = { ...baseParams };
                (upParams as any)[paramName] = paramValue + perturbation;
                const upStrategy = new StrategyGenome(upParams);
                const upPerformance = await fitnessEvaluator.evaluateFitness(upStrategy, marketData, initialBalance);

                // Test negatívnej zmeny
                const downParams = { ...baseParams };
                (downParams as any)[paramName] = paramValue - perturbation;
                const downStrategy = new StrategyGenome(downParams);
                const downPerformance = await fitnessEvaluator.evaluateFitness(downStrategy, marketData, initialBalance);

                // Vypočítaj citlivosť
                const maxChange = Math.max(
                    Math.abs(upPerformance.overallScore - basePerformance.overallScore),
                    Math.abs(downPerformance.overallScore - basePerformance.overallScore)
                );

                sensitivities[paramName] = maxChange / basePerformance.overallScore;
            }
        }

        // Nájdi najcitlivejší parameter
        const mostSensitiveParameter = Object.entries(sensitivities)
            .reduce((max, [param, sensitivity]) => sensitivity > max.sensitivity ? { param, sensitivity } : max,
                    { param: '', sensitivity: 0 }).param;

        // Vypočítaj stability score (inverzná hodnota priemernej citlivosti)
        const avgSensitivity = Object.values(sensitivities).reduce((sum, s) => sum + s, 0) / Object.values(sensitivities).length;
        const stabilityScore = 1 / (1 + avgSensitivity);

        return {
            parameterSensitivities: sensitivities,
            mostSensitiveParameter,
            stabilityScore
        };
    }

    /**
     * Stress testing
     */
    async stressTesting(
        strategy: StrategyGenome,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        options: {
            stressScenarios: {
                name: string;
                volatilityMultiplier?: number;
                trendShift?: number;
                duration?: number;
                volumeReduction?: number;
                spreadIncrease?: number;
            }[];
        }
    ): Promise<{
        scenarios: {
            name: string;
            performance: FitnessResult;
            stressLevel: number;
        }[];
        overallStressScore: number;
    }> {
        const scenarioResults = [];

        for (const scenario of options.stressScenarios) {
            // Aplikuj stress na dáta
            const stressedData = this.applyStressScenario(marketData, scenario);

            // Vyhodnoť výkonnosť
            const performance = await fitnessEvaluator.evaluateFitness(strategy, stressedData, initialBalance);

            // Vypočítaj úroveň stresu
            const stressLevel = this.calculateStressLevel(scenario);

            scenarioResults.push({
                name: scenario.name,
                performance,
                stressLevel
            });
        }

        // Vypočítaj celkové stress score
        const avgPerformance = scenarioResults.reduce((sum, s) => sum + s.performance.overallScore, 0) / scenarioResults.length;
        const overallStressScore = Math.max(0, avgPerformance / 100); // Normalizované na 0-1

        return {
            scenarios: scenarioResults,
            overallStressScore
        };
    }

    // Pomocné metódy
    private calculateOverallPerformance(periods: any[]): FitnessResult {
        const avgWinRate = periods.reduce((sum, p) => sum + p.testPerformance.winRate, 0) / periods.length;
        const avgProfitFactor = periods.reduce((sum, p) => sum + p.testPerformance.profitFactor, 0) / periods.length;
        const avgOverallScore = periods.reduce((sum, p) => sum + p.testPerformance.overallScore, 0) / periods.length;

        return {
            winRate: avgWinRate,
            profitFactor: avgProfitFactor,
            overallScore: avgOverallScore,
            // Ostatné vlastnosti s defaultnými hodnotami
            sharpeRatio: 0,
            maxDrawdown: 0,
            totalTrades: 0,
            totalProfit: 0,
            averageWin: 0,
            averageLoss: 0,
            consecutiveWins: 0,
            consecutiveLosses: 0,
            profitability: 0,
            volatility: 0,
            calmarRatio: 0,
            sortinoRatio: 0,
            tradesPerDay: 0,
            winningDays: 0,
            losingDays: 0,
            maxDailyProfit: 0,
            maxDailyLoss: 0,
            recoveryFactor: 0,
            expectancy: 0
        };
    }

    private calculateStabilityMetrics(periods: any[]): { consistency: number; volatility: number; maxDrawdown: number } {
        const scores = periods.map(p => p.testPerformance.overallScore);
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const volatility = Math.sqrt(variance);

        const consistency = mean > 0 ? 1 - (volatility / mean) : 0;
        const maxDrawdown = Math.max(...periods.map(p => p.testPerformance.maxDrawdown));

        return {
            consistency: Math.max(0, consistency),
            volatility,
            maxDrawdown
        };
    }

    private findOptimalComplexity(results: any[]): number {
        // Nájdi komplexnosť s najlepším pomerom výkonnosti a generalizácie
        let bestComplexity = 1;
        let bestScore = -Infinity;

        for (const result of results) {
            // Skóre kombinuje test výkonnosť a penalizuje veľký gap
            const score = result.testScore - Math.abs(result.gap) * 0.5;
            if (score > bestScore) {
                bestScore = score;
                bestComplexity = result.complexity;
            }
        }

        return bestComplexity;
    }

    private calculateOverfittingScore(results: any[]): number {
        // Vypočítaj priemerný gap medzi train a test
        const avgGap = results.reduce((sum, r) => sum + Math.abs(r.gap), 0) / results.length;
        const avgTestScore = results.reduce((sum, r) => sum + r.testScore, 0) / results.length;

        return avgTestScore > 0 ? avgGap / avgTestScore : 1;
    }

    private shuffleArray<T>(array: T[]): T[] {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    private addNoiseToData(data: EnhancedMarketData[], noiseLevel: number): EnhancedMarketData[] {
        return data.map(point => ({
            ...point,
            price: point.price * (1 + (Math.random() - 0.5) * noiseLevel * 2),
            volume: point.volume * (1 + (Math.random() - 0.5) * noiseLevel)
        }));
    }

    private bootstrapSample(data: EnhancedMarketData[]): EnhancedMarketData[] {
        const sample = [];
        for (let i = 0; i < data.length; i++) {
            const randomIndex = Math.floor(Math.random() * data.length);
            sample.push(data[randomIndex]);
        }
        return sample;
    }

    private calculateStatistics(scores: number[]): {
        mean: number;
        std: number;
        min: number;
        max: number;
        percentiles: { [key: string]: number };
    } {
        const sorted = [...scores].sort((a, b) => a - b);
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const std = Math.sqrt(variance);

        const percentiles = {
            '5%': sorted[Math.floor(sorted.length * 0.05)],
            '25%': sorted[Math.floor(sorted.length * 0.25)],
            '50%': sorted[Math.floor(sorted.length * 0.50)],
            '75%': sorted[Math.floor(sorted.length * 0.75)],
            '95%': sorted[Math.floor(sorted.length * 0.95)]
        };

        return {
            mean,
            std,
            min: Math.min(...scores),
            max: Math.max(...scores),
            percentiles
        };
    }

    private calculateConfidenceIntervals(scores: number[]): { [key: string]: [number, number] } {
        const sorted = [...scores].sort((a, b) => a - b);
        const n = sorted.length;

        return {
            '90%': [sorted[Math.floor(n * 0.05)], sorted[Math.floor(n * 0.95)]],
            '95%': [sorted[Math.floor(n * 0.025)], sorted[Math.floor(n * 0.975)]],
            '99%': [sorted[Math.floor(n * 0.005)], sorted[Math.floor(n * 0.995)]]
        };
    }

    private applyStressScenario(data: EnhancedMarketData[], scenario: any): EnhancedMarketData[] {
        return data.map((point, index) => {
            let modifiedPoint = { ...point };

            if (scenario.volatilityMultiplier) {
                const noise = (Math.random() - 0.5) * scenario.volatilityMultiplier * 0.01;
                modifiedPoint.price *= (1 + noise);
            }

            if (scenario.trendShift) {
                modifiedPoint.price *= (1 + scenario.trendShift * (index / data.length));
            }

            if (scenario.volumeReduction) {
                modifiedPoint.volume *= (1 - scenario.volumeReduction);
            }

            return modifiedPoint;
        });
    }

    private calculateStressLevel(scenario: any): number {
        let stressLevel = 0;

        if (scenario.volatilityMultiplier) stressLevel += scenario.volatilityMultiplier * 0.3;
        if (scenario.trendShift) stressLevel += Math.abs(scenario.trendShift) * 2;
        if (scenario.volumeReduction) stressLevel += scenario.volumeReduction;
        if (scenario.spreadIncrease) stressLevel += scenario.spreadIncrease * 0.2;

        return Math.min(1, stressLevel);
    }
}
