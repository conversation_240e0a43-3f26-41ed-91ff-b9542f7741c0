// tests/evolution/agent-evolution.test.ts
// Komplexný test suite pre evolúciu trading agentov

import { AgentEvolutionFramework } from './framework/AgentEvolutionFramework';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { GeneticOperators } from './framework/GeneticOperators';
import { StrategyGenome } from './framework/StrategyGenome';
import { SmartStrategy } from '../../src/agent/smartStrategy';
import { OptimizedSmartStrategy } from '../../src/agent/optimizedSmartStrategy';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Agent Evolution Framework', () => {
    let evolutionFramework: AgentEvolutionFramework;
    let fitnessEvaluator: FitnessEvaluator;
    let geneticOperators: GeneticOperators;

    beforeEach(() => {
        evolutionFramework = new AgentEvolutionFramework({
            populationSize: 20,
            generations: 10,
            mutationRate: 0.1,
            crossoverRate: 0.8,
            elitismRate: 0.2,
            tournamentSize: 3
        });
        
        fitnessEvaluator = new FitnessEvaluator();
        geneticOperators = new GeneticOperators();
    });

    describe('Framework Initialization', () => {
        it('should initialize with correct parameters', () => {
            expect(evolutionFramework).toBeInstanceOf(AgentEvolutionFramework);
            expect(evolutionFramework.getPopulationSize()).toBe(20);
            expect(evolutionFramework.getGenerations()).toBe(10);
        });

        it('should create initial population', async () => {
            const population = await evolutionFramework.createInitialPopulation();
            expect(population).toHaveLength(20);
            expect(population[0]).toBeInstanceOf(StrategyGenome);
        });
    });

    describe('Fitness Evaluation', () => {
        it('should evaluate strategy fitness correctly', async () => {
            const genome = new StrategyGenome({
                minConfidence: 75,
                minSignalStrength: 70,
                maxRiskLevel: 'medium',
                trendConfirmationPeriod: 5,
                volatilityThreshold: 0.02
            });

            const fitness = await fitnessEvaluator.evaluateFitness(
                genome, 
                testScenarios.optimalConditions,
                10000 // initial balance
            );

            expect(fitness).toHaveProperty('winRate');
            expect(fitness).toHaveProperty('profitFactor');
            expect(fitness).toHaveProperty('sharpeRatio');
            expect(fitness).toHaveProperty('maxDrawdown');
            expect(fitness).toHaveProperty('totalTrades');
            expect(fitness).toHaveProperty('overallScore');

            expect(fitness.winRate).toBeGreaterThanOrEqual(0);
            expect(fitness.winRate).toBeLessThanOrEqual(1);
            expect(fitness.overallScore).toBeGreaterThan(0);
        });

        it('should handle multiple market scenarios', async () => {
            const genome = new StrategyGenome();
            
            const scenarios = [
                testScenarios.optimalConditions,
                testScenarios.volatileMarket,
                testScenarios.trendingMarket
            ];

            for (const scenario of scenarios) {
                const fitness = await fitnessEvaluator.evaluateFitness(
                    genome, 
                    scenario,
                    10000
                );
                
                expect(fitness.overallScore).toBeGreaterThan(0);
                expect(fitness.totalTrades).toBeGreaterThanOrEqual(0);
            }
        });
    });

    describe('Genetic Operations', () => {
        it('should perform crossover correctly', () => {
            const parent1 = new StrategyGenome({
                minConfidence: 75,
                minSignalStrength: 70,
                maxRiskLevel: 'medium',
                trendConfirmationPeriod: 5
            });

            const parent2 = new StrategyGenome({
                minConfidence: 85,
                minSignalStrength: 80,
                maxRiskLevel: 'low',
                trendConfirmationPeriod: 3
            });

            const offspring = geneticOperators.crossover(parent1, parent2);
            
            expect(offspring).toHaveLength(2);
            expect(offspring[0]).toBeInstanceOf(StrategyGenome);
            expect(offspring[1]).toBeInstanceOf(StrategyGenome);
            
            // Check that offspring have mixed parameters
            const child1Params = offspring[0].getParameters();
            const child2Params = offspring[1].getParameters();
            
            expect(child1Params.minConfidence).toBeGreaterThanOrEqual(75);
            expect(child1Params.minConfidence).toBeLessThanOrEqual(85);
        });

        it('should perform mutation correctly', () => {
            const original = new StrategyGenome({
                minConfidence: 75,
                minSignalStrength: 70,
                volatilityThreshold: 0.02
            });

            const mutated = geneticOperators.mutate(original, 1.0); // 100% mutation rate
            
            expect(mutated).toBeInstanceOf(StrategyGenome);
            
            const originalParams = original.getParameters();
            const mutatedParams = mutated.getParameters();
            
            // At least some parameters should be different
            const paramKeys = Object.keys(originalParams);
            let differences = 0;
            
            paramKeys.forEach(key => {
                if (originalParams[key] !== mutatedParams[key]) {
                    differences++;
                }
            });
            
            expect(differences).toBeGreaterThan(0);
        });

        it('should perform selection correctly', async () => {
            const population = await evolutionFramework.createInitialPopulation();
            
            // Evaluate fitness for all individuals
            const evaluatedPopulation = [];
            for (const individual of population) {
                const fitness = await fitnessEvaluator.evaluateFitness(
                    individual,
                    testScenarios.optimalConditions.slice(0, 50), // Smaller dataset for speed
                    10000
                );
                evaluatedPopulation.push({ individual, fitness });
            }
            
            const selected = geneticOperators.tournamentSelection(
                evaluatedPopulation,
                10, // select 10 individuals
                3   // tournament size
            );
            
            expect(selected).toHaveLength(10);
            expect(selected[0]).toHaveProperty('individual');
            expect(selected[0]).toHaveProperty('fitness');
        });
    });

    describe('Evolution Process', () => {
        it('should run complete evolution cycle', async () => {
            // Use smaller parameters for faster testing
            const quickEvolution = new AgentEvolutionFramework({
                populationSize: 10,
                generations: 3,
                mutationRate: 0.2,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });

            const result = await quickEvolution.evolve(
                testScenarios.optimalConditions.slice(0, 100), // Smaller dataset
                10000 // initial balance
            );

            expect(result).toHaveProperty('bestIndividual');
            expect(result).toHaveProperty('bestFitness');
            expect(result).toHaveProperty('generationStats');
            expect(result).toHaveProperty('convergenceData');

            expect(result.bestFitness.overallScore).toBeGreaterThan(0);
            expect(result.generationStats).toHaveLength(3); // 3 generations
            
            // Check that fitness improved over generations
            const firstGenBest = result.generationStats[0].bestFitness;
            const lastGenBest = result.generationStats[2].bestFitness;
            
            // Evolution should maintain or improve fitness
            expect(lastGenBest).toBeGreaterThanOrEqual(firstGenBest * 0.9); // Allow 10% tolerance
        });

        it('should maintain diversity in population', async () => {
            const population = await evolutionFramework.createInitialPopulation();
            
            // Check that individuals have different parameters
            const parameterSets = population.map(individual => 
                JSON.stringify(individual.getParameters())
            );
            
            const uniqueParameterSets = new Set(parameterSets);
            
            // Should have good diversity (at least 80% unique)
            expect(uniqueParameterSets.size).toBeGreaterThanOrEqual(population.length * 0.8);
        });
    });

    describe('Performance Benchmarks', () => {
        it('should benchmark against baseline strategies', async () => {
            const baselineStrategy = new SmartStrategy();
            const optimizedStrategy = new OptimizedSmartStrategy();
            
            // Create genomes from existing strategies
            const baselineGenome = StrategyGenome.fromStrategy(baselineStrategy);
            const optimizedGenome = StrategyGenome.fromStrategy(optimizedStrategy);
            
            const baselineFitness = await fitnessEvaluator.evaluateFitness(
                baselineGenome,
                testScenarios.optimalConditions.slice(0, 100),
                10000
            );
            
            const optimizedFitness = await fitnessEvaluator.evaluateFitness(
                optimizedGenome,
                testScenarios.optimalConditions.slice(0, 100),
                10000
            );
            
            expect(baselineFitness.overallScore).toBeGreaterThan(0);
            expect(optimizedFitness.overallScore).toBeGreaterThan(0);
            
            // Optimized should generally perform better
            expect(optimizedFitness.overallScore).toBeGreaterThanOrEqual(
                baselineFitness.overallScore * 0.9 // Allow some tolerance
            );
        });
    });

    describe('Multi-Objective Optimization', () => {
        it('should optimize for multiple objectives', async () => {
            const multiObjectiveEvolution = new AgentEvolutionFramework({
                populationSize: 15,
                generations: 5,
                mutationRate: 0.15,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3,
                objectives: ['winRate', 'profitFactor', 'sharpeRatio', 'maxDrawdown']
            });

            const result = await multiObjectiveEvolution.evolve(
                testScenarios.optimalConditions.slice(0, 100),
                10000
            );

            expect(result.bestFitness).toHaveProperty('winRate');
            expect(result.bestFitness).toHaveProperty('profitFactor');
            expect(result.bestFitness).toHaveProperty('sharpeRatio');
            expect(result.bestFitness).toHaveProperty('maxDrawdown');
            
            // Check that we have a balanced solution
            expect(result.bestFitness.winRate).toBeGreaterThan(0.6); // At least 60% win rate
            expect(result.bestFitness.profitFactor).toBeGreaterThan(1.0); // Profitable
            expect(result.bestFitness.maxDrawdown).toBeLessThan(0.2); // Max 20% drawdown
        });
    });
});
