// tests/agent/smartStrategy.test.ts

import { SmartStrategy } from '../../src/agent/smartStrategy';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('SmartStrategy', () => {
    let strategy: SmartStrategy;

    beforeEach(() => {
        strategy = new SmartStrategy();
    });

    it('should be instantiated correctly', () => {
        expect(strategy).toBeInstanceOf(SmartStrategy);
    });

    it('should handle market data and make decisions', () => {
        const marketData = {
            price: 1.2000,
            high: 1.2010,
            low: 1.1990,
            volume: 1000
        };

        // Feed some data first
        for (let i = 0; i < 30; i++) {
            const decision = strategy.decideAction({
                ...marketData,
                price: marketData.price + (i * 0.0001)
            });
            // Decision can be null or a valid trade order
            if (decision) {
                expect(['buy', 'sell']).toContain(decision.action);
                expect(decision.amount).toBeGreaterThan(0);
            }
        }
    });

    it('should work with test scenarios', () => {
        const scenario = testScenarios.optimalConditions.slice(0, 20);
        let decisions = 0;

        scenario.forEach(dataPoint => {
            const decision = strategy.decideAction(dataPoint);
            if (decision) {
                decisions++;
                expect(['buy', 'sell']).toContain(decision.action);
                expect(decision.amount).toBeGreaterThan(0);
            }
        });

        // Should make at least some decisions with optimal conditions
        expect(decisions).toBeGreaterThanOrEqual(0);
    });
});