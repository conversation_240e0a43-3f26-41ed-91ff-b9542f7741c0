// src/utils/config.ts

import config from '../../config/config.json';

export interface AppConfig {
    initialBalance: number;
    apiUrl: string;
    apiSymbol: string;
    accessKey: string;
}

const loadConfig = (): AppConfig => {
    // Simple validation
    if (typeof config.initialBalance !== 'number' || typeof config.apiUrl !== 'string' || typeof config.apiSymbol !== 'string' || typeof config.accessKey !== 'string') {
        throw new Error('Invalid configuration format.');
    }
    return config;
};

export const appConfig = loadConfig();