// tests/evolution/framework/GeneticOperators.ts
// Genetické operátory pre evolúciu trading stratégií

import { StrategyGenome } from './StrategyGenome';
import { FitnessResult } from './FitnessEvaluator';

export interface EvaluatedIndividual {
    individual: StrategyGenome;
    fitness: FitnessResult;
}

export interface SelectionConfig {
    method: 'tournament' | 'roulette' | 'rank' | 'elite';
    tournamentSize?: number;
    eliteRatio?: number;
}

export interface CrossoverConfig {
    method: 'uniform' | 'single_point' | 'two_point' | 'arithmetic';
    crossoverRate: number;
}

export interface MutationConfig {
    method: 'gaussian' | 'uniform' | 'adaptive';
    mutationRate: number;
    mutationStrength?: number;
    adaptiveRate?: boolean;
}

export class GeneticOperators {
    private selectionConfig: SelectionConfig = {
        method: 'tournament',
        tournamentSize: 3
    };

    private crossoverConfig: CrossoverConfig = {
        method: 'uniform',
        crossoverRate: 0.8
    };

    private mutationConfig: MutationConfig = {
        method: 'gaussian',
        mutationRate: 0.1,
        mutationStrength: 0.1
    };

    /**
     * Tournament selection
     */
    tournamentSelection(
        population: EvaluatedIndividual[],
        selectionSize: number,
        tournamentSize: number = 3
    ): EvaluatedIndividual[] {
        const selected: EvaluatedIndividual[] = [];
        
        for (let i = 0; i < selectionSize; i++) {
            const tournament: EvaluatedIndividual[] = [];
            
            // Vyber náhodných jedincov do turnaja
            for (let j = 0; j < tournamentSize; j++) {
                const randomIndex = Math.floor(Math.random() * population.length);
                tournament.push(population[randomIndex]);
            }
            
            // Nájdi najlepšieho v turnaji
            const winner = tournament.reduce((best, current) => 
                current.fitness.overallScore > best.fitness.overallScore ? current : best
            );
            
            selected.push(winner);
        }
        
        return selected;
    }

    /**
     * Roulette wheel selection
     */
    rouletteSelection(
        population: EvaluatedIndividual[],
        selectionSize: number
    ): EvaluatedIndividual[] {
        const selected: EvaluatedIndividual[] = [];
        
        // Vypočítaj celkovú fitness
        const totalFitness = population.reduce((sum, individual) => 
            sum + Math.max(0, individual.fitness.overallScore), 0
        );
        
        if (totalFitness === 0) {
            // Ak je celková fitness 0, vyber náhodne
            for (let i = 0; i < selectionSize; i++) {
                const randomIndex = Math.floor(Math.random() * population.length);
                selected.push(population[randomIndex]);
            }
            return selected;
        }
        
        for (let i = 0; i < selectionSize; i++) {
            const randomValue = Math.random() * totalFitness;
            let cumulativeFitness = 0;
            
            for (const individual of population) {
                cumulativeFitness += Math.max(0, individual.fitness.overallScore);
                if (cumulativeFitness >= randomValue) {
                    selected.push(individual);
                    break;
                }
            }
        }
        
        return selected;
    }

    /**
     * Rank-based selection
     */
    rankSelection(
        population: EvaluatedIndividual[],
        selectionSize: number
    ): EvaluatedIndividual[] {
        // Zoraď populáciu podľa fitness
        const sortedPopulation = [...population].sort((a, b) => 
            b.fitness.overallScore - a.fitness.overallScore
        );
        
        // Priraď ranky (najlepší = populácia.length, najhorší = 1)
        const rankedPopulation = sortedPopulation.map((individual, index) => ({
            individual,
            rank: population.length - index
        }));
        
        const totalRank = rankedPopulation.reduce((sum, item) => sum + item.rank, 0);
        const selected: EvaluatedIndividual[] = [];
        
        for (let i = 0; i < selectionSize; i++) {
            const randomValue = Math.random() * totalRank;
            let cumulativeRank = 0;
            
            for (const item of rankedPopulation) {
                cumulativeRank += item.rank;
                if (cumulativeRank >= randomValue) {
                    selected.push(item.individual);
                    break;
                }
            }
        }
        
        return selected;
    }

    /**
     * Elite selection
     */
    eliteSelection(
        population: EvaluatedIndividual[],
        selectionSize: number
    ): EvaluatedIndividual[] {
        return [...population]
            .sort((a, b) => b.fitness.overallScore - a.fitness.overallScore)
            .slice(0, selectionSize);
    }

    /**
     * Uniform crossover
     */
    uniformCrossover(parent1: StrategyGenome, parent2: StrategyGenome): [StrategyGenome, StrategyGenome] {
        const params1 = parent1.getParameters();
        const params2 = parent2.getParameters();
        
        const child1Params = { ...params1 };
        const child2Params = { ...params2 };
        
        // Pre každý parameter, vymeň s 50% pravdepodobnosťou
        Object.keys(params1).forEach(key => {
            if (Math.random() < 0.5) {
                (child1Params as any)[key] = (params2 as any)[key];
                (child2Params as any)[key] = (params1 as any)[key];
            }
        });
        
        return [
            new StrategyGenome(child1Params),
            new StrategyGenome(child2Params)
        ];
    }

    /**
     * Single-point crossover
     */
    singlePointCrossover(parent1: StrategyGenome, parent2: StrategyGenome): [StrategyGenome, StrategyGenome] {
        const params1 = parent1.getParameters();
        const params2 = parent2.getParameters();
        
        const paramKeys = Object.keys(params1);
        const crossoverPoint = Math.floor(Math.random() * paramKeys.length);
        
        const child1Params = { ...params1 };
        const child2Params = { ...params2 };
        
        // Vymeň parametre za crossover bodom
        for (let i = crossoverPoint; i < paramKeys.length; i++) {
            const key = paramKeys[i];
            (child1Params as any)[key] = (params2 as any)[key];
            (child2Params as any)[key] = (params1 as any)[key];
        }
        
        return [
            new StrategyGenome(child1Params),
            new StrategyGenome(child2Params)
        ];
    }

    /**
     * Arithmetic crossover (pre numerické parametre)
     */
    arithmeticCrossover(parent1: StrategyGenome, parent2: StrategyGenome): [StrategyGenome, StrategyGenome] {
        const params1 = parent1.getParameters();
        const params2 = parent2.getParameters();
        
        const alpha = Math.random(); // Váha pre kríženie
        
        const child1Params = { ...params1 };
        const child2Params = { ...params2 };
        
        // Numerické parametre
        const numericParams = [
            'minConfidence', 'minSignalStrength', 'trendConfirmationPeriod',
            'volatilityThreshold', 'maxDailyTrades', 'maxConsecutiveLosses',
            'profitTarget', 'stopLoss'
        ];
        
        numericParams.forEach(param => {
            if (typeof (params1 as any)[param] === 'number' && typeof (params2 as any)[param] === 'number') {
                (child1Params as any)[param] = alpha * (params1 as any)[param] + (1 - alpha) * (params2 as any)[param];
                (child2Params as any)[param] = (1 - alpha) * (params1 as any)[param] + alpha * (params2 as any)[param];
            }
        });
        
        return [
            new StrategyGenome(child1Params),
            new StrategyGenome(child2Params)
        ];
    }

    /**
     * Hlavná crossover metóda
     */
    crossover(parent1: StrategyGenome, parent2: StrategyGenome): [StrategyGenome, StrategyGenome] {
        switch (this.crossoverConfig.method) {
            case 'uniform':
                return this.uniformCrossover(parent1, parent2);
            case 'single_point':
                return this.singlePointCrossover(parent1, parent2);
            case 'arithmetic':
                return this.arithmeticCrossover(parent1, parent2);
            default:
                return this.uniformCrossover(parent1, parent2);
        }
    }

    /**
     * Gaussian mutation
     */
    gaussianMutation(genome: StrategyGenome, mutationRate: number): StrategyGenome {
        return genome.mutate(mutationRate);
    }

    /**
     * Uniform mutation
     */
    uniformMutation(genome: StrategyGenome, mutationRate: number): StrategyGenome {
        const mutated = genome.clone();
        const params = mutated.getParameters();
        
        // Mutuj každý parameter s danou pravdepodobnosťou
        Object.keys(params).forEach(key => {
            if (Math.random() < mutationRate) {
                const param = (params as any)[key];
                
                if (typeof param === 'number') {
                    // Numerický parameter - pridaj náhodný šum
                    const range = this.getParameterRange(key);
                    const noise = (Math.random() - 0.5) * range.range * 0.1; // 10% z rozsahu
                    (params as any)[key] = Math.max(range.min, Math.min(range.max, param + noise));
                } else if (typeof param === 'boolean') {
                    // Boolean parameter - flip s malou pravdepodobnosťou
                    if (Math.random() < 0.1) {
                        (params as any)[key] = !param;
                    }
                } else if (typeof param === 'string') {
                    // String parameter (napr. maxRiskLevel)
                    if (key === 'maxRiskLevel') {
                        const levels = ['low', 'medium', 'high'];
                        (params as any)[key] = levels[Math.floor(Math.random() * levels.length)];
                    }
                }
            }
        });
        
        mutated.setParameters(params);
        return mutated;
    }

    /**
     * Adaptive mutation - mení silu mutácie na základe diverzity populácie
     */
    adaptiveMutation(
        genome: StrategyGenome, 
        mutationRate: number,
        populationDiversity: number
    ): StrategyGenome {
        // Ak je diverzita nízka, zvýš mutáciu
        const adaptedRate = populationDiversity < 0.1 
            ? mutationRate * 2 
            : mutationRate;
        
        return this.gaussianMutation(genome, adaptedRate);
    }

    /**
     * Hlavná mutation metóda
     */
    mutate(genome: StrategyGenome, mutationRate: number): StrategyGenome {
        switch (this.mutationConfig.method) {
            case 'gaussian':
                return this.gaussianMutation(genome, mutationRate);
            case 'uniform':
                return this.uniformMutation(genome, mutationRate);
            case 'adaptive':
                return this.adaptiveMutation(genome, mutationRate, 0.5); // Default diversity
            default:
                return this.gaussianMutation(genome, mutationRate);
        }
    }

    /**
     * Vypočíta diverzitu populácie
     */
    calculatePopulationDiversity(population: StrategyGenome[]): number {
        if (population.length < 2) return 1.0;
        
        let totalDistance = 0;
        let comparisons = 0;
        
        for (let i = 0; i < population.length; i++) {
            for (let j = i + 1; j < population.length; j++) {
                totalDistance += population[i].distance(population[j]);
                comparisons++;
            }
        }
        
        return comparisons > 0 ? totalDistance / comparisons : 0;
    }

    /**
     * Vráti rozsah pre daný parameter
     */
    private getParameterRange(paramName: string): { min: number; max: number; range: number } {
        const ranges: { [key: string]: { min: number; max: number } } = {
            minConfidence: { min: 60, max: 95 },
            minSignalStrength: { min: 50, max: 90 },
            trendConfirmationPeriod: { min: 3, max: 10 },
            volatilityThreshold: { min: 0.01, max: 0.05 },
            maxDailyTrades: { min: 3, max: 15 },
            maxConsecutiveLosses: { min: 2, max: 5 },
            profitTarget: { min: 1.5, max: 3.0 },
            stopLoss: { min: 0.5, max: 1.5 }
        };
        
        const range = ranges[paramName] || { min: 0, max: 1 };
        return { ...range, range: range.max - range.min };
    }

    // Konfiguračné metódy
    setSelectionConfig(config: Partial<SelectionConfig>): void {
        this.selectionConfig = { ...this.selectionConfig, ...config };
    }

    setCrossoverConfig(config: Partial<CrossoverConfig>): void {
        this.crossoverConfig = { ...this.crossoverConfig, ...config };
    }

    setMutationConfig(config: Partial<MutationConfig>): void {
        this.mutationConfig = { ...this.mutationConfig, ...config };
    }
}
