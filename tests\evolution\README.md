# Agent Evolution Test Suite

Komplexný test suite pre evolúciu trading agentov s pokročilými genetickými algoritmami, optimalizáciou parametrov a backtestingom.

## 🎯 Prehľad

Tento test suite poskytuje robustný framework pre:
- **Evolúciu trading stratégií** pomocou genetických algoritmov
- **Optimalizáciu parametrov** (Grid Search, Bayesian Optimization)
- **Multi-strategy evolúciu** a ensemble learning
- **Komplexný backtesting** s walk-forward analýzou
- **Performance benchmarking** a stress testing

## 📁 Štruktúra

```
tests/evolution/
├── README.md                           # Tento súbor
├── run-evolution-tests.ts             # Hlavný test runner
├── agent-evolution.test.ts            # Základné evolučné testy
├── genetic-algorithms.test.ts         # Genetické algoritmy
├── performance-benchmarks.test.ts     # Performance benchmarking
├── parameter-optimization.test.ts     # Optimalizácia parametrov
├── multi-strategy-evolution.test.ts   # Multi-strategy evolúcia
├── backtesting-evolution.test.ts      # Backtesting framework
└── framework/                         # Core framework classes
    ├── AgentEvolutionFramework.ts     # Hlavný evolučný framework
    ├── StrategyGenome.ts              # Reprezentácia genómu stratégie
    ├── FitnessEvaluator.ts            # Vyhodnocovanie fitness
    ├── GeneticOperators.ts            # Genetické operátory
    ├── ParameterOptimizer.ts          # Parameter optimization
    ├── GridSearchOptimizer.ts         # Grid search
    ├── BayesianOptimizer.ts           # Bayesian optimization
    ├── MultiStrategyEvolution.ts      # Multi-strategy framework
    └── BacktestingEvolution.ts        # Backtesting framework
```

## 🚀 Spustenie testov

### Základné použitie

```bash
# Spusti všetky evolučné testy
npm run test:evolution

# Spusti len vysokoprioritné testy (rýchle)
npm run test:evolution:quick

# Spusti testy paralelne (rýchlejšie)
npm run test:evolution:parallel
```

### Špecifické test suites

```bash
# Core evolution framework
npm run test:evolution:core

# Genetické algoritmy
npm run test:evolution:genetic

# Performance benchmarking
npm run test:evolution:benchmarks

# Parameter optimization
npm run test:evolution:optimization

# Multi-strategy evolution
npm run test:evolution:multi

# Backtesting framework
npm run test:evolution:backtest
```

### Pokročilé možnosti

```bash
# Spusti len testy s vysokou prioritou
npm run test:evolution -- --priority high

# Spusti špecifické testy
npm run test:evolution -- --specific "genetic,benchmark"

# Nastav timeout (v minútach)
npm run test:evolution -- --timeout 45

# Zobraz help
npm run test:evolution -- --help
```

## 📊 Test Suites

### 1. Core Evolution Framework (`agent-evolution.test.ts`)
- **Priorita**: High
- **Čas**: ~5 minút
- **Popis**: Základné testy evolučného frameworku, fitness evaluácie a genetických operátorov

**Testuje**:
- Inicializáciu evolučného frameworku
- Vytvorenie počiatočnej populácie
- Fitness evaluáciu stratégií
- Základné genetické operácie
- Evolučný cyklus

### 2. Genetic Algorithms (`genetic-algorithms.test.ts`)
- **Priorita**: High
- **Čas**: ~3 minúty
- **Popis**: Detailné testy genetických algoritmov

**Testuje**:
- Tournament, roulette, rank a elite selection
- Uniform, single-point a arithmetic crossover
- Gaussian, uniform a adaptive mutation
- Population diversity
- Konfiguráciu genetických operátorov

### 3. Performance Benchmarks (`performance-benchmarks.test.ts`)
- **Priorita**: Medium
- **Čas**: ~8 minút
- **Popis**: Benchmarking výkonnosti stratégií

**Testuje**:
- Baseline strategy performance (SmartStrategy vs OptimizedSmartStrategy)
- Performance across different market conditions
- Parameter sensitivity analysis
- Evolution speed and convergence
- Stress testing under extreme conditions

### 4. Parameter Optimization (`parameter-optimization.test.ts`)
- **Priorita**: Medium
- **Čas**: ~10 minút
- **Popis**: Grid search, Bayesian optimalizácia a hyperparameter tuning

**Testuje**:
- Grid search optimization
- Bayesian optimization s rôznymi acquisition functions
- Multi-objective optimization
- Hyperparameter tuning
- Adaptive parameter optimization

### 5. Multi-Strategy Evolution (`multi-strategy-evolution.test.ts`)
- **Priorita**: Low
- **Čas**: ~12 minút
- **Popis**: Evolúcia viacerých stratégií súčasne

**Testuje**:
- Evolution rôznych typov stratégií (conservative, balanced, aggressive)
- Ensemble evolution a voting mechanisms
- Competitive evolution
- Cooperative evolution
- Meta-evolution (evolúcia evolučných parametrov)

### 6. Backtesting Evolution (`backtesting-evolution.test.ts`)
- **Priorita**: Medium
- **Čas**: ~15 minút
- **Popis**: Komplexný backtesting framework

**Testuje**:
- Walk-forward optimization
- Overfitting detection
- K-fold cross-validation
- Time series cross-validation
- Monte Carlo backtesting
- Out-of-sample validation
- Performance attribution
- Sensitivity analysis
- Stress testing

## 🧬 Framework Components

### AgentEvolutionFramework
Hlavný evolučný framework implementujúci genetický algoritmus pre trading stratégie.

**Kľúčové funkcie**:
- Vytvorenie a správa populácie
- Evolučný cyklus s selection, crossover a mutation
- Multi-objective optimization
- Convergence tracking

### StrategyGenome
Reprezentácia genómu trading stratégie s parametrami ako:
- `minConfidence` (60-95%)
- `minSignalStrength` (50-90%)
- `maxRiskLevel` (low/medium/high)
- `trendConfirmationPeriod` (3-10)
- Boolean flags pre rôzne features

### FitnessEvaluator
Komplexné vyhodnocovanie fitness stratégií:
- **Win Rate**: Percentuálny podiel výherných obchodov
- **Profit Factor**: Pomer zisku k strate
- **Sharpe Ratio**: Risk-adjusted return
- **Max Drawdown**: Maximálny pokles hodnoty
- **Overall Score**: Kompozitné skóre

### GeneticOperators
Implementácia genetických operátorov:
- **Selection**: Tournament, roulette, rank, elite
- **Crossover**: Uniform, single-point, arithmetic
- **Mutation**: Gaussian, uniform, adaptive

## 📈 Metriky a Reporting

### Fitness Metriky
- **Win Rate**: 0-1 (vyšší = lepší)
- **Profit Factor**: >1 = profitabilný
- **Sharpe Ratio**: >1 = dobrý risk-adjusted return
- **Max Drawdown**: <0.2 = prijateľné riziko
- **Total Trades**: >10 = dostatočná aktivita

### Performance Benchmarks
- **Baseline Comparison**: SmartStrategy vs OptimizedSmartStrategy
- **Market Regime Testing**: Bull, bear, sideways markets
- **Stress Testing**: Extreme volatility, crashes, low liquidity
- **Robustness**: Performance s rôznymi úrovňami šumu

### Reporting
- **JSON Reports**: Detailné výsledky uložené v `evolution-test-report.json`
- **Console Output**: Real-time progress a summary
- **Performance Metrics**: Execution time, convergence data
- **Recommendations**: Automatické odporúčania na základe výsledkov

## 🔧 Konfigurácia

### Evolution Parameters
```typescript
{
  populationSize: 20,        // Veľkosť populácie
  generations: 10,           // Počet generácií
  mutationRate: 0.1,         // Pravdepodobnosť mutácie
  crossoverRate: 0.8,        // Pravdepodobnosť kríženia
  elitismRate: 0.2,          // Podiel elitných jedincov
  tournamentSize: 3          // Veľkosť turnaja pre selection
}
```

### Optimization Settings
```typescript
{
  method: 'bayesian',        // grid_search, bayesian, genetic
  maxIterations: 50,         // Maximálny počet iterácií
  acquisitionFunction: 'expected_improvement',
  objectives: ['winRate', 'profitFactor', 'sharpeRatio']
}
```

## 🎯 Best Practices

### Pre rýchle testovanie
1. Použite `--priority high` pre základné testy
2. Znížte `populationSize` a `generations` pre development
3. Použite menšie datasety (`testDataSize = 100`)

### Pre produkčné testovanie
1. Spustite všetky test suites
2. Použite reálne market data
3. Nastavte vyššie timeouty pre komplexné testy
4. Analyzujte reports pre optimalizáciu

### Pre debugging
1. Použite `--verbose` pre detailný output
2. Spustite jednotlivé test suites samostatne
3. Skontrolujte `evolution-test-report.json` pre detaily
4. Použite menšie parametre pre rýchlejšie iterácie

## 🚨 Troubleshooting

### Časté problémy
1. **Testy trvajú príliš dlho**: Znížte `populationSize`, `generations` alebo `testDataSize`
2. **Memory errors**: Spustite testy sekvenčne namiesto paralelne
3. **Timeout errors**: Zvýšte `--timeout` parameter
4. **Nízka fitness**: Skontrolujte market data a strategy parameters

### Performance optimalizácia
1. Použite menšie datasety pre development
2. Implementujte caching pre fitness evaluation
3. Optimalizujte genetic operators
4. Použite parallel processing pre nezávislé operácie

## 📚 Ďalšie zdroje

- [Genetic Algorithms in Trading](docs/genetic-algorithms.md)
- [Parameter Optimization Guide](docs/parameter-optimization.md)
- [Backtesting Best Practices](docs/backtesting.md)
- [Multi-Strategy Evolution](docs/multi-strategy.md)
