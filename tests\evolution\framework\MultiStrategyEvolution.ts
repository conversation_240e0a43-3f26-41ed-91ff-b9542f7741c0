// tests/evolution/framework/MultiStrategyEvolution.ts
// Framework pre evolúciu viacerých stratégií súčasne

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { AgentEvolutionFramework } from './AgentEvolutionFramework';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface MultiStrategyConfig {
    populationSize: number;
    generations: number;
    mutationRate: number;
    crossoverRate: number;
    elitismRate: number;
    strategyTypes: string[];
    ensembleSize: number;
}

export interface EnsembleResult {
    ensemble: StrategyGenome[];
    ensemblePerformance: FitnessResult;
    individualPerformances: FitnessResult[];
    diversityScore: number;
}

export interface CompetitionConfig {
    rounds: number;
    participantsPerRound: number;
    eliminationRate: number;
}

export interface CompetitionResult {
    winner: { genome: StrategyGenome; fitness: FitnessResult };
    finalRanking: { genome: StrategyGenome; fitness: FitnessResult }[];
    roundResults: any[];
}

export class MultiStrategyEvolution {
    private config: MultiStrategyConfig;

    constructor(config: MultiStrategyConfig) {
        this.config = config;
    }

    /**
     * Adaptuj stratégie na špecifické trhové podmienky
     */
    async adaptToMarketCondition(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<{
        bestStrategy: string;
        performance: FitnessResult;
        adaptedParameters: any;
        allResults: { strategyType: string; performance: FitnessResult }[];
    }> {
        const results = [];
        
        for (const strategyType of this.config.strategyTypes) {
            // Vytvor špecializovanú populáciu pre daný typ
            const population = this.createSpecializedPopulation(strategyType, 10);
            
            // Vyhodnoť najlepšieho jedinca z populácie
            let bestGenome = population[0];
            let bestFitness = await fitnessEvaluator.evaluateFitness(bestGenome, marketData, initialBalance);
            
            for (const genome of population.slice(1)) {
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                if (fitness.overallScore > bestFitness.overallScore) {
                    bestGenome = genome;
                    bestFitness = fitness;
                }
            }
            
            results.push({
                strategyType,
                performance: bestFitness,
                parameters: bestGenome.getParameters()
            });
        }
        
        // Nájdi najlepší typ stratégie
        const bestResult = results.reduce((best, current) => 
            current.performance.overallScore > best.performance.overallScore ? current : best
        );
        
        return {
            bestStrategy: bestResult.strategyType,
            performance: bestResult.performance,
            adaptedParameters: bestResult.parameters,
            allResults: results
        };
    }

    /**
     * Evolúcia ensemble stratégií
     */
    async evolveEnsemble(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<EnsembleResult> {
        // Vytvor diverzifikovanú populáciu
        const population = this.createDiversePopulation(this.config.populationSize);
        
        // Vyhodnoť všetkých jedincov
        const evaluatedPopulation = [];
        for (const genome of population) {
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            evaluatedPopulation.push({ genome, fitness });
        }
        
        // Vyber ensemble členov na základe diverzity a výkonnosti
        const ensemble = this.selectEnsembleMembers(evaluatedPopulation, this.config.ensembleSize);
        
        // Vyhodnoť ensemble výkonnosť
        const ensemblePerformance = await this.evaluateEnsemble(ensemble, marketData, initialBalance, fitnessEvaluator);
        
        // Vyhodnoť individuálne výkonnosti
        const individualPerformances = [];
        for (const member of ensemble) {
            const fitness = await fitnessEvaluator.evaluateFitness(member, marketData, initialBalance);
            individualPerformances.push(fitness);
        }
        
        // Vypočítaj diverzitu
        const diversityScore = this.calculateEnsembleDiversity(ensemble);
        
        return {
            ensemble,
            ensemblePerformance,
            individualPerformances,
            diversityScore
        };
    }

    /**
     * Test rôznych voting mechanizmov
     */
    async testVotingMechanism(
        ensemble: StrategyGenome[],
        mechanism: string,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<{
        performance: FitnessResult;
        tradeCount: number;
        agreementRate: number;
    }> {
        // Simuluj ensemble trading s daným voting mechanizmom
        let totalTrades = 0;
        let agreements = 0;
        let totalDecisions = 0;
        
        // Zjednodušená simulácia
        for (let i = 1; i < marketData.length; i++) {
            const decisions = [];
            
            // Získaj rozhodnutia od všetkých členov ensemble
            for (const member of ensemble) {
                const strategy = member.createStrategy();
                const decision = strategy.decideAction(marketData[i]);
                decisions.push(decision);
            }
            
            totalDecisions++;
            
            // Aplikuj voting mechanizmus
            const finalDecision = this.applyVotingMechanism(decisions, mechanism);
            
            if (finalDecision) {
                totalTrades++;
                
                // Počítaj agreement rate
                const validDecisions = decisions.filter(d => d !== null);
                if (validDecisions.length > 1) {
                    const sameAction = validDecisions.every(d => d!.action === validDecisions[0]!.action);
                    if (sameAction) agreements++;
                }
            }
        }
        
        const agreementRate = totalDecisions > 0 ? agreements / totalDecisions : 0;
        
        // Vyhodnoť výkonnosť ensemble
        const ensemblePerformance = await this.evaluateEnsemble(ensemble, marketData, initialBalance, fitnessEvaluator);
        
        return {
            performance: ensemblePerformance,
            tradeCount: totalTrades,
            agreementRate
        };
    }

    /**
     * Spusti súťaž medzi stratégiami
     */
    async runCompetition(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        config: CompetitionConfig
    ): Promise<CompetitionResult> {
        // Vytvor počiatočnú populáciu
        let participants = this.createDiversePopulation(config.participantsPerRound);
        const roundResults = [];
        
        for (let round = 0; round < config.rounds; round++) {
            console.log(`🏁 Competition Round ${round + 1}/${config.rounds}`);
            
            // Vyhodnoť všetkých účastníkov
            const evaluatedParticipants = [];
            for (const genome of participants) {
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                evaluatedParticipants.push({ genome, fitness });
            }
            
            // Zoraď podľa výkonnosti
            evaluatedParticipants.sort((a, b) => b.fitness.overallScore - a.fitness.overallScore);
            
            roundResults.push({
                round: round + 1,
                participants: evaluatedParticipants.length,
                bestScore: evaluatedParticipants[0].fitness.overallScore,
                averageScore: evaluatedParticipants.reduce((sum, p) => sum + p.fitness.overallScore, 0) / evaluatedParticipants.length
            });
            
            // Eliminuj najhorších
            const survivorCount = Math.ceil(participants.length * (1 - config.eliminationRate));
            const survivors = evaluatedParticipants.slice(0, survivorCount);
            
            // Pre ďalšie kolo, pridaj mutácie najlepších
            participants = survivors.map(s => s.genome);
            
            // Pridaj mutované verzie najlepších
            while (participants.length < config.participantsPerRound) {
                const parent = survivors[Math.floor(Math.random() * Math.min(3, survivors.length))];
                const mutated = parent.genome.mutate(0.2);
                participants.push(mutated);
            }
        }
        
        // Finálne vyhodnotenie
        const finalEvaluated = [];
        for (const genome of participants) {
            const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
            finalEvaluated.push({ genome, fitness });
        }
        
        finalEvaluated.sort((a, b) => b.fitness.overallScore - a.fitness.overallScore);
        
        return {
            winner: finalEvaluated[0],
            finalRanking: finalEvaluated,
            roundResults
        };
    }

    /**
     * Kompetitívna evolúcia s udržiavaním diverzity
     */
    async competitiveEvolutionWithDiversity(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        config: {
            populationSize: number;
            generations: number;
            diversityWeight: number;
            fitnessWeight: number;
        }
    ): Promise<{
        finalPopulation: StrategyGenome[];
        diversityHistory: number[];
        fitnessHistory: number[];
        bestFitness: FitnessResult;
    }> {
        let population = this.createDiversePopulation(config.populationSize);
        const diversityHistory = [];
        const fitnessHistory = [];
        let bestFitness = null;
        
        for (let gen = 0; gen < config.generations; gen++) {
            // Vyhodnoť populáciu
            const evaluatedPopulation = [];
            for (const genome of population) {
                const fitness = await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
                evaluatedPopulation.push({ genome, fitness });
                
                if (!bestFitness || fitness.overallScore > bestFitness.overallScore) {
                    bestFitness = fitness;
                }
            }
            
            // Vypočítaj diverzitu
            const diversity = this.calculatePopulationDiversity(population);
            diversityHistory.push(diversity);
            
            const avgFitness = evaluatedPopulation.reduce((sum, e) => sum + e.fitness.overallScore, 0) / evaluatedPopulation.length;
            fitnessHistory.push(avgFitness);
            
            // Vytvor novú generáciu s ohľadom na diverzitu
            population = this.createNextGenerationWithDiversity(
                evaluatedPopulation,
                config.diversityWeight,
                config.fitnessWeight
            );
        }
        
        return {
            finalPopulation: population,
            diversityHistory,
            fitnessHistory,
            bestFitness: bestFitness!
        };
    }

    /**
     * Kooperatívna evolúcia
     */
    async cooperativeEvolution(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        config: {
            teamSize: number;
            populationSize: number;
            generations: number;
            cooperationWeight: number;
        }
    ): Promise<{
        bestTeam: StrategyGenome[];
        teamPerformance: FitnessResult;
        cooperationScore: number;
        individualScores: number[];
    }> {
        let population = this.createDiversePopulation(config.populationSize);
        let bestTeam = null;
        let bestTeamPerformance = null;
        
        for (let gen = 0; gen < config.generations; gen++) {
            // Vytvor náhodné tímy
            const teams = this.createRandomTeams(population, config.teamSize);
            
            for (const team of teams) {
                // Vyhodnoť tím
                const teamPerformance = await this.evaluateTeam(team, marketData, initialBalance, fitnessEvaluator);
                
                if (!bestTeamPerformance || teamPerformance.overallScore > bestTeamPerformance.overallScore) {
                    bestTeam = team;
                    bestTeamPerformance = teamPerformance;
                }
            }
            
            // Vytvor novú generáciu na základe tímových výsledkov
            population = this.evolveBasedOnTeamPerformance(teams, marketData, initialBalance, fitnessEvaluator);
        }
        
        // Vypočítaj kooperačné skóre
        const cooperationScore = await this.calculateCooperationScore(bestTeam!, marketData, initialBalance, fitnessEvaluator);
        
        // Individuálne skóre
        const individualScores = [];
        for (const member of bestTeam!) {
            const fitness = await fitnessEvaluator.evaluateFitness(member, marketData, initialBalance);
            individualScores.push(fitness.overallScore);
        }
        
        return {
            bestTeam: bestTeam!,
            teamPerformance: bestTeamPerformance!,
            cooperationScore,
            individualScores
        };
    }

    /**
     * Meta-evolúcia - evolúcia evolučných parametrov
     */
    async metaEvolution(
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator,
        config: {
            metaGenerations: number;
            strategiesPerMeta: number;
            parameterRanges: any;
        }
    ): Promise<{
        bestEvolutionParams: any;
        bestStrategy: StrategyGenome;
        metaFitness: FitnessResult;
    }> {
        let bestParams = null;
        let bestStrategy = null;
        let bestFitness = null;
        
        for (let metaGen = 0; metaGen < config.metaGenerations; metaGen++) {
            // Vygeneruj náhodné evolučné parametre
            const evolutionParams = this.sampleEvolutionParameters(config.parameterRanges);
            
            // Spusti evolúciu s týmito parametrami
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: evolutionParams.populationSize,
                generations: config.strategiesPerMeta,
                mutationRate: evolutionParams.mutationRate,
                crossoverRate: evolutionParams.crossoverRate,
                elitismRate: 0.2,
                tournamentSize: 3
            });
            
            const result = await evolutionFramework.evolve(marketData, initialBalance);
            
            if (!bestFitness || result.bestFitness.overallScore > bestFitness.overallScore) {
                bestParams = evolutionParams;
                bestStrategy = result.bestIndividual;
                bestFitness = result.bestFitness;
            }
        }
        
        return {
            bestEvolutionParams: bestParams!,
            bestStrategy: bestStrategy!,
            metaFitness: bestFitness!
        };
    }

    // Pomocné metódy
    private createSpecializedPopulation(strategyType: string, size: number): StrategyGenome[] {
        const population = [];
        
        for (let i = 0; i < size; i++) {
            let params;
            
            switch (strategyType) {
                case 'conservative':
                    params = {
                        minConfidence: 85 + Math.random() * 10,
                        maxRiskLevel: 'low' as const,
                        maxDailyTrades: 2 + Math.floor(Math.random() * 3)
                    };
                    break;
                case 'aggressive':
                    params = {
                        minConfidence: 60 + Math.random() * 15,
                        maxRiskLevel: 'high' as const,
                        maxDailyTrades: 8 + Math.floor(Math.random() * 7)
                    };
                    break;
                default:
                    params = {
                        minConfidence: 70 + Math.random() * 15,
                        maxRiskLevel: 'medium' as const,
                        maxDailyTrades: 4 + Math.floor(Math.random() * 4)
                    };
            }
            
            population.push(new StrategyGenome(params));
        }
        
        return population;
    }

    private createDiversePopulation(size: number): StrategyGenome[] {
        const population = [];
        
        for (let i = 0; i < size; i++) {
            const params = {
                minConfidence: 60 + Math.random() * 35,
                minSignalStrength: 50 + Math.random() * 40,
                maxRiskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
                trendConfirmationPeriod: 3 + Math.floor(Math.random() * 8),
                maxDailyTrades: 3 + Math.floor(Math.random() * 12)
            };
            
            population.push(new StrategyGenome(params));
        }
        
        return population;
    }

    private selectEnsembleMembers(
        evaluatedPopulation: { genome: StrategyGenome; fitness: FitnessResult }[],
        ensembleSize: number
    ): StrategyGenome[] {
        // Zjednodušená implementácia - vyber najlepších s ohľadom na diverzitu
        const selected = [];
        const candidates = [...evaluatedPopulation].sort((a, b) => b.fitness.overallScore - a.fitness.overallScore);
        
        selected.push(candidates[0].genome); // Najlepší
        
        for (let i = 1; i < ensembleSize && i < candidates.length; i++) {
            // Vyber kandidáta s najväčšou diverzitou voči už vybraným
            let bestCandidate = candidates[i];
            let maxMinDistance = 0;
            
            for (let j = i; j < Math.min(i + 10, candidates.length); j++) {
                const candidate = candidates[j];
                let minDistance = Infinity;
                
                for (const selectedMember of selected) {
                    const distance = candidate.genome.distance(selectedMember);
                    minDistance = Math.min(minDistance, distance);
                }
                
                if (minDistance > maxMinDistance) {
                    maxMinDistance = minDistance;
                    bestCandidate = candidate;
                }
            }
            
            selected.push(bestCandidate.genome);
        }
        
        return selected;
    }

    private async evaluateEnsemble(
        ensemble: StrategyGenome[],
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<FitnessResult> {
        // Zjednodušená implementácia - priemer individuálnych výkonností
        const individualFitnesses = [];
        
        for (const member of ensemble) {
            const fitness = await fitnessEvaluator.evaluateFitness(member, marketData, initialBalance);
            individualFitnesses.push(fitness);
        }
        
        // Vypočítaj priemerné metriky
        const avgWinRate = individualFitnesses.reduce((sum, f) => sum + f.winRate, 0) / individualFitnesses.length;
        const avgProfitFactor = individualFitnesses.reduce((sum, f) => sum + f.profitFactor, 0) / individualFitnesses.length;
        const avgOverallScore = individualFitnesses.reduce((sum, f) => sum + f.overallScore, 0) / individualFitnesses.length;
        
        return {
            ...individualFitnesses[0], // Použij štruktúru prvého
            winRate: avgWinRate,
            profitFactor: avgProfitFactor,
            overallScore: avgOverallScore * 1.1 // Bonus za ensemble
        };
    }

    private calculateEnsembleDiversity(ensemble: StrategyGenome[]): number {
        let totalDistance = 0;
        let comparisons = 0;
        
        for (let i = 0; i < ensemble.length; i++) {
            for (let j = i + 1; j < ensemble.length; j++) {
                totalDistance += ensemble[i].distance(ensemble[j]);
                comparisons++;
            }
        }
        
        return comparisons > 0 ? totalDistance / comparisons : 0;
    }

    private applyVotingMechanism(decisions: any[], mechanism: string): any {
        const validDecisions = decisions.filter(d => d !== null);
        
        if (validDecisions.length === 0) return null;
        
        switch (mechanism) {
            case 'majority':
                const buyVotes = validDecisions.filter(d => d.action === 'buy').length;
                const sellVotes = validDecisions.filter(d => d.action === 'sell').length;
                
                if (buyVotes > sellVotes) return { action: 'buy', amount: 10 };
                if (sellVotes > buyVotes) return { action: 'sell', amount: 10 };
                return null;
                
            case 'unanimous':
                const firstAction = validDecisions[0].action;
                const allSame = validDecisions.every(d => d.action === firstAction);
                return allSame ? { action: firstAction, amount: 10 } : null;
                
            case 'weighted':
                // Zjednodušená implementácia
                return validDecisions[0];
                
            default:
                return validDecisions[0];
        }
    }

    private calculatePopulationDiversity(population: StrategyGenome[]): number {
        let totalDistance = 0;
        let comparisons = 0;
        
        for (let i = 0; i < population.length; i++) {
            for (let j = i + 1; j < population.length; j++) {
                totalDistance += population[i].distance(population[j]);
                comparisons++;
            }
        }
        
        return comparisons > 0 ? totalDistance / comparisons : 0;
    }

    private createNextGenerationWithDiversity(
        evaluatedPopulation: { genome: StrategyGenome; fitness: FitnessResult }[],
        diversityWeight: number,
        fitnessWeight: number
    ): StrategyGenome[] {
        // Zjednodušená implementácia
        const sorted = evaluatedPopulation.sort((a, b) => b.fitness.overallScore - a.fitness.overallScore);
        const nextGen = [];
        
        // Elitizmus
        nextGen.push(...sorted.slice(0, 5).map(e => e.genome));
        
        // Mutácie najlepších
        while (nextGen.length < this.config.populationSize) {
            const parent = sorted[Math.floor(Math.random() * Math.min(10, sorted.length))];
            nextGen.push(parent.genome.mutate(0.15));
        }
        
        return nextGen;
    }

    private createRandomTeams(population: StrategyGenome[], teamSize: number): StrategyGenome[][] {
        const teams = [];
        const shuffled = [...population].sort(() => Math.random() - 0.5);
        
        for (let i = 0; i < shuffled.length; i += teamSize) {
            if (i + teamSize <= shuffled.length) {
                teams.push(shuffled.slice(i, i + teamSize));
            }
        }
        
        return teams;
    }

    private async evaluateTeam(
        team: StrategyGenome[],
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<FitnessResult> {
        return await this.evaluateEnsemble(team, marketData, initialBalance, fitnessEvaluator);
    }

    private async evolveBasedOnTeamPerformance(
        teams: StrategyGenome[][],
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): StrategyGenome[] {
        // Zjednodušená implementácia
        const allMembers = teams.flat();
        return this.createDiversePopulation(this.config.populationSize);
    }

    private async calculateCooperationScore(
        team: StrategyGenome[],
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<number> {
        // Zjednodušená implementácia
        return Math.random() * 0.5 + 0.5; // 0.5-1.0
    }

    private sampleEvolutionParameters(ranges: any): any {
        const params: any = {};
        
        for (const [key, values] of Object.entries(ranges)) {
            const valueArray = values as any[];
            params[key] = valueArray[Math.floor(Math.random() * valueArray.length)];
        }
        
        return params;
    }
}
