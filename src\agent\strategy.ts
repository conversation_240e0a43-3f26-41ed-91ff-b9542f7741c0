// strategy.ts

export interface TradeOrder {
    action: 'buy' | 'sell';
    amount: number;
}

export interface TradingStrategy {
    decideAction(marketData: any): TradeOrder | null;
}

export class SimpleStrategy implements TradingStrategy {
    decideAction(marketData: any): TradeOrder | null {
        // Simple logic for trading decision
        if (marketData.price > marketData.movingAverage) {
            return { action: 'buy', amount: 10 }; // Example amount
        } else if (marketData.price < marketData.movingAverage) {
            return { action: 'sell', amount: 10 }; // Example amount
        }
        return null;
    }
}

export class MovingAverageCrossoverStrategy implements TradingStrategy {
    private shortTermMA: number;
    private longTermMA: number;

    constructor(shortTermMA: number, longTermMA: number) {
        this.shortTermMA = shortTermMA;
        this.longTermMA = longTermMA;
    }

    decideAction(marketData: any): TradeOrder | null {
        if (marketData.shortTermMovingAverage > marketData.longTermMovingAverage) {
            return { action: 'buy', amount: 10 };
        } else if (marketData.shortTermMovingAverage < marketData.longTermMovingAverage) {
            return { action: 'sell', amount: 10 };
        }
        return null;
    }
}