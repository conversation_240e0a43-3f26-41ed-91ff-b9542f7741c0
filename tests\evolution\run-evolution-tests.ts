// tests/evolution/run-evolution-tests.ts
// Komplexný test runner pre evolučné testy trading agentov

import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

interface TestSuite {
    name: string;
    file: string;
    description: string;
    estimatedTime: number; // v minútach
    priority: 'high' | 'medium' | 'low';
}

interface TestResult {
    suite: string;
    passed: boolean;
    duration: number;
    output: string;
    errors?: string;
}

class EvolutionTestRunner {
    private testSuites: TestSuite[] = [
        {
            name: 'Core Evolution Framework',
            file: 'agent-evolution.test.ts',
            description: 'Základné testy evolučného frameworku, fitness evaluácie a genetických operátorov',
            estimatedTime: 5,
            priority: 'high'
        },
        {
            name: 'Genetic Algorithms',
            file: 'genetic-algorithms.test.ts',
            description: 'Detailné testy genetických algoritmov - sele<PERSON>cia, kríženie, mutácia',
            estimatedTime: 3,
            priority: 'high'
        },
        {
            name: 'Performance Benchmarks',
            file: 'performance-benchmarks.test.ts',
            description: 'Benchmarking výkonnosti stratégií a porovnanie s baseline',
            estimatedTime: 8,
            priority: 'medium'
        },
        {
            name: 'Parameter Optimization',
            file: 'parameter-optimization.test.ts',
            description: 'Grid search, Bayesian optimalizácia a hyperparameter tuning',
            estimatedTime: 10,
            priority: 'medium'
        },
        {
            name: 'Multi-Strategy Evolution',
            file: 'multi-strategy-evolution.test.ts',
            description: 'Evolúcia viacerých stratégií, ensemble learning a kooperácia',
            estimatedTime: 12,
            priority: 'low'
        },
        {
            name: 'Backtesting Evolution',
            file: 'backtesting-evolution.test.ts',
            description: 'Walk-forward analýza, cross-validation a robustness testing',
            estimatedTime: 15,
            priority: 'medium'
        }
    ];

    private results: TestResult[] = [];

    /**
     * Spusti všetky evolučné testy
     */
    async runAllTests(options: {
        priority?: 'high' | 'medium' | 'low';
        parallel?: boolean;
        timeout?: number;
        verbose?: boolean;
    } = {}): Promise<void> {
        const { priority, parallel = false, timeout = 30, verbose = true } = options;
        
        console.log('🧬 === EVOLUTION TESTS RUNNER ===\n');
        
        // Filtruj test suites podľa priority
        let suitesToRun = this.testSuites;
        if (priority) {
            const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
            suitesToRun = this.testSuites.filter(suite => 
                priorityOrder[suite.priority] >= priorityOrder[priority]
            );
        }
        
        console.log(`📋 Spúšťam ${suitesToRun.length} test suites:`);
        suitesToRun.forEach((suite, index) => {
            console.log(`  ${index + 1}. ${suite.name} (${suite.estimatedTime}min, ${suite.priority} priority)`);
        });
        
        const totalEstimatedTime = suitesToRun.reduce((sum, suite) => sum + suite.estimatedTime, 0);
        console.log(`⏱️ Odhadovaný čas: ${totalEstimatedTime} minút\n`);
        
        const startTime = Date.now();
        
        if (parallel) {
            await this.runTestsInParallel(suitesToRun, timeout, verbose);
        } else {
            await this.runTestsSequentially(suitesToRun, timeout, verbose);
        }
        
        const totalTime = (Date.now() - startTime) / 1000 / 60;
        
        // Vygeneruj report
        this.generateReport(totalTime);
        
        // Ulož výsledky
        await this.saveResults();
    }

    /**
     * Spusti testy sekvenčne
     */
    private async runTestsSequentially(
        suites: TestSuite[], 
        timeout: number, 
        verbose: boolean
    ): Promise<void> {
        for (let i = 0; i < suites.length; i++) {
            const suite = suites[i];
            console.log(`\n🔄 [${i + 1}/${suites.length}] Spúšťam: ${suite.name}`);
            console.log(`📝 ${suite.description}`);
            
            const result = await this.runSingleTest(suite, timeout, verbose);
            this.results.push(result);
            
            if (result.passed) {
                console.log(`✅ ${suite.name} - PASSED (${result.duration.toFixed(1)}s)`);
            } else {
                console.log(`❌ ${suite.name} - FAILED (${result.duration.toFixed(1)}s)`);
                if (verbose && result.errors) {
                    console.log(`🔍 Errors:\n${result.errors}`);
                }
            }
        }
    }

    /**
     * Spusti testy paralelne
     */
    private async runTestsInParallel(
        suites: TestSuite[], 
        timeout: number, 
        verbose: boolean
    ): Promise<void> {
        console.log('\n🚀 Spúšťam testy paralelne...\n');
        
        const promises = suites.map(suite => this.runSingleTest(suite, timeout, verbose));
        const results = await Promise.all(promises);
        
        this.results.push(...results);
        
        // Zobraz výsledky
        results.forEach((result, index) => {
            const suite = suites[index];
            if (result.passed) {
                console.log(`✅ ${suite.name} - PASSED (${result.duration.toFixed(1)}s)`);
            } else {
                console.log(`❌ ${suite.name} - FAILED (${result.duration.toFixed(1)}s)`);
            }
        });
    }

    /**
     * Spusti jeden test suite
     */
    private async runSingleTest(
        suite: TestSuite, 
        timeout: number, 
        verbose: boolean
    ): Promise<TestResult> {
        const startTime = Date.now();
        
        return new Promise((resolve) => {
            const testPath = path.join(__dirname, suite.file);
            const jestCommand = `npx jest ${testPath} --verbose=${verbose}`;
            
            const child = spawn('npx', ['jest', testPath, `--verbose=${verbose}`], {
                cwd: process.cwd(),
                stdio: 'pipe'
            });
            
            let output = '';
            let errors = '';
            
            child.stdout?.on('data', (data) => {
                output += data.toString();
            });
            
            child.stderr?.on('data', (data) => {
                errors += data.toString();
            });
            
            // Timeout handling
            const timeoutId = setTimeout(() => {
                child.kill('SIGTERM');
                resolve({
                    suite: suite.name,
                    passed: false,
                    duration: (Date.now() - startTime) / 1000,
                    output: output,
                    errors: `Test timeout after ${timeout} minutes`
                });
            }, timeout * 60 * 1000);
            
            child.on('close', (code) => {
                clearTimeout(timeoutId);
                resolve({
                    suite: suite.name,
                    passed: code === 0,
                    duration: (Date.now() - startTime) / 1000,
                    output: output,
                    errors: code !== 0 ? errors : undefined
                });
            });
        });
    }

    /**
     * Vygeneruj report
     */
    private generateReport(totalTime: number): void {
        console.log('\n🎯 === TEST RESULTS SUMMARY ===\n');
        
        const passed = this.results.filter(r => r.passed).length;
        const failed = this.results.filter(r => !r.passed).length;
        const total = this.results.length;
        
        console.log(`📊 Celkové výsledky:`);
        console.log(`  ✅ Passed: ${passed}/${total} (${((passed/total)*100).toFixed(1)}%)`);
        console.log(`  ❌ Failed: ${failed}/${total} (${((failed/total)*100).toFixed(1)}%)`);
        console.log(`  ⏱️ Total time: ${totalTime.toFixed(1)} minutes`);
        
        if (failed > 0) {
            console.log(`\n❌ Failed tests:`);
            this.results.filter(r => !r.passed).forEach(result => {
                console.log(`  • ${result.suite} (${result.duration.toFixed(1)}s)`);
            });
        }
        
        console.log(`\n⚡ Performance summary:`);
        this.results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`  ${status} ${result.suite}: ${result.duration.toFixed(1)}s`);
        });
        
        // Recommendations
        console.log(`\n💡 Recommendations:`);
        if (failed === 0) {
            console.log(`  🎉 All tests passed! Your evolution framework is working correctly.`);
        } else {
            console.log(`  🔧 ${failed} test(s) failed. Check the errors above and fix the issues.`);
        }
        
        const avgTime = this.results.reduce((sum, r) => sum + r.duration, 0) / this.results.length;
        if (avgTime > 300) { // 5 minutes
            console.log(`  ⚡ Tests are running slowly (avg ${avgTime.toFixed(1)}s). Consider optimizing.`);
        }
    }

    /**
     * Ulož výsledky do súboru
     */
    private async saveResults(): Promise<void> {
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                total: this.results.length,
                passed: this.results.filter(r => r.passed).length,
                failed: this.results.filter(r => !r.passed).length,
                totalTime: this.results.reduce((sum, r) => sum + r.duration, 0)
            },
            results: this.results.map(r => ({
                suite: r.suite,
                passed: r.passed,
                duration: r.duration,
                hasErrors: !!r.errors
            }))
        };
        
        const reportPath = path.join(__dirname, '../../evolution-test-report.json');
        await fs.promises.writeFile(reportPath, JSON.stringify(reportData, null, 2));
        
        console.log(`\n📄 Report saved to: ${reportPath}`);
    }

    /**
     * Spusti len špecifické testy
     */
    async runSpecificTests(testNames: string[]): Promise<void> {
        const suitesToRun = this.testSuites.filter(suite => 
            testNames.some(name => suite.name.toLowerCase().includes(name.toLowerCase()))
        );
        
        if (suitesToRun.length === 0) {
            console.log('❌ No matching test suites found.');
            console.log('Available test suites:');
            this.testSuites.forEach(suite => console.log(`  • ${suite.name}`));
            return;
        }
        
        await this.runTestsSequentially(suitesToRun, 30, true);
        this.generateReport(0);
    }
}

// CLI interface
if (require.main === module) {
    const runner = new EvolutionTestRunner();
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
🧬 Evolution Tests Runner

Usage:
  npm run test:evolution                    # Run all tests
  npm run test:evolution -- --priority high # Run only high priority tests
  npm run test:evolution -- --parallel     # Run tests in parallel
  npm run test:evolution -- --specific "genetic,benchmark" # Run specific tests

Options:
  --priority <high|medium|low>  Run tests with specified priority or higher
  --parallel                    Run tests in parallel (faster but less readable)
  --timeout <minutes>           Timeout for each test suite (default: 30)
  --verbose                     Verbose output (default: true)
  --specific <names>            Run only tests matching the specified names
  --help, -h                    Show this help message
        `);
        process.exit(0);
    }
    
    const options: any = {};
    
    if (args.includes('--priority')) {
        const priorityIndex = args.indexOf('--priority');
        options.priority = args[priorityIndex + 1];
    }
    
    if (args.includes('--parallel')) {
        options.parallel = true;
    }
    
    if (args.includes('--timeout')) {
        const timeoutIndex = args.indexOf('--timeout');
        options.timeout = parseInt(args[timeoutIndex + 1]);
    }
    
    if (args.includes('--verbose')) {
        const verboseIndex = args.indexOf('--verbose');
        options.verbose = args[verboseIndex + 1] === 'true';
    }
    
    if (args.includes('--specific')) {
        const specificIndex = args.indexOf('--specific');
        const testNames = args[specificIndex + 1].split(',');
        runner.runSpecificTests(testNames).catch(console.error);
    } else {
        runner.runAllTests(options).catch(console.error);
    }
}
