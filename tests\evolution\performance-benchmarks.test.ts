// tests/evolution/performance-benchmarks.test.ts
// Komplexné benchmarking testy pre výkonnosť trading stratégií

import { AgentEvolutionFramework } from './framework/AgentEvolutionFramework';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { StrategyGenome } from './framework/StrategyGenome';
import { SmartStrategy } from '../../src/agent/smartStrategy';
import { OptimizedSmartStrategy } from '../../src/agent/optimizedSmartStrategy';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Strategy Performance Benchmarks', () => {
    let fitnessEvaluator: FitnessEvaluator;
    const initialBalance = 10000;
    const testDataSize = 200; // Menší dataset pre rýchlejšie testy

    beforeEach(() => {
        fitnessEvaluator = new FitnessEvaluator();
    });

    describe('Baseline Strategy Performance', () => {
        it('should benchmark SmartStrategy performance', async () => {
            const smartStrategy = new SmartStrategy();
            const genome = StrategyGenome.fromStrategy(smartStrategy);
            
            const fitness = await fitnessEvaluator.evaluateFitness(
                genome,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance
            );

            // Základné očakávania pre SmartStrategy
            expect(fitness.winRate).toBeGreaterThanOrEqual(0.4); // Minimálne 40% win rate
            expect(fitness.profitFactor).toBeGreaterThan(0.8); // Aspoň blízko k profitabilite
            expect(fitness.maxDrawdown).toBeLessThan(0.5); // Max 50% drawdown
            expect(fitness.totalTrades).toBeGreaterThan(0); // Musí robiť obchody
            expect(fitness.overallScore).toBeGreaterThan(0);

            console.log(`📊 SmartStrategy Benchmark:`);
            console.log(`  Win Rate: ${(fitness.winRate * 100).toFixed(2)}%`);
            console.log(`  Profit Factor: ${fitness.profitFactor.toFixed(2)}`);
            console.log(`  Max Drawdown: ${(fitness.maxDrawdown * 100).toFixed(2)}%`);
            console.log(`  Total Trades: ${fitness.totalTrades}`);
            console.log(`  Overall Score: ${fitness.overallScore.toFixed(2)}`);
        });

        it('should benchmark OptimizedSmartStrategy performance', async () => {
            const optimizedStrategy = new OptimizedSmartStrategy();
            const genome = StrategyGenome.fromStrategy(optimizedStrategy);
            
            const fitness = await fitnessEvaluator.evaluateFitness(
                genome,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance
            );

            // Vyššie očakávania pre OptimizedSmartStrategy
            expect(fitness.winRate).toBeGreaterThanOrEqual(0.5); // Minimálne 50% win rate
            expect(fitness.profitFactor).toBeGreaterThan(1.0); // Musí byť profitabilná
            expect(fitness.maxDrawdown).toBeLessThan(0.3); // Max 30% drawdown
            expect(fitness.totalTrades).toBeGreaterThan(0);
            expect(fitness.overallScore).toBeGreaterThan(0);

            console.log(`📊 OptimizedSmartStrategy Benchmark:`);
            console.log(`  Win Rate: ${(fitness.winRate * 100).toFixed(2)}%`);
            console.log(`  Profit Factor: ${fitness.profitFactor.toFixed(2)}`);
            console.log(`  Max Drawdown: ${(fitness.maxDrawdown * 100).toFixed(2)}%`);
            console.log(`  Total Trades: ${fitness.totalTrades}`);
            console.log(`  Overall Score: ${fitness.overallScore.toFixed(2)}`);
        });

        it('should compare baseline strategies', async () => {
            const smartGenome = StrategyGenome.fromStrategy(new SmartStrategy());
            const optimizedGenome = StrategyGenome.fromStrategy(new OptimizedSmartStrategy());
            
            const smartFitness = await fitnessEvaluator.evaluateFitness(
                smartGenome,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance
            );
            
            const optimizedFitness = await fitnessEvaluator.evaluateFitness(
                optimizedGenome,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance
            );

            // OptimizedSmartStrategy by mala byť lepšia
            expect(optimizedFitness.overallScore).toBeGreaterThanOrEqual(smartFitness.overallScore * 0.9);
            expect(optimizedFitness.winRate).toBeGreaterThanOrEqual(smartFitness.winRate * 0.9);
            
            console.log(`📈 Strategy Comparison:`);
            console.log(`  Smart vs Optimized Win Rate: ${(smartFitness.winRate * 100).toFixed(2)}% vs ${(optimizedFitness.winRate * 100).toFixed(2)}%`);
            console.log(`  Smart vs Optimized Score: ${smartFitness.overallScore.toFixed(2)} vs ${optimizedFitness.overallScore.toFixed(2)}`);
        });
    });

    describe('Market Condition Performance', () => {
        it('should test performance across different market conditions', async () => {
            const testGenome = new StrategyGenome({
                minConfidence: 80,
                minSignalStrength: 75,
                maxRiskLevel: 'medium'
            });

            const scenarios = [
                { name: 'Optimal', data: testScenarios.optimalConditions },
                { name: 'Volatile', data: testScenarios.volatileMarket },
                { name: 'Trending', data: testScenarios.trendingMarket },
                { name: 'Sideways', data: testScenarios.sidewaysMarket },
                { name: 'High Volume', data: testScenarios.highVolumeMarket }
            ];

            const results = [];

            for (const scenario of scenarios) {
                const fitness = await fitnessEvaluator.evaluateFitness(
                    testGenome,
                    scenario.data.slice(0, testDataSize),
                    initialBalance
                );
                
                results.push({
                    scenario: scenario.name,
                    fitness
                });

                expect(fitness.overallScore).toBeGreaterThanOrEqual(0);
                console.log(`📊 ${scenario.name} Market: Score ${fitness.overallScore.toFixed(2)}, Win Rate ${(fitness.winRate * 100).toFixed(2)}%`);
            }

            // Stratégia by mala fungovať aspoň v niektorých podmienkach
            const workingScenarios = results.filter(r => r.fitness.overallScore > 10);
            expect(workingScenarios.length).toBeGreaterThan(0);
        });

        it('should identify best performing market conditions', async () => {
            const conservativeGenome = new StrategyGenome({
                minConfidence: 90,
                minSignalStrength: 85,
                maxRiskLevel: 'low',
                maxDailyTrades: 3
            });

            const aggressiveGenome = new StrategyGenome({
                minConfidence: 60,
                minSignalStrength: 55,
                maxRiskLevel: 'high',
                maxDailyTrades: 15
            });

            const scenarios = [
                testScenarios.optimalConditions,
                testScenarios.volatileMarket,
                testScenarios.trendingMarket
            ];

            for (const scenario of scenarios) {
                const conservativeFitness = await fitnessEvaluator.evaluateFitness(
                    conservativeGenome,
                    scenario.slice(0, testDataSize),
                    initialBalance
                );

                const aggressiveFitness = await fitnessEvaluator.evaluateFitness(
                    aggressiveGenome,
                    scenario.slice(0, testDataSize),
                    initialBalance
                );

                // Aspoň jedna stratégia by mala fungovať
                expect(Math.max(conservativeFitness.overallScore, aggressiveFitness.overallScore)).toBeGreaterThan(0);
                
                console.log(`Conservative vs Aggressive: ${conservativeFitness.overallScore.toFixed(2)} vs ${aggressiveFitness.overallScore.toFixed(2)}`);
            }
        });
    });

    describe('Parameter Sensitivity Analysis', () => {
        it('should test sensitivity to confidence threshold', async () => {
            const baseParams = {
                minSignalStrength: 70,
                maxRiskLevel: 'medium' as const,
                trendConfirmationPeriod: 5
            };

            const confidenceValues = [60, 70, 80, 90, 95];
            const results = [];

            for (const confidence of confidenceValues) {
                const genome = new StrategyGenome({
                    ...baseParams,
                    minConfidence: confidence
                });

                const fitness = await fitnessEvaluator.evaluateFitness(
                    genome,
                    testScenarios.optimalConditions.slice(0, testDataSize),
                    initialBalance
                );

                results.push({ confidence, fitness: fitness.overallScore });
                console.log(`Confidence ${confidence}%: Score ${fitness.overallScore.toFixed(2)}`);
            }

            // Mal by existovať optimálny bod
            const scores = results.map(r => r.fitness);
            const maxScore = Math.max(...scores);
            expect(maxScore).toBeGreaterThan(0);
        });

        it('should test sensitivity to risk level', async () => {
            const baseParams = {
                minConfidence: 75,
                minSignalStrength: 70,
                trendConfirmationPeriod: 5
            };

            const riskLevels: ('low' | 'medium' | 'high')[] = ['low', 'medium', 'high'];
            const results = [];

            for (const riskLevel of riskLevels) {
                const genome = new StrategyGenome({
                    ...baseParams,
                    maxRiskLevel: riskLevel
                });

                const fitness = await fitnessEvaluator.evaluateFitness(
                    genome,
                    testScenarios.optimalConditions.slice(0, testDataSize),
                    initialBalance
                );

                results.push({ riskLevel, fitness: fitness.overallScore });
                console.log(`Risk Level ${riskLevel}: Score ${fitness.overallScore.toFixed(2)}`);
            }

            // Všetky úrovne rizika by mali produkovať nejaký výsledok
            results.forEach(result => {
                expect(result.fitness).toBeGreaterThanOrEqual(0);
            });
        });
    });

    describe('Evolution Performance Benchmarks', () => {
        it('should benchmark evolution speed and convergence', async () => {
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: 10,
                generations: 5,
                mutationRate: 0.2,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });

            const startTime = Date.now();
            
            const result = await evolutionFramework.evolve(
                testScenarios.optimalConditions.slice(0, 100), // Menší dataset
                initialBalance
            );

            const executionTime = Date.now() - startTime;

            expect(result.bestFitness.overallScore).toBeGreaterThan(0);
            expect(result.generationStats).toHaveLength(5);
            expect(executionTime).toBeLessThan(60000); // Max 60 sekúnd

            console.log(`🚀 Evolution Benchmark:`);
            console.log(`  Execution Time: ${executionTime}ms`);
            console.log(`  Best Fitness: ${result.bestFitness.overallScore.toFixed(2)}`);
            console.log(`  Total Evaluations: ${result.totalEvaluations}`);
            console.log(`  Evaluations/Second: ${(result.totalEvaluations / (executionTime / 1000)).toFixed(2)}`);
        });

        it('should test convergence quality', async () => {
            const evolutionFramework = new AgentEvolutionFramework({
                populationSize: 15,
                generations: 8,
                mutationRate: 0.15,
                crossoverRate: 0.8,
                elitismRate: 0.2,
                tournamentSize: 3
            });

            const result = await evolutionFramework.evolve(
                testScenarios.optimalConditions.slice(0, 150),
                initialBalance
            );

            // Skontroluj konvergenciu
            const firstGenBest = result.generationStats[0].bestFitness;
            const lastGenBest = result.generationStats[result.generationStats.length - 1].bestFitness;
            
            // Fitness by sa mala zlepšiť alebo zostať stabilná
            expect(lastGenBest).toBeGreaterThanOrEqual(firstGenBest * 0.8); // Tolerancia 20%

            // Skontroluj diverzitu
            const avgDiversity = result.generationStats.reduce((sum, stat) => sum + stat.diversity, 0) / result.generationStats.length;
            expect(avgDiversity).toBeGreaterThan(0);

            console.log(`📈 Convergence Analysis:`);
            console.log(`  First Gen Best: ${firstGenBest.toFixed(2)}`);
            console.log(`  Last Gen Best: ${lastGenBest.toFixed(2)}`);
            console.log(`  Improvement: ${((lastGenBest - firstGenBest) / firstGenBest * 100).toFixed(2)}%`);
            console.log(`  Average Diversity: ${avgDiversity.toFixed(4)}`);
        });
    });

    describe('Stress Testing', () => {
        it('should handle extreme market conditions', async () => {
            const robustGenome = new StrategyGenome({
                minConfidence: 85,
                minSignalStrength: 80,
                maxRiskLevel: 'low',
                maxConsecutiveLosses: 2,
                stopLoss: 0.5 // Prísny stop loss
            });

            // Vytvor extrémne volatilné dáta
            const extremeData = testScenarios.volatileMarket.slice(0, testDataSize).map(data => ({
                ...data,
                price: data.price * (1 + (Math.random() - 0.5) * 0.1), // ±5% náhodná volatilita
                volume: data.volume * (Math.random() * 3) // 0-3x objem
            }));

            const fitness = await fitnessEvaluator.evaluateFitness(
                robustGenome,
                extremeData,
                initialBalance
            );

            // Stratégia by mala prežiť extrémne podmienky
            expect(fitness.maxDrawdown).toBeLessThan(0.8); // Max 80% drawdown
            expect(fitness.overallScore).toBeGreaterThanOrEqual(0);

            console.log(`💥 Stress Test Results:`);
            console.log(`  Max Drawdown: ${(fitness.maxDrawdown * 100).toFixed(2)}%`);
            console.log(`  Win Rate: ${(fitness.winRate * 100).toFixed(2)}%`);
            console.log(`  Total Trades: ${fitness.totalTrades}`);
        });

        it('should test with limited data', async () => {
            const genome = new StrategyGenome();
            
            // Test s veľmi malým datasetom
            const smallData = testScenarios.optimalConditions.slice(0, 20);
            
            const fitness = await fitnessEvaluator.evaluateFitness(
                genome,
                smallData,
                initialBalance
            );

            // Aj s malými dátami by mal vrátiť validný výsledok
            expect(fitness).toHaveProperty('overallScore');
            expect(fitness.overallScore).toBeGreaterThanOrEqual(0);
            expect(fitness.totalTrades).toBeGreaterThanOrEqual(0);
        });
    });
});
