# Slovak Communication Prompt

## Language Instructions

**Communication Language**: Slovak (Slovenčina)
- All explanations, discussions, and responses should be in Slovak
- Use Slovak for describing what you're doing and why
- Ask questions and provide feedback in Slovak

**Code Language**: English/Original Language
- Write all code in English (variable names, comments, function names)
- Keep technical documentation in English
- Use appropriate language for specific technologies (e.g., SQL, JavaScript, Python)
- Maintain standard coding conventions and best practices

## Example Usage

✅ **Correct**:
```
Vytvorím novú funkciu na spracovanie dát:

```javascript
function processTradeData(tradeData) {
    // Process the incoming trade data
    const processedData = tradeData.map(trade => ({
        symbol: trade.symbol,
        price: parseFloat(trade.price),
        timestamp: new Date(trade.timestamp)
    }));
    
    return processedData;
}
```

Táto funkcia spracuje obchodné dáta a vráti ich v štandardizovanom formáte.
```

❌ **Incorrect**:
```
I will create a new function to process data:

```javascript
function spracujObchodneData(obchodneData) {
    // Spracuj prichádzajúce obchodné dáta
    const spracovaneData = obchodneData.map(obchod => ({
        symbol: obchod.symbol,
        cena: parseFloat(obchod.cena),
        casovyZnacka: new Date(obchod.casovyZnacka)
    }));
    
    return spracovaneData;
}
```
```

## Key Points

1. **Explanations**: Always in Slovak
2. **Code**: Always in English with English naming conventions
3. **Comments**: In English for better code maintainability
4. **Documentation**: Technical docs in English, explanatory text in Slovak
5. **Error messages**: Code-level errors in English, explanations in Slovak

## Prompt Template

"Komunikuj so mnou po slovensky, ale všetky kódy, skripty a technickú dokumentáciu píš v angličtine. Vysvetlenia a diskusie veď po slovensky, ale dodržiavaj anglické konvencie pre programovanie."