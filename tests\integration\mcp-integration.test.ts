/**
 * MCP Integration Tests
 * Komplexné testy pre integráciu medzi trading-agent a MCP serverom
 */

import { MCPTradingClient } from '../../src/utils/mcp-client';
import DataFetcher from '../../src/data/fetcher';

describe('MCP Integration Tests', () => {
  let mcpClient: MCPTradingClient;
  let dataFetcher: DataFetcher;

  beforeAll(async () => {
    mcpClient = new MCPTradingClient();
    dataFetcher = new DataFetcher();
  });

  afterAll(async () => {
    await mcpClient.disconnect();
  });

  describe('MCP Client Tests', () => {
    test('should connect to MCP server', async () => {
      const isConnected = await mcpClient.testConnection();
      expect(isConnected).toBe(true);
    }, 30000);

    test('should fetch forex data', async () => {
      const forexData = await mcpClient.getForexData('EURUSD', '1h', '1d');
      
      expect(forexData).toBeDefined();
      expect(forexData.data).toBeDefined();
      expect(Array.isArray(forexData.data)).toBe(true);
      expect(forexData.data.length).toBeGreaterThan(0);
      
      // Kontrola štruktúry dát
      const firstItem = forexData.data[0];
      expect(firstItem).toHaveProperty('timestamp');
      expect(firstItem).toHaveProperty('open');
      expect(firstItem).toHaveProperty('high');
      expect(firstItem).toHaveProperty('low');
      expect(firstItem).toHaveProperty('close');
    }, 15000);

    test('should fetch stock data', async () => {
      const stockData = await mcpClient.getStockData('AAPL', '1h', '1d');
      
      expect(stockData).toBeDefined();
      expect(stockData.data).toBeDefined();
      expect(Array.isArray(stockData.data)).toBe(true);
      expect(stockData.data.length).toBeGreaterThan(0);
    }, 15000);

    test('should calculate market indicators', async () => {
      const indicators = await mcpClient.getMarketIndicators('EURUSD', ['rsi', 'macd', 'sma'], 14);
      
      expect(indicators).toBeDefined();
      expect(indicators.indicators).toBeDefined();
      expect(indicators.indicators.rsi).toBeDefined();
      expect(indicators.indicators.macd).toBeDefined();
      expect(indicators.indicators.sma).toBeDefined();
      
      // Kontrola hodnôt
      expect(typeof indicators.indicators.rsi).toBe('number');
      expect(indicators.indicators.rsi).toBeGreaterThanOrEqual(0);
      expect(indicators.indicators.rsi).toBeLessThanOrEqual(100);
    }, 15000);

    test('should handle connection errors gracefully', async () => {
      // Simulácia chyby - pokus o pripojenie k neexistujúcemu serveru
      const badClient = new MCPTradingClient();
      // Modifikácia cesty na neexistujúci server
      (badClient as any).transport = null;
      
      await expect(badClient.getForexData('EURUSD')).rejects.toThrow();
    });
  });

  describe('DataFetcher Integration Tests', () => {
    test('should test MCP connection through DataFetcher', async () => {
      const isConnected = await dataFetcher.testMCPConnection();
      expect(isConnected).toBe(true);
    }, 30000);

    test('should fetch market data with MCP fallback', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-01-02';
      
      const marketData = await dataFetcher.fetchMarketData('EURUSD', startDate, endDate);
      
      expect(marketData).toBeDefined();
      expect(typeof marketData).toBe('object');
      
      // Kontrola formátu dát
      const dateKeys = Object.keys(marketData);
      if (dateKeys.length > 0) {
        const firstDateData = marketData[dateKeys[0]];
        expect(firstDateData).toHaveProperty('open');
        expect(firstDateData).toHaveProperty('high');
        expect(firstDateData).toHaveProperty('low');
        expect(firstDateData).toHaveProperty('close');
      }
    }, 20000);

    test('should fetch market indicators through DataFetcher', async () => {
      const indicators = await dataFetcher.fetchMarketIndicators('EURUSD', ['rsi', 'macd'], 14);
      
      expect(indicators).toBeDefined();
      expect(indicators.symbol).toBe('EURUSD');
      expect(indicators.indicators).toBeDefined();
      expect(indicators.indicators.rsi).toBeDefined();
      expect(indicators.indicators.macd).toBeDefined();
    }, 15000);

    test('should get MCP status', () => {
      const status = dataFetcher.getMCPStatus();
      
      expect(status).toBeDefined();
      expect(status).toHaveProperty('available');
      expect(status).toHaveProperty('using');
      expect(typeof status.available).toBe('boolean');
      expect(typeof status.using).toBe('boolean');
    });

    test('should toggle MCP mode', () => {
      const originalStatus = dataFetcher.getMCPStatus();
      
      // Vypnutie MCP
      dataFetcher.toggleMCP(false);
      let newStatus = dataFetcher.getMCPStatus();
      expect(newStatus.using).toBe(false);
      
      // Zapnutie MCP
      dataFetcher.toggleMCP(true);
      newStatus = dataFetcher.getMCPStatus();
      expect(newStatus.using).toBe(true);
      
      // Obnovenie pôvodného stavu
      dataFetcher.toggleMCP(originalStatus.using);
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle invalid forex pair', async () => {
      await expect(mcpClient.getForexData('INVALID', '1h', '1d')).rejects.toThrow();
    }, 10000);

    test('should handle invalid stock symbol', async () => {
      await expect(mcpClient.getStockData('INVALID123', '1h', '1d')).rejects.toThrow();
    }, 10000);

    test('should handle invalid indicators', async () => {
      await expect(mcpClient.getMarketIndicators('EURUSD', ['invalid_indicator'], 14)).rejects.toThrow();
    }, 10000);
  });

  describe('Performance Tests', () => {
    test('should fetch data within reasonable time', async () => {
      const startTime = Date.now();
      
      await mcpClient.getForexData('EURUSD', '1h', '1d');
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(10000); // Menej ako 10 sekúnd
    }, 15000);

    test('should handle multiple concurrent requests', async () => {
      const promises = [
        mcpClient.getForexData('EURUSD', '1h', '1d'),
        mcpClient.getForexData('GBPUSD', '1h', '1d'),
        mcpClient.getStockData('AAPL', '1h', '1d')
      ];
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.data).toBeDefined();
      });
    }, 30000);
  });

  describe('Data Quality Tests', () => {
    test('should return valid OHLC data structure', async () => {
      const forexData = await mcpClient.getForexData('EURUSD', '1h', '1d');
      
      expect(forexData.data.length).toBeGreaterThan(0);
      
      forexData.data.forEach((candle: any) => {
        expect(candle.open).toBeGreaterThan(0);
        expect(candle.high).toBeGreaterThanOrEqual(candle.open);
        expect(candle.high).toBeGreaterThanOrEqual(candle.close);
        expect(candle.low).toBeLessThanOrEqual(candle.open);
        expect(candle.low).toBeLessThanOrEqual(candle.close);
        expect(candle.close).toBeGreaterThan(0);
      });
    }, 15000);

    test('should return chronologically ordered data', async () => {
      const forexData = await mcpClient.getForexData('EURUSD', '1h', '1d');
      
      for (let i = 1; i < forexData.data.length; i++) {
        const prevTimestamp = new Date(forexData.data[i-1].timestamp).getTime();
        const currTimestamp = new Date(forexData.data[i].timestamp).getTime();
        expect(currTimestamp).toBeGreaterThan(prevTimestamp);
      }
    }, 15000);

    test('should return reasonable indicator values', async () => {
      const indicators = await mcpClient.getMarketIndicators('EURUSD', ['rsi', 'macd', 'sma'], 14);
      
      // RSI by mal byť medzi 0 a 100
      expect(indicators.indicators.rsi).toBeGreaterThanOrEqual(0);
      expect(indicators.indicators.rsi).toBeLessThanOrEqual(100);
      
      // SMA by mala byť rozumná hodnota pre EURUSD (okolo 1.0-1.3)
      expect(indicators.indicators.sma).toBeGreaterThan(0.5);
      expect(indicators.indicators.sma).toBeLessThan(2.0);
      
      // MACD by mal mať správnu štruktúru
      expect(indicators.indicators.macd).toHaveProperty('macd');
      expect(indicators.indicators.macd).toHaveProperty('signal');
      expect(indicators.indicators.macd).toHaveProperty('histogram');
    }, 15000);
  });

  describe('Stress Tests', () => {
    test('should handle rapid sequential requests', async () => {
      const requests: Promise<any>[] = [];
      
      for (let i = 0; i < 5; i++) {
        requests.push(mcpClient.getForexData('EURUSD', '1h', '1d'));
      }
      
      const results = await Promise.all(requests);
      
      expect(results).toHaveLength(5);
      results.forEach((result: any) => {
        expect(result).toBeDefined();
        expect(result.data).toBeDefined();
      });
    }, 45000);

    test('should maintain connection stability', async () => {
      // Test pripojenia, odpojenia a opätovného pripojenia
      await mcpClient.disconnect();
      
      const isReconnected = await mcpClient.testConnection();
      expect(isReconnected).toBe(true);
      
      const data = await mcpClient.getForexData('EURUSD', '1h', '1d');
      expect(data).toBeDefined();
    }, 30000);
  });

  describe('Integration with Trading System', () => {
    test('should integrate with DataFetcher for trading decisions', async () => {
      // Simulácia trading workflow
      const symbol = 'EURUSD';
      const startDate = '2024-01-01';
      const endDate = '2024-01-02';
      
      // 1. Získanie market dát
      const marketData = await dataFetcher.fetchMarketData(symbol, startDate, endDate);
      expect(marketData).toBeDefined();
      
      // 2. Získanie indikátorov
      const indicators = await dataFetcher.fetchMarketIndicators(symbol, ['rsi', 'macd']);
      expect(indicators).toBeDefined();
      
      // 3. Simulácia trading rozhodnutia na základe RSI
      const rsi = indicators.indicators.rsi;
      let tradingSignal = 'HOLD';
      
      if (rsi < 30) {
        tradingSignal = 'BUY';
      } else if (rsi > 70) {
        tradingSignal = 'SELL';
      }
      
      expect(['BUY', 'SELL', 'HOLD']).toContain(tradingSignal);
      
      console.log(`Trading signal for ${symbol}: ${tradingSignal} (RSI: ${rsi.toFixed(2)})`);
    }, 25000);
  });
});