// tests/evolution/framework/BayesianOptimizer.ts
// Bayesian optimalizácia pre trading stratégie

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface BayesianConfig {
    acquisitionFunction: 'expected_improvement' | 'upper_confidence_bound' | 'probability_improvement';
    initialSamples: number;
    maxIterations: number;
    explorationWeight?: number;
    xi?: number; // exploration parameter for EI
    kappa?: number; // exploration parameter for UCB
}

export interface BayesianResult {
    bestParameters: any;
    bestFitness: FitnessResult;
    convergenceHistory: number[];
    allEvaluations: { parameters: any; fitness: FitnessResult }[];
    acquisitionHistory: number[];
    executionTime: number;
}

export interface GaussianProcess {
    mean: (x: any) => number;
    variance: (x: any) => number;
    update: (x: any, y: number) => void;
}

export class BayesianOptimizer {
    private config: BayesianConfig;
    private evaluations: { parameters: any; fitness: FitnessResult }[] = [];
    private gaussianProcess: GaussianProcess;

    constructor(config: BayesianConfig) {
        this.config = {
            explorationWeight: 0.1,
            xi: 0.01,
            kappa: 2.576,
            ...config
        };
        
        this.gaussianProcess = this.createGaussianProcess();
    }

    /**
     * Vykonaj Bayesian optimalizáciu
     */
    async optimize(
        parameterBounds: { [key: string]: { min: number; max: number } },
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<BayesianResult> {
        const startTime = Date.now();
        
        console.log(`🧠 Bayesian Optimization: ${this.config.maxIterations} iterations`);
        console.log(`   Acquisition Function: ${this.config.acquisitionFunction}`);
        
        this.evaluations = [];
        const convergenceHistory: number[] = [];
        const acquisitionHistory: number[] = [];
        
        // Počiatočné náhodné vzorky
        console.log(`🎲 Generating ${this.config.initialSamples} initial samples...`);
        
        for (let i = 0; i < this.config.initialSamples; i++) {
            const parameters = this.sampleRandomParameters(parameterBounds);
            const fitness = await this.evaluateParameters(parameters, marketData, initialBalance, fitnessEvaluator);
            
            this.evaluations.push({ parameters, fitness });
            convergenceHistory.push(fitness.overallScore);
            
            // Aktualizuj Gaussian Process
            this.gaussianProcess.update(parameters, fitness.overallScore);
        }
        
        let bestEvaluation = this.getBestEvaluation();
        console.log(`   Initial best score: ${bestEvaluation.fitness.overallScore.toFixed(2)}`);
        
        // Iteratívna optimalizácia
        for (let iteration = this.config.initialSamples; iteration < this.config.maxIterations; iteration++) {
            console.log(`🔍 Iteration ${iteration + 1}/${this.config.maxIterations}`);
            
            // Nájdi ďalší bod na vyhodnotenie pomocou acquisition function
            const nextParameters = await this.acquireNextPoint(parameterBounds);
            const acquisitionValue = this.calculateAcquisitionValue(nextParameters);
            
            // Vyhodnoť nový bod
            const fitness = await this.evaluateParameters(nextParameters, marketData, initialBalance, fitnessEvaluator);
            
            this.evaluations.push({ parameters: nextParameters, fitness });
            convergenceHistory.push(fitness.overallScore);
            acquisitionHistory.push(acquisitionValue);
            
            // Aktualizuj Gaussian Process
            this.gaussianProcess.update(nextParameters, fitness.overallScore);
            
            // Aktualizuj najlepší výsledok
            if (fitness.overallScore > bestEvaluation.fitness.overallScore) {
                bestEvaluation = { parameters: nextParameters, fitness };
                console.log(`   🎯 New best score: ${fitness.overallScore.toFixed(2)}`);
            }
            
            // Early stopping ak sa výkonnosť nezlepšuje
            if (iteration > this.config.initialSamples + 5) {
                const recentBest = Math.max(...convergenceHistory.slice(-5));
                const previousBest = Math.max(...convergenceHistory.slice(-10, -5));
                
                if (recentBest <= previousBest * 1.01) { // Menej ako 1% zlepšenie
                    console.log(`   ⏹️ Early stopping - no significant improvement`);
                    break;
                }
            }
        }
        
        const executionTime = Date.now() - startTime;
        console.log(`✅ Bayesian Optimization completed in ${executionTime}ms`);
        
        return {
            bestParameters: bestEvaluation.parameters,
            bestFitness: bestEvaluation.fitness,
            convergenceHistory,
            allEvaluations: this.evaluations,
            acquisitionHistory,
            executionTime
        };
    }

    /**
     * Multi-objective Bayesian optimalizácia
     */
    async multiObjectiveOptimize(
        parameterBounds: { [key: string]: { min: number; max: number } },
        objectives: string[],
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<BayesianResult & { paretoFront: any[] }> {
        // Zjednodušená implementácia - optimalizuj weighted sum
        const weights = objectives.reduce((w, obj) => ({ ...w, [obj]: 1 / objectives.length }), {});
        
        const result = await this.optimize(parameterBounds, marketData, initialBalance, fitnessEvaluator);
        
        // Nájdi Pareto front z všetkých vyhodnotení
        const paretoFront = this.extractParetoFront(result.allEvaluations, objectives);
        
        return {
            ...result,
            paretoFront
        };
    }

    /**
     * Nájdi ďalší bod na vyhodnotenie
     */
    private async acquireNextPoint(parameterBounds: { [key: string]: { min: number; max: number } }): Promise<any> {
        const candidates = [];
        const numCandidates = 1000; // Počet kandidátskych bodov
        
        // Vygeneruj kandidátskych bodov
        for (let i = 0; i < numCandidates; i++) {
            candidates.push(this.sampleRandomParameters(parameterBounds));
        }
        
        // Vyhodnoť acquisition function pre každého kandidáta
        let bestCandidate = candidates[0];
        let bestAcquisitionValue = this.calculateAcquisitionValue(bestCandidate);
        
        for (const candidate of candidates) {
            const acquisitionValue = this.calculateAcquisitionValue(candidate);
            
            if (acquisitionValue > bestAcquisitionValue) {
                bestCandidate = candidate;
                bestAcquisitionValue = acquisitionValue;
            }
        }
        
        return bestCandidate;
    }

    /**
     * Vypočítaj hodnotu acquisition function
     */
    private calculateAcquisitionValue(parameters: any): number {
        const mean = this.gaussianProcess.mean(parameters);
        const variance = this.gaussianProcess.variance(parameters);
        const sigma = Math.sqrt(variance);
        
        const bestValue = this.getBestEvaluation().fitness.overallScore;
        
        switch (this.config.acquisitionFunction) {
            case 'expected_improvement':
                return this.expectedImprovement(mean, sigma, bestValue);
            
            case 'upper_confidence_bound':
                return this.upperConfidenceBound(mean, sigma);
            
            case 'probability_improvement':
                return this.probabilityOfImprovement(mean, sigma, bestValue);
            
            default:
                return this.expectedImprovement(mean, sigma, bestValue);
        }
    }

    /**
     * Expected Improvement acquisition function
     */
    private expectedImprovement(mean: number, sigma: number, bestValue: number): number {
        if (sigma === 0) return 0;
        
        const xi = this.config.xi!;
        const improvement = mean - bestValue - xi;
        const z = improvement / sigma;
        
        return improvement * this.normalCDF(z) + sigma * this.normalPDF(z);
    }

    /**
     * Upper Confidence Bound acquisition function
     */
    private upperConfidenceBound(mean: number, sigma: number): number {
        const kappa = this.config.kappa!;
        return mean + kappa * sigma;
    }

    /**
     * Probability of Improvement acquisition function
     */
    private probabilityOfImprovement(mean: number, sigma: number, bestValue: number): number {
        if (sigma === 0) return 0;
        
        const xi = this.config.xi!;
        const z = (mean - bestValue - xi) / sigma;
        
        return this.normalCDF(z);
    }

    /**
     * Vytvor zjednodušený Gaussian Process
     */
    private createGaussianProcess(): GaussianProcess {
        const observations: { x: any; y: number }[] = [];
        
        return {
            mean: (x: any) => {
                if (observations.length === 0) return 0;
                
                // Zjednodušená implementácia - váhovaný priemer na základe vzdialenosti
                let weightedSum = 0;
                let totalWeight = 0;
                
                for (const obs of observations) {
                    const distance = this.calculateDistance(x, obs.x);
                    const weight = Math.exp(-distance * 2); // RBF kernel
                    
                    weightedSum += weight * obs.y;
                    totalWeight += weight;
                }
                
                return totalWeight > 0 ? weightedSum / totalWeight : 0;
            },
            
            variance: (x: any) => {
                if (observations.length === 0) return 1;
                
                // Zjednodušená implementácia - vyššia variancia ďalej od pozorovaní
                let minDistance = Infinity;
                
                for (const obs of observations) {
                    const distance = this.calculateDistance(x, obs.x);
                    minDistance = Math.min(minDistance, distance);
                }
                
                return Math.exp(-minDistance) + 0.01; // Minimálna variancia
            },
            
            update: (x: any, y: number) => {
                observations.push({ x: { ...x }, y });
            }
        };
    }

    /**
     * Vypočítaj vzdialenosť medzi parametrami
     */
    private calculateDistance(params1: any, params2: any): number {
        let distance = 0;
        const keys = Object.keys(params1);
        
        for (const key of keys) {
            const diff = (params1[key] - params2[key]) || 0;
            distance += diff * diff;
        }
        
        return Math.sqrt(distance);
    }

    /**
     * Vyhodnoť parametre
     */
    private async evaluateParameters(
        parameters: any,
        marketData: EnhancedMarketData[],
        initialBalance: number,
        fitnessEvaluator: FitnessEvaluator
    ): Promise<FitnessResult> {
        const genome = new StrategyGenome(parameters);
        return await fitnessEvaluator.evaluateFitness(genome, marketData, initialBalance);
    }

    /**
     * Vzorkuj náhodné parametre
     */
    private sampleRandomParameters(bounds: { [key: string]: { min: number; max: number } }): any {
        const parameters: any = {};
        
        for (const [key, bound] of Object.entries(bounds)) {
            parameters[key] = Math.random() * (bound.max - bound.min) + bound.min;
        }
        
        return parameters;
    }

    /**
     * Nájdi najlepšie vyhodnotenie
     */
    private getBestEvaluation(): { parameters: any; fitness: FitnessResult } {
        return this.evaluations.reduce((best, current) => 
            current.fitness.overallScore > best.fitness.overallScore ? current : best
        );
    }

    /**
     * Extrahuj Pareto front
     */
    private extractParetoFront(evaluations: any[], objectives: string[]): any[] {
        const paretoFront = [];
        
        for (const evaluation of evaluations) {
            let isDominated = false;
            
            for (const other of evaluations) {
                if (this.dominates(other.fitness, evaluation.fitness, objectives)) {
                    isDominated = true;
                    break;
                }
            }
            
            if (!isDominated) {
                paretoFront.push(evaluation);
            }
        }
        
        return paretoFront;
    }

    /**
     * Skontroluj dominanciu
     */
    private dominates(fitness1: FitnessResult, fitness2: FitnessResult, objectives: string[]): boolean {
        let betterInAll = true;
        let betterInSome = false;
        
        for (const objective of objectives) {
            const value1 = (fitness1 as any)[objective] || 0;
            const value2 = (fitness2 as any)[objective] || 0;
            
            // Pre maxDrawdown chceme minimalizovať
            const isMinimizing = objective === 'maxDrawdown';
            
            if (isMinimizing) {
                if (value1 > value2) betterInAll = false;
                if (value1 < value2) betterInSome = true;
            } else {
                if (value1 < value2) betterInAll = false;
                if (value1 > value2) betterInSome = true;
            }
        }
        
        return betterInAll && betterInSome;
    }

    // Pomocné matematické funkcie
    private normalPDF(x: number): number {
        return Math.exp(-0.5 * x * x) / Math.sqrt(2 * Math.PI);
    }

    private normalCDF(x: number): number {
        // Aproximácia pomocou error function
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;

        const sign = x < 0 ? -1 : 1;
        x = Math.abs(x) / Math.sqrt(2.0);

        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

        return 0.5 * (1.0 + sign * y);
    }
}
