#!/usr/bin/env node

/**
 * MCP Task Runner - <PERSON><PERSON><PERSON> build a testovanie MCP servera
 * Poskytuje kompletný workflow pre MCP server development
 */

import { execSync, spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { MCPTradingClient } from '../src/utils/mcp-client';
import DataFetcher from '../src/data/fetcher';

interface TaskResult {
  success: boolean;
  message: string;
  duration: number;
  data?: any;
}

class MCPTaskRunner {
  private mcpServerPath = 'D:\\MCP\\trading-data-server';
  private buildPath = path.join(this.mcpServerPath, 'build');
  private logFile = 'mcp-task-runner.log';
  private results: TaskResult[] = [];

  constructor() {
    console.log('🚀 MCP Task Runner inicializovaný');
    this.initializeLogFile();
  }

  /**
   * Hlavný workflow - spustí v<PERSON><PERSON><PERSON> v správnom poradí
   */
  async runFullWorkflow(): Promise<void> {
    console.log('\n=== MCP TASK RUNNER - FULL WORKFLOW ===\n');
    
    const tasks = [
      { name: 'Build MCP Server', fn: () => this.buildMCPServer() },
      { name: 'Test MCP Server Startup', fn: () => this.testMCPServerStartup() },
      { name: 'Integration Test', fn: () => this.runIntegrationTest() },
      { name: 'Add MCP to SuperAgent', fn: () => this.addMCPToSuperAgent() },
      { name: 'Full System Test', fn: () => this.runFullSystemTest() }
    ];

    for (const task of tasks) {
      console.log(`\n📋 Spúšťam úlohu: ${task.name}`);
      const startTime = Date.now();
      
      try {
        const result = await task.fn();
        const duration = Date.now() - startTime;
        
        this.results.push({
          success: true,
          message: `${task.name} úspešne dokončená`,
          duration,
          data: result
        });
        
        console.log(`✅ ${task.name} dokončená za ${duration}ms`);
        
      } catch (error) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        this.results.push({
          success: false,
          message: `${task.name} zlyhala: ${errorMessage}`,
          duration
        });
        
        console.error(`❌ ${task.name} zlyhala: ${errorMessage}`);
        this.logError(`Task ${task.name} failed`, error);
        
        // Pokračuj aj pri chybe (pre debugging)
        console.log('⚠️ Pokračujem s ďalšími úlohami...\n');
      }
    }

    this.printSummary();
  }

  /**
   * 1. Build MCP Server
   */
  async buildMCPServer(): Promise<any> {
    console.log('🔨 Kompilujem MCP server...');
    
    if (!fs.existsSync(this.mcpServerPath)) {
      throw new Error(`MCP server path neexistuje: ${this.mcpServerPath}`);
    }

    try {
      // Kontrola package.json
      const packageJsonPath = path.join(this.mcpServerPath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        throw new Error('package.json nenájdený v MCP server adresári');
      }

      // Spustenie npm run build
      console.log('📦 Spúšťam npm run build...');
      const buildOutput = execSync('npm run build', {
        cwd: this.mcpServerPath,
        encoding: 'utf8',
        timeout: 60000 // 60 sekúnd timeout
      });

      // Kontrola build výstupu
      if (!fs.existsSync(this.buildPath)) {
        throw new Error('Build adresár nebol vytvorený');
      }

      const indexPath = path.join(this.buildPath, 'index.js');
      if (!fs.existsSync(indexPath)) {
        throw new Error('index.js nebol vytvorený v build adresári');
      }

      console.log('✅ MCP server úspešne skompilovaný');
      this.logInfo('MCP server build successful', { buildOutput });
      
      return {
        buildPath: this.buildPath,
        indexPath,
        buildOutput: buildOutput.substring(0, 500) // Skrátený výstup
      };

    } catch (error) {
      this.logError('MCP server build failed', error);
      throw error;
    }
  }

  /**
   * 2. Test MCP Server Startup
   */
  async testMCPServerStartup(): Promise<any> {
    console.log('🧪 Testujem spustenie MCP servera...');
    
    const indexPath = path.join(this.buildPath, 'index.js');
    
    return new Promise((resolve, reject) => {
      const serverProcess = spawn('node', [indexPath], {
        cwd: this.mcpServerPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';
      let resolved = false;

      // Timeout pre test
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          serverProcess.kill();
          reject(new Error('MCP server startup timeout (10s)'));
        }
      }, 10000);

      serverProcess.stdout.on('data', (data) => {
        output += data.toString();
        console.log(`📡 Server output: ${data.toString().trim()}`);
        
        // Ak server vypíše správu o úspešnom štarte
        if (output.includes('Server running') || output.includes('listening') || output.includes('ready')) {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeout);
            serverProcess.kill();
            resolve({
              success: true,
              output: output.trim(),
              message: 'MCP server úspešne spustený'
            });
          }
        }
      });

      serverProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.error(`❌ Server error: ${data.toString().trim()}`);
      });

      serverProcess.on('close', (code) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          
          if (code === 0) {
            resolve({
              success: true,
              output: output.trim(),
              message: 'MCP server ukončený bez chyby'
            });
          } else {
            reject(new Error(`MCP server ukončený s kódom ${code}. Error: ${errorOutput}`));
          }
        }
      });

      serverProcess.on('error', (error) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          reject(error);
        }
      });
    });
  }

  /**
   * 3. Integration Test medzi trading-agent a MCP serverom
   */
  async runIntegrationTest(): Promise<any> {
    console.log('🔗 Spúšťam integračný test...');
    
    const mcpClient = new MCPTradingClient();
    const dataFetcher = new DataFetcher();
    
    try {
      // Test 1: Základné pripojenie
      console.log('🔌 Test 1: Základné pripojenie...');
      const connectionTest = await mcpClient.testConnection();
      
      if (!connectionTest) {
        throw new Error('MCP pripojenie zlyhalo');
      }

      // Test 2: Forex dáta
      console.log('💱 Test 2: Forex dáta...');
      const forexData = await mcpClient.getForexData('EURUSD', '1h', '1d');
      
      if (!forexData || !forexData.data) {
        throw new Error('Forex dáta neboli získané');
      }

      // Test 3: Akciové dáta
      console.log('📈 Test 3: Akciové dáta...');
      const stockData = await mcpClient.getStockData('AAPL', '1h', '1d');
      
      if (!stockData || !stockData.data) {
        throw new Error('Akciové dáta neboli získané');
      }

      // Test 4: Technické indikátory
      console.log('📊 Test 4: Technické indikátory...');
      const indicators = await mcpClient.getMarketIndicators('EURUSD', ['RSI', 'MACD'], 14);
      
      if (!indicators) {
        throw new Error('Indikátory neboli získané');
      }

      // Test 5: DataFetcher integrácia
      console.log('🔄 Test 5: DataFetcher integrácia...');
      const mcpStatus = await dataFetcher.testMCPConnection();
      
      if (!mcpStatus) {
        throw new Error('DataFetcher MCP integrácia zlyhala');
      }

      // Test 6: Získanie dát cez DataFetcher
      console.log('📊 Test 6: Dáta cez DataFetcher...');
      const marketData = await dataFetcher.fetchMarketData('EURUSD', '2024-01-01', '2024-01-02');
      
      if (!marketData) {
        throw new Error('Market dáta cez DataFetcher neboli získané');
      }

      await mcpClient.disconnect();

      const testResults = {
        connectionTest,
        forexDataCount: forexData.data?.length || 0,
        stockDataCount: stockData.data?.length || 0,
        indicatorsReceived: Object.keys(indicators.indicators || {}).length,
        dataFetcherMCP: mcpStatus,
        marketDataKeys: Object.keys(marketData).length
      };

      console.log('✅ Všetky integračné testy úspešné');
      this.logInfo('Integration tests successful', testResults);
      
      return testResults;

    } catch (error) {
      await mcpClient.disconnect();
      this.logError('Integration test failed', error);
      throw error;
    }
  }

  /**
   * 4. Pridanie MCP integrácie do SuperAgent
   */
  async addMCPToSuperAgent(): Promise<any> {
    console.log('🤖 Pridávam MCP integráciu do SuperAgent...');
    
    const superAgentPath = 'src/agent/superAgent.ts';
    
    try {
      // Čítanie aktuálneho SuperAgent kódu
      let superAgentCode = fs.readFileSync(superAgentPath, 'utf8');
      
      // Kontrola či už MCP integrácia existuje
      if (superAgentCode.includes('MCPTradingClient') || superAgentCode.includes('DataFetcher')) {
        console.log('ℹ️ MCP integrácia už existuje v SuperAgent');
        return { message: 'MCP integrácia už existuje', updated: false };
      }

      // Pridanie importov
      const mcpImports = `import { MCPTradingClient } from '../utils/mcp-client';
import DataFetcher from '../data/fetcher';
`;

      // Vloženie importov na začiatok (po existujúcich importoch)
      const importIndex = superAgentCode.lastIndexOf('import');
      const nextLineIndex = superAgentCode.indexOf('\n', importIndex);
      superAgentCode = superAgentCode.slice(0, nextLineIndex + 1) + mcpImports + superAgentCode.slice(nextLineIndex + 1);

      // Pridanie MCP funkcionalít do SuperAgent triedy
      const mcpMethods = `
    private mcpClient: MCPTradingClient;
    private dataFetcher: DataFetcher;

    constructor() {
        this.mcpClient = new MCPTradingClient();
        this.dataFetcher = new DataFetcher();
        log('SuperAgent inicializovaný s MCP podporou');
    }

    /**
     * Test MCP pripojenia pred spustením evolučného cyklu
     */
    async testMCPConnection(): Promise<boolean> {
        try {
            const isConnected = await this.dataFetcher.testMCPConnection();
            if (isConnected) {
                log('✅ MCP pripojenie úspešné - používam real-time dáta');
            } else {
                log('⚠️ MCP nedostupný - používam fallback dáta');
            }
            return isConnected;
        } catch (error) {
            log('❌ MCP test zlyhal:', error);
            return false;
        }
    }

    /**
     * Získanie real-time market dát pre optimalizáciu
     */
    async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {
        try {
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            
            return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);
        } catch (error) {
            log('❌ Chyba pri získavaní real-time dát:', error);
            return null;
        }
    }

    /**
     * Rozšírený evolučný cyklus s MCP dátami
     */
    async runFullCycleWithMCP(): Promise<void> {
        log('--- SuperAgent: Spúšťam rozšírený evolučný cyklus s MCP ---');
        
        // Test MCP pripojenia
        const mcpAvailable = await this.testMCPConnection();
        
        // Získanie real-time dát ak je MCP dostupný
        let realTimeData = null;
        if (mcpAvailable) {
            realTimeData = await this.getRealTimeMarketData();
            if (realTimeData) {
                log('📊 Real-time dáta získané, používam pre optimalizáciu');
            }
        }
        
        // Spustenie pôvodného cyklu
        await this.runFullCycle();
        
        // Ak máme real-time dáta, spustíme dodatočnú optimalizáciu
        if (realTimeData && Object.keys(realTimeData).length > 0) {
            log('🔄 Spúšťam dodatočnú optimalizáciu na real-time dátach...');
            
            // Konverzia real-time dát na formát pre backtest
            const convertedData = this.convertRealTimeDataForBacktest(realTimeData);
            
            if (convertedData.length > 0) {
                const realTimeOpt = optimizeStrategy(convertedData);
                log(\`Real-time optimalizácia: Win rate: \${(realTimeOpt.bestWinRate*100).toFixed(2)}%, Params: \${JSON.stringify(realTimeOpt.bestParams)}\`);
            }
        }
        
        // Odpojenie MCP
        await this.mcpClient.disconnect();
        log('🔌 MCP pripojenie ukončené');
    }

    /**
     * Konverzia real-time dát na formát pre backtest
     */
    private convertRealTimeDataForBacktest(realTimeData: any): any[] {
        const converted = [];
        
        for (const [date, data] of Object.entries(realTimeData)) {
            if (data && typeof data === 'object' && 'close' in data) {
                converted.push({
                    timestamp: new Date(date).getTime(),
                    price: (data as any).close,
                    volume: (data as any).volume || 1000,
                    high: (data as any).high || (data as any).close,
                    low: (data as any).low || (data as any).close,
                    open: (data as any).open || (data as any).close
                });
            }
        }
        
        return converted.sort((a, b) => a.timestamp - b.timestamp);
    }
`;

      // Vloženie metód do triedy (pred posledný })
      const classEndIndex = superAgentCode.lastIndexOf('}');
      superAgentCode = superAgentCode.slice(0, classEndIndex) + mcpMethods + '\n' + superAgentCode.slice(classEndIndex);

      // Uloženie aktualizovaného kódu
      fs.writeFileSync(superAgentPath, superAgentCode);
      
      console.log('✅ MCP integrácia pridaná do SuperAgent');
      this.logInfo('SuperAgent MCP integration added');
      
      return { message: 'MCP integrácia úspešne pridaná', updated: true };

    } catch (error) {
      this.logError('Failed to add MCP integration to SuperAgent', error);
      throw error;
    }
  }

  /**
   * 5. Kompletný systémový test
   */
  async runFullSystemTest(): Promise<any> {
    console.log('🔬 Spúšťam kompletný systémový test...');
    
    try {
      // Import SuperAgent s MCP integráciou
      const SuperAgent = require('../src/agent/superAgent').default;
      const superAgent = new SuperAgent();
      
      // Test MCP pripojenia cez SuperAgent
      console.log('🤖 Test SuperAgent MCP pripojenia...');
      const mcpTest = await superAgent.testMCPConnection();
      
      // Test získania real-time dát
      console.log('📊 Test real-time dát cez SuperAgent...');
      const realTimeData = await superAgent.getRealTimeMarketData('EURUSD');
      
      // Krátky test evolučného cyklu (bez plného behu)
      console.log('🧬 Test evolučného cyklu (skrátený)...');
      // Poznámka: Nespúšťame plný cyklus kvôli času
      
      const systemTestResults = {
        superAgentMCPTest: mcpTest,
        realTimeDataAvailable: realTimeData !== null,
        realTimeDataSize: realTimeData ? Object.keys(realTimeData).length : 0,
        timestamp: new Date().toISOString()
      };

      console.log('✅ Systémový test dokončený');
      this.logInfo('Full system test completed', systemTestResults);
      
      return systemTestResults;

    } catch (error) {
      this.logError('Full system test failed', error);
      throw error;
    }
  }

  /**
   * Výpis súhrnu všetkých úloh
   */
  private printSummary(): void {
    console.log('\n=== SÚHRN VÝSLEDKOV ===\n');
    
    let successCount = 0;
    let totalDuration = 0;
    
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${index + 1}. ${result.message} (${result.duration}ms)`);
      
      if (result.success) successCount++;
      totalDuration += result.duration;
    });
    
    console.log(`\n📊 Úspešnosť: ${successCount}/${this.results.length} úloh`);
    console.log(`⏱️ Celkový čas: ${totalDuration}ms`);
    console.log(`📝 Log súbor: ${this.logFile}\n`);
    
    // Uloženie súhrnu do súboru
    const summary = {
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: {
        total: this.results.length,
        successful: successCount,
        failed: this.results.length - successCount,
        totalDuration
      }
    };
    
    fs.writeFileSync('mcp-task-runner-summary.json', JSON.stringify(summary, null, 2));
    console.log('💾 Súhrn uložený do mcp-task-runner-summary.json');
  }

  /**
   * Inicializácia log súboru
   */
  private initializeLogFile(): void {
    const logHeader = `\n=== MCP Task Runner Log - ${new Date().toISOString()} ===\n`;
    fs.appendFileSync(this.logFile, logHeader);
  }

  /**
   * Logovanie informácií
   */
  private logInfo(message: string, data?: any): void {
    const logEntry = `[INFO] ${new Date().toISOString()} - ${message}${data ? '\n' + JSON.stringify(data, null, 2) : ''}\n`;
    fs.appendFileSync(this.logFile, logEntry);
  }

  /**
   * Logovanie chýb
   */
  private logError(message: string, error: any): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : '';
    const logEntry = `[ERROR] ${new Date().toISOString()} - ${message}\nError: ${errorMessage}\nStack: ${stack}\n\n`;
    fs.appendFileSync(this.logFile, logEntry);
  }

  /**
   * Spustenie jednotlivých úloh
   */
  async buildOnly(): Promise<void> {
    await this.buildMCPServer();
  }

  async testOnly(): Promise<void> {
    await this.testMCPServerStartup();
  }

  async integrationOnly(): Promise<void> {
    await this.runIntegrationTest();
  }

  async superAgentOnly(): Promise<void> {
    await this.addMCPToSuperAgent();
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const runner = new MCPTaskRunner();

  try {
    switch (args[0]) {
      case 'build':
        await runner.buildOnly();
        break;
      case 'test':
        await runner.testOnly();
        break;
      case 'integration':
        await runner.integrationOnly();
        break;
      case 'superagent':
        await runner.superAgentOnly();
        break;
      case 'full':
      default:
        await runner.runFullWorkflow();
        break;
    }
  } catch (error) {
    console.error('❌ Task runner zlyhal:', error);
    process.exit(1);
  }
}

// Spustenie ak je skript volaný priamo
if (require.main === module) {
  main();
}

export default MCPTaskRunner;