// tests/evolution/parameter-optimization.test.ts
// Testy pre optimalizáciu parametrov trading stratégií

import { ParameterOptimizer } from './framework/ParameterOptimizer';
import { GridSearchOptimizer } from './framework/GridSearchOptimizer';
import { BayesianOptimizer } from './framework/BayesianOptimizer';
import { StrategyGenome } from './framework/StrategyGenome';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Parameter Optimization', () => {
    let fitnessEvaluator: FitnessEvaluator;
    const initialBalance = 10000;
    const testDataSize = 150;

    beforeEach(() => {
        fitnessEvaluator = new FitnessEvaluator();
    });

    describe('Grid Search Optimization', () => {
        it('should perform grid search on confidence parameters', async () => {
            const gridSearch = new GridSearchOptimizer();
            
            const parameterSpace = {
                minConfidence: [70, 75, 80, 85, 90],
                minSignalStrength: [65, 70, 75, 80],
                maxRiskLevel: ['low', 'medium'] as const
            };

            const result = await gridSearch.optimize(
                parameterSpace,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result).toHaveProperty('bestParameters');
            expect(result).toHaveProperty('bestFitness');
            expect(result).toHaveProperty('allResults');
            expect(result.bestFitness.overallScore).toBeGreaterThan(0);
            
            // Skontroluj, že najlepšie parametre sú v definovanom priestore
            expect(parameterSpace.minConfidence).toContain(result.bestParameters.minConfidence);
            expect(parameterSpace.minSignalStrength).toContain(result.bestParameters.minSignalStrength);
            expect(parameterSpace.maxRiskLevel).toContain(result.bestParameters.maxRiskLevel);

            console.log(`🔍 Grid Search Results:`);
            console.log(`  Best Confidence: ${result.bestParameters.minConfidence}%`);
            console.log(`  Best Signal Strength: ${result.bestParameters.minSignalStrength}%`);
            console.log(`  Best Risk Level: ${result.bestParameters.maxRiskLevel}`);
            console.log(`  Best Fitness: ${result.bestFitness.overallScore.toFixed(2)}`);
            console.log(`  Total Combinations: ${result.allResults.length}`);
        });

        it('should find optimal risk management parameters', async () => {
            const gridSearch = new GridSearchOptimizer();
            
            const parameterSpace = {
                maxDailyTrades: [3, 5, 8, 10],
                maxConsecutiveLosses: [2, 3, 4],
                profitTarget: [1.5, 2.0, 2.5, 3.0],
                stopLoss: [0.5, 1.0, 1.5]
            };

            const result = await gridSearch.optimize(
                parameterSpace,
                testScenarios.volatileMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result.bestFitness.overallScore).toBeGreaterThan(0);
            expect(result.allResults.length).toBe(3 * 3 * 4 * 3); // 108 kombinácií

            // Najlepšie parametre by mali byť rozumné
            expect(result.bestParameters.profitTarget).toBeGreaterThan(result.bestParameters.stopLoss);
            expect(result.bestParameters.maxConsecutiveLosses).toBeGreaterThanOrEqual(2);
            expect(result.bestParameters.maxDailyTrades).toBeGreaterThanOrEqual(3);

            console.log(`⚖️ Risk Management Optimization:`);
            console.log(`  Max Daily Trades: ${result.bestParameters.maxDailyTrades}`);
            console.log(`  Max Consecutive Losses: ${result.bestParameters.maxConsecutiveLosses}`);
            console.log(`  Profit Target: ${result.bestParameters.profitTarget}`);
            console.log(`  Stop Loss: ${result.bestParameters.stopLoss}`);
        });

        it('should handle mixed parameter types', async () => {
            const gridSearch = new GridSearchOptimizer();
            
            const parameterSpace = {
                minConfidence: [75, 85],
                maxRiskLevel: ['low', 'medium', 'high'] as const,
                volumeConfirmation: [true, false],
                trendConfirmationPeriod: [3, 5, 7]
            };

            const result = await gridSearch.optimize(
                parameterSpace,
                testScenarios.trendingMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result.bestFitness.overallScore).toBeGreaterThan(0);
            expect(result.allResults.length).toBe(2 * 3 * 2 * 3); // 36 kombinácií

            // Skontroluj typy parametrov
            expect(typeof result.bestParameters.minConfidence).toBe('number');
            expect(typeof result.bestParameters.maxRiskLevel).toBe('string');
            expect(typeof result.bestParameters.volumeConfirmation).toBe('boolean');
            expect(typeof result.bestParameters.trendConfirmationPeriod).toBe('number');
        });
    });

    describe('Bayesian Optimization', () => {
        it('should perform Bayesian optimization efficiently', async () => {
            const bayesianOpt = new BayesianOptimizer({
                acquisitionFunction: 'expected_improvement',
                initialSamples: 5,
                maxIterations: 15
            });

            const parameterBounds = {
                minConfidence: { min: 60, max: 95 },
                minSignalStrength: { min: 50, max: 90 },
                volatilityThreshold: { min: 0.01, max: 0.05 }
            };

            const result = await bayesianOpt.optimize(
                parameterBounds,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result).toHaveProperty('bestParameters');
            expect(result).toHaveProperty('bestFitness');
            expect(result).toHaveProperty('convergenceHistory');
            expect(result.bestFitness.overallScore).toBeGreaterThan(0);

            // Bayesian optimization by mala byť efektívnejšia ako grid search
            expect(result.convergenceHistory.length).toBeLessThanOrEqual(20);

            console.log(`🧠 Bayesian Optimization Results:`);
            console.log(`  Best Confidence: ${result.bestParameters.minConfidence.toFixed(2)}%`);
            console.log(`  Best Signal Strength: ${result.bestParameters.minSignalStrength.toFixed(2)}%`);
            console.log(`  Best Volatility Threshold: ${result.bestParameters.volatilityThreshold.toFixed(4)}`);
            console.log(`  Best Fitness: ${result.bestFitness.overallScore.toFixed(2)}`);
            console.log(`  Iterations: ${result.convergenceHistory.length}`);
        });

        it('should show improvement over iterations', async () => {
            const bayesianOpt = new BayesianOptimizer({
                acquisitionFunction: 'upper_confidence_bound',
                initialSamples: 3,
                maxIterations: 10
            });

            const parameterBounds = {
                minConfidence: { min: 70, max: 90 },
                profitTarget: { min: 1.5, max: 3.0 },
                stopLoss: { min: 0.5, max: 1.5 }
            };

            const result = await bayesianOpt.optimize(
                parameterBounds,
                testScenarios.volatileMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            // Skontroluj konvergenciu
            const convergence = result.convergenceHistory;
            expect(convergence.length).toBeGreaterThan(3);

            // Posledné iterácie by mali mať lepšie výsledky ako prvé
            const firstHalf = convergence.slice(0, Math.floor(convergence.length / 2));
            const secondHalf = convergence.slice(Math.floor(convergence.length / 2));
            
            const avgFirstHalf = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
            const avgSecondHalf = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
            
            expect(avgSecondHalf).toBeGreaterThanOrEqual(avgFirstHalf * 0.9); // Tolerancia 10%

            console.log(`📈 Convergence Analysis:`);
            console.log(`  First Half Avg: ${avgFirstHalf.toFixed(2)}`);
            console.log(`  Second Half Avg: ${avgSecondHalf.toFixed(2)}`);
            console.log(`  Improvement: ${((avgSecondHalf - avgFirstHalf) / avgFirstHalf * 100).toFixed(2)}%`);
        });
    });

    describe('Multi-Objective Optimization', () => {
        it('should optimize for multiple objectives simultaneously', async () => {
            const multiObjOptimizer = new ParameterOptimizer({
                method: 'nsga2',
                populationSize: 20,
                generations: 10,
                objectives: ['winRate', 'profitFactor', 'maxDrawdown', 'sharpeRatio']
            });

            const parameterBounds = {
                minConfidence: { min: 65, max: 95 },
                minSignalStrength: { min: 60, max: 90 },
                maxRiskLevel: ['low', 'medium', 'high'],
                maxDailyTrades: { min: 3, max: 12 }
            };

            const result = await multiObjOptimizer.optimize(
                parameterBounds,
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result).toHaveProperty('paretoFront');
            expect(result).toHaveProperty('bestCompromise');
            expect(result.paretoFront.length).toBeGreaterThan(0);

            // Skontroluj Pareto front
            for (const solution of result.paretoFront) {
                expect(solution.fitness.winRate).toBeGreaterThanOrEqual(0);
                expect(solution.fitness.profitFactor).toBeGreaterThanOrEqual(0);
                expect(solution.fitness.maxDrawdown).toBeGreaterThanOrEqual(0);
                expect(solution.fitness.sharpeRatio).toBeGreaterThanOrEqual(-5); // Môže byť negatívny
            }

            console.log(`🎯 Multi-Objective Results:`);
            console.log(`  Pareto Front Size: ${result.paretoFront.length}`);
            console.log(`  Best Compromise Win Rate: ${(result.bestCompromise.fitness.winRate * 100).toFixed(2)}%`);
            console.log(`  Best Compromise Profit Factor: ${result.bestCompromise.fitness.profitFactor.toFixed(2)}`);
        });

        it('should find trade-offs between risk and return', async () => {
            const optimizer = new ParameterOptimizer({
                method: 'weighted_sum',
                objectives: {
                    profitFactor: 0.4,
                    maxDrawdown: -0.3, // Negatívna váha (minimalizovať)
                    winRate: 0.3
                }
            });

            const parameterBounds = {
                minConfidence: { min: 70, max: 95 },
                maxRiskLevel: ['low', 'medium', 'high'],
                profitTarget: { min: 1.5, max: 3.0 },
                stopLoss: { min: 0.5, max: 2.0 }
            };

            const result = await optimizer.optimize(
                parameterBounds,
                testScenarios.volatileMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(result.bestParameters).toBeDefined();
            expect(result.bestFitness.overallScore).toBeGreaterThan(0);

            // Optimalizácia by mala nájsť rozumný kompromis
            expect(result.bestFitness.profitFactor).toBeGreaterThan(0.8);
            expect(result.bestFitness.maxDrawdown).toBeLessThan(0.5);
            expect(result.bestFitness.winRate).toBeGreaterThan(0.3);

            console.log(`⚖️ Risk-Return Trade-off:`);
            console.log(`  Profit Factor: ${result.bestFitness.profitFactor.toFixed(2)}`);
            console.log(`  Max Drawdown: ${(result.bestFitness.maxDrawdown * 100).toFixed(2)}%`);
            console.log(`  Win Rate: ${(result.bestFitness.winRate * 100).toFixed(2)}%`);
        });
    });

    describe('Hyperparameter Tuning', () => {
        it('should tune genetic algorithm hyperparameters', async () => {
            const hyperparameterSpace = {
                populationSize: [10, 20, 30],
                mutationRate: [0.05, 0.1, 0.2],
                crossoverRate: [0.7, 0.8, 0.9],
                elitismRate: [0.1, 0.2, 0.3]
            };

            const results = [];

            for (const popSize of hyperparameterSpace.populationSize) {
                for (const mutRate of hyperparameterSpace.mutationRate) {
                    for (const crossRate of hyperparameterSpace.crossoverRate) {
                        for (const eliteRate of hyperparameterSpace.elitismRate) {
                            // Simuluj krátky evolučný beh
                            const mockResult = {
                                hyperparameters: { popSize, mutRate, crossRate, eliteRate },
                                performance: Math.random() * 100 + popSize * 0.5 - mutRate * 50
                            };
                            results.push(mockResult);
                        }
                    }
                }
            }

            // Nájdi najlepšie hyperparametre
            const bestResult = results.reduce((best, current) => 
                current.performance > best.performance ? current : best
            );

            expect(bestResult.performance).toBeGreaterThan(0);
            expect(bestResult.hyperparameters.popSize).toBeGreaterThanOrEqual(10);
            expect(bestResult.hyperparameters.mutRate).toBeGreaterThanOrEqual(0.05);

            console.log(`🔧 Best Hyperparameters:`);
            console.log(`  Population Size: ${bestResult.hyperparameters.popSize}`);
            console.log(`  Mutation Rate: ${bestResult.hyperparameters.mutRate}`);
            console.log(`  Crossover Rate: ${bestResult.hyperparameters.crossRate}`);
            console.log(`  Elitism Rate: ${bestResult.hyperparameters.eliteRate}`);
            console.log(`  Performance: ${bestResult.performance.toFixed(2)}`);
        });

        it('should validate hyperparameter sensitivity', async () => {
            const baseConfig = {
                populationSize: 20,
                mutationRate: 0.1,
                crossoverRate: 0.8,
                elitismRate: 0.2
            };

            // Test citlivosti na mutation rate
            const mutationRates = [0.05, 0.1, 0.15, 0.2, 0.25];
            const mutationResults = [];

            for (const rate of mutationRates) {
                // Simuluj výsledok s danou mutation rate
                const performance = 50 + Math.random() * 20 - Math.abs(rate - 0.15) * 100;
                mutationResults.push({ rate, performance });
            }

            // Nájdi optimálnu mutation rate
            const bestMutation = mutationResults.reduce((best, current) => 
                current.performance > best.performance ? current : best
            );

            expect(bestMutation.rate).toBeGreaterThan(0);
            expect(bestMutation.rate).toBeLessThan(0.3);

            console.log(`🎛️ Mutation Rate Sensitivity:`);
            mutationResults.forEach(result => {
                console.log(`  Rate ${result.rate}: Performance ${result.performance.toFixed(2)}`);
            });
            console.log(`  Optimal Rate: ${bestMutation.rate}`);
        });
    });

    describe('Adaptive Parameter Optimization', () => {
        it('should adapt parameters based on market conditions', async () => {
            const adaptiveOptimizer = new ParameterOptimizer({
                method: 'adaptive',
                adaptationPeriod: 50, // Adaptuj každých 50 dátových bodov
                learningRate: 0.1
            });

            const scenarios = [
                { name: 'Bull Market', data: testScenarios.trendingMarket },
                { name: 'Bear Market', data: testScenarios.volatileMarket },
                { name: 'Sideways', data: testScenarios.sidewaysMarket }
            ];

            const adaptiveResults = [];

            for (const scenario of scenarios) {
                const result = await adaptiveOptimizer.adaptToMarket(
                    scenario.data.slice(0, testDataSize),
                    initialBalance,
                    fitnessEvaluator
                );

                adaptiveResults.push({
                    scenario: scenario.name,
                    parameters: result.adaptedParameters,
                    performance: result.performance
                });

                expect(result.performance.overallScore).toBeGreaterThan(0);
            }

            // Parametre by sa mali líšiť pre rôzne trhové podmienky
            const bullParams = adaptiveResults[0].parameters;
            const bearParams = adaptiveResults[1].parameters;
            
            // Aspoň niektoré parametre by mali byť odlišné
            const differences = Object.keys(bullParams).filter(key => 
                (bullParams as any)[key] !== (bearParams as any)[key]
            ).length;
            
            expect(differences).toBeGreaterThan(0);

            console.log(`🔄 Adaptive Optimization Results:`);
            adaptiveResults.forEach(result => {
                console.log(`  ${result.scenario}:`);
                console.log(`    Confidence: ${result.parameters.minConfidence}%`);
                console.log(`    Performance: ${result.performance.overallScore.toFixed(2)}`);
            });
        });
    });
});
