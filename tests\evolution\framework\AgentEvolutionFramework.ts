// tests/evolution/framework/AgentEvolutionFramework.ts
// Hlavný framework pre evolúciu trading agentov

import { StrategyGenome } from './StrategyGenome';
import { FitnessEvaluator, FitnessResult } from './FitnessEvaluator';
import { GeneticOperators } from './GeneticOperators';
import { EnhancedMarketData } from '../../../src/data/enhancedMockData';

export interface EvolutionConfig {
    populationSize: number;
    generations: number;
    mutationRate: number;
    crossoverRate: number;
    elitismRate: number;
    tournamentSize: number;
    objectives?: string[];
}

export interface GenerationStats {
    generation: number;
    bestFitness: number;
    averageFitness: number;
    worstFitness: number;
    diversity: number;
    convergence: number;
}

export interface EvolutionResult {
    bestIndividual: StrategyGenome;
    bestFitness: FitnessResult;
    generationStats: GenerationStats[];
    convergenceData: number[];
    totalEvaluations: number;
    executionTime: number;
}

export interface EvaluatedIndividual {
    individual: StrategyGenome;
    fitness: FitnessResult;
}

export class AgentEvolutionFramework {
    private config: EvolutionConfig;
    private fitnessEvaluator: FitnessEvaluator;
    private geneticOperators: GeneticOperators;
    private evaluationCount: number = 0;

    constructor(config: EvolutionConfig) {
        this.config = config;
        this.fitnessEvaluator = new FitnessEvaluator();
        this.geneticOperators = new GeneticOperators();
    }

    /**
     * Vytvorí počiatočnú populáciu
     */
    async createInitialPopulation(): Promise<StrategyGenome[]> {
        const population: StrategyGenome[] = [];
        
        for (let i = 0; i < this.config.populationSize; i++) {
            // Vytvor náhodný genóm s rôznymi parametrami
            const genome = new StrategyGenome({
                minConfidence: this.randomBetween(60, 95),
                minSignalStrength: this.randomBetween(50, 90),
                maxRiskLevel: this.randomChoice(['low', 'medium', 'high']),
                trendConfirmationPeriod: this.randomBetween(3, 10),
                volatilityThreshold: this.randomBetween(0.01, 0.05),
                volumeConfirmation: Math.random() > 0.5,
                multiTimeframeConfirmation: Math.random() > 0.3,
                supportResistanceRespect: Math.random() > 0.2,
                fibonacciLevels: Math.random() > 0.4,
                marketConditionFilter: Math.random() > 0.3,
                maxDailyTrades: this.randomBetween(3, 15),
                maxConsecutiveLosses: this.randomBetween(2, 5),
                profitTarget: this.randomBetween(1.5, 3.0),
                stopLoss: this.randomBetween(0.5, 1.5)
            });
            
            population.push(genome);
        }
        
        return population;
    }

    /**
     * Hlavný evolučný cyklus
     */
    async evolve(
        marketData: EnhancedMarketData[], 
        initialBalance: number
    ): Promise<EvolutionResult> {
        const startTime = Date.now();
        this.evaluationCount = 0;
        
        // Vytvor počiatočnú populáciu
        let population = await this.createInitialPopulation();
        
        const generationStats: GenerationStats[] = [];
        const convergenceData: number[] = [];
        let bestOverall: EvaluatedIndividual | null = null;

        for (let generation = 0; generation < this.config.generations; generation++) {
            console.log(`🧬 Generácia ${generation + 1}/${this.config.generations}`);
            
            // Vyhodnoť fitness pre celú populáciu
            const evaluatedPopulation = await this.evaluatePopulation(
                population, 
                marketData, 
                initialBalance
            );
            
            // Nájdi najlepšieho jedinca
            const generationBest = evaluatedPopulation.reduce((best, current) => 
                current.fitness.overallScore > best.fitness.overallScore ? current : best
            );
            
            // Aktualizuj celkovo najlepšieho
            if (!bestOverall || generationBest.fitness.overallScore > bestOverall.fitness.overallScore) {
                bestOverall = generationBest;
            }
            
            // Vypočítaj štatistiky generácie
            const stats = this.calculateGenerationStats(generation, evaluatedPopulation);
            generationStats.push(stats);
            convergenceData.push(stats.bestFitness);
            
            console.log(`  📊 Najlepší: ${stats.bestFitness.toFixed(4)}, Priemer: ${stats.averageFitness.toFixed(4)}`);
            
            // Ak nie je posledná generácia, vytvor novú populáciu
            if (generation < this.config.generations - 1) {
                population = await this.createNextGeneration(evaluatedPopulation);
            }
        }

        const executionTime = Date.now() - startTime;
        
        return {
            bestIndividual: bestOverall!.individual,
            bestFitness: bestOverall!.fitness,
            generationStats,
            convergenceData,
            totalEvaluations: this.evaluationCount,
            executionTime
        };
    }

    /**
     * Vyhodnotí fitness pre celú populáciu
     */
    private async evaluatePopulation(
        population: StrategyGenome[],
        marketData: EnhancedMarketData[],
        initialBalance: number
    ): Promise<EvaluatedIndividual[]> {
        const evaluatedPopulation: EvaluatedIndividual[] = [];
        
        for (const individual of population) {
            const fitness = await this.fitnessEvaluator.evaluateFitness(
                individual,
                marketData,
                initialBalance
            );
            
            evaluatedPopulation.push({ individual, fitness });
            this.evaluationCount++;
        }
        
        return evaluatedPopulation;
    }

    /**
     * Vytvorí novú generáciu
     */
    private async createNextGeneration(
        evaluatedPopulation: EvaluatedIndividual[]
    ): Promise<StrategyGenome[]> {
        const newPopulation: StrategyGenome[] = [];
        
        // Elitizmus - zachovaj najlepších jedincov
        const eliteCount = Math.floor(this.config.populationSize * this.config.elitismRate);
        const elite = evaluatedPopulation
            .sort((a, b) => b.fitness.overallScore - a.fitness.overallScore)
            .slice(0, eliteCount)
            .map(e => e.individual.clone());
        
        newPopulation.push(...elite);
        
        // Vytvor zvyšok populácie krížením a mutáciou
        while (newPopulation.length < this.config.populationSize) {
            // Selekcia rodičov
            const parents = this.geneticOperators.tournamentSelection(
                evaluatedPopulation,
                2,
                this.config.tournamentSize
            );
            
            if (Math.random() < this.config.crossoverRate) {
                // Kríženie
                const offspring = this.geneticOperators.crossover(
                    parents[0].individual,
                    parents[1].individual
                );
                
                // Mutácia potomkov
                for (const child of offspring) {
                    if (Math.random() < this.config.mutationRate) {
                        const mutated = this.geneticOperators.mutate(child, this.config.mutationRate);
                        newPopulation.push(mutated);
                    } else {
                        newPopulation.push(child);
                    }
                    
                    if (newPopulation.length >= this.config.populationSize) break;
                }
            } else {
                // Len mutácia
                const mutated = this.geneticOperators.mutate(
                    parents[0].individual,
                    this.config.mutationRate
                );
                newPopulation.push(mutated);
            }
        }
        
        // Orezaj na správnu veľkosť
        return newPopulation.slice(0, this.config.populationSize);
    }

    /**
     * Vypočíta štatistiky generácie
     */
    private calculateGenerationStats(
        generation: number,
        evaluatedPopulation: EvaluatedIndividual[]
    ): GenerationStats {
        const fitnessValues = evaluatedPopulation.map(e => e.fitness.overallScore);
        
        const bestFitness = Math.max(...fitnessValues);
        const worstFitness = Math.min(...fitnessValues);
        const averageFitness = fitnessValues.reduce((sum, f) => sum + f, 0) / fitnessValues.length;
        
        // Vypočítaj diverzitu (štandardná odchýlka fitness)
        const variance = fitnessValues.reduce((sum, f) => sum + Math.pow(f - averageFitness, 2), 0) / fitnessValues.length;
        const diversity = Math.sqrt(variance);
        
        // Vypočítaj konvergenciu (rozdiel medzi najlepším a priemerným)
        const convergence = bestFitness - averageFitness;
        
        return {
            generation,
            bestFitness,
            averageFitness,
            worstFitness,
            diversity,
            convergence
        };
    }

    // Utility metódy
    private randomBetween(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }

    private randomChoice<T>(choices: T[]): T {
        return choices[Math.floor(Math.random() * choices.length)];
    }

    // Getters
    getPopulationSize(): number { return this.config.populationSize; }
    getGenerations(): number { return this.config.generations; }
    getMutationRate(): number { return this.config.mutationRate; }
    getCrossoverRate(): number { return this.config.crossoverRate; }
}
