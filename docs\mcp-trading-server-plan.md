# MCP Trading Data Server - Implementačný plán

## 🎯 Cieľ projektu

Vytvoriť MCP server `trading-data-server` ktorý poskytne real-time a historické trading dáta pre SuperAgent bez potreby platených API kľúčov.

## 📋 Prehľad

SuperAgent momentálne používa mock dáta a nemá prístup k real-time trhové informácie. Tento MCP server mu umožní:
- Získavať aktuálne forex, akciové a krypto dáta
- Stiahnuť historické dáta pre backtesting
- Vypočítavať technické indikátory z live dát
- Optimalizovať stratégie na reálnych trhových podmienkach

## 🏗️ Architektúra

```mermaid
graph TB
    A[SuperAgent] --> B[MCP Client]
    B --> C[Trading Data MCP Server]
    C --> D[Yahoo Finance API]
    C --> E[Alpha Vantage Free]
    C --> F[CoinGecko API]
    C --> G[Local Cache]
    
    H[Tools] --> I[get_stock_data]
    H --> J[get_forex_data]
    H --> K[get_crypto_data]
    H --> L[get_market_indicators]
    
    M[Resources] --> N[market://EURUSD/current]
    M --> O[market://AAPL/historical]
    M --> P[indicators://RSI/EURUSD]
```

## 📦 Komponenty MCP Servera

### 1. Nástroje (Tools)

#### `get_forex_data`
- **Popis**: Získanie forex dát pre menové páry
- **Parametre**:
  - `pair` (string): Menový pár (napr. EURUSD, GBPUSD)
  - `interval` (enum): Časový interval (1m, 5m, 15m, 1h, 1d)
  - `period` (string): Časové obdobie (1mo, 3mo, 1y)
- **Zdroj**: Yahoo Finance scraping

#### `get_stock_data`
- **Popis**: Získanie akciových dát
- **Parametre**:
  - `symbol` (string): Symbol akcie (napr. AAPL, MSFT)
  - `interval` (enum): Časový interval
  - `period` (string): Časové obdobie
- **Zdroj**: Yahoo Finance, Alpha Vantage

#### `get_crypto_data`
- **Popis**: Získanie kryptomenových dát
- **Parametre**:
  - `symbol` (string): Krypto symbol (napr. BTC, ETH)
  - `vs_currency` (string): Referenčná mena (USD, EUR)
  - `days` (number): Počet dní histórie
- **Zdroj**: CoinGecko API

#### `get_market_indicators`
- **Popis**: Výpočet technických indikátorov
- **Parametre**:
  - `symbol` (string): Symbol inštrumentu
  - `indicators` (array): Zoznam indikátorov (RSI, MACD, SMA, EMA)
  - `period` (number): Obdobie pre výpočet
- **Výpočet**: Lokálne pomocou technicalindicators knižnice

#### `get_economic_calendar`
- **Popis**: Ekonomické udalosti a správy
- **Parametre**:
  - `date_from` (string): Dátum od
  - `date_to` (string): Dátum do
  - `importance` (enum): Dôležitosť (low, medium, high)
- **Zdroj**: Free economic calendar APIs

#### `search_symbols`
- **Popis**: Vyhľadávanie symbolov inštrumentov
- **Parametre**:
  - `query` (string): Vyhľadávací dotaz
  - `type` (enum): Typ inštrumentu (stock, forex, crypto)
- **Zdroj**: Kombinované vyhľadávanie

### 2. Zdroje (Resources)

#### `market://{symbol}/current`
- **Popis**: Aktuálne trhové dáta pre symbol
- **Príklad**: `market://EURUSD/current`
- **Formát**: JSON s cenou, objemom, zmenou

#### `market://{symbol}/historical`
- **Popis**: Historické dáta pre symbol
- **Príklad**: `market://AAPL/historical`
- **Formát**: JSON array s OHLCV dátami

#### `indicators://{indicator}/{symbol}`
- **Popis**: Technické indikátory pre symbol
- **Príklad**: `indicators://RSI/EURUSD`
- **Formát**: JSON s hodnotami indikátora

#### `news://market/latest`
- **Popis**: Najnovšie trhové správy
- **Formát**: JSON array s článkami

## 🔧 Dátové zdroje (Free APIs)

### 1. Yahoo Finance
- **Typ**: Web scraping
- **Limity**: Žiadne oficiálne (rate limiting potrebný)
- **Dáta**: Akcie, forex, komodity, indexy
- **Implementácia**: Custom scraper s axios

### 2. Alpha Vantage (Free Tier)
- **Typ**: REST API
- **Limity**: 5 calls/minute, 500 calls/day
- **Dáta**: Akcie, forex, krypto
- **API kľúč**: Voliteľný (free tier dostupný)

### 3. CoinGecko
- **Typ**: REST API
- **Limity**: 10-50 calls/minute (bez API kľúča)
- **Dáta**: Kryptomeny, DeFi
- **Implementácia**: Priame API volania

### 4. FRED Economic Data
- **Typ**: REST API
- **Limity**: Žiadne (registrácia potrebná)
- **Dáta**: Ekonomické indikátory, úrokové sadzby
- **API kľúč**: Free

### 5. Financial Modeling Prep (Free Tier)
- **Typ**: REST API
- **Limity**: 250 calls/day
- **Dáta**: Akcie, finančné výkazy
- **API kľúč**: Free tier

## 📁 Štruktúra projektu

```
D:\MCP\trading-data-server/
├── package.json
├── tsconfig.json
├── README.md
├── src/
│   ├── index.ts              # Hlavný MCP server
│   ├── adapters/
│   │   ├── base-adapter.ts   # Základná trieda pre adaptéry
│   │   ├── yahoo-finance.ts  # Yahoo Finance scraper
│   │   ├── alpha-vantage.ts  # Alpha Vantage API
│   │   ├── coingecko.ts      # CoinGecko API
│   │   ├── fred.ts           # FRED Economic Data
│   │   └── fmp.ts            # Financial Modeling Prep
│   ├── cache/
│   │   ├── cache-manager.ts  # Cache systém
│   │   ├── cache-types.ts    # Cache typy a interfaces
│   │   └── memory-cache.ts   # In-memory cache implementácia
│   ├── tools/
│   │   ├── stock-tools.ts    # Akciové nástroje
│   │   ├── forex-tools.ts    # Forex nástroje
│   │   ├── crypto-tools.ts   # Krypto nástroje
│   │   ├── indicator-tools.ts # Technické indikátory
│   │   └── news-tools.ts     # Správy a kalendár
│   ├── resources/
│   │   ├── market-resources.ts # Trhové zdroje
│   │   ├── indicator-resources.ts # Indikátory ako zdroje
│   │   └── news-resources.ts   # Správy ako zdroje
│   ├── utils/
│   │   ├── rate-limiter.ts   # Rate limiting pre APIs
│   │   ├── error-handler.ts  # Centralizované error handling
│   │   ├── validators.ts     # Input validácie
│   │   ├── formatters.ts     # Dáta formatovanie
│   │   └── constants.ts      # Konštanty a konfigurácia
│   └── types/
│       ├── market-data.ts    # Typy pre trhové dáta
│       ├── indicators.ts     # Typy pre indikátory
│       └── api-responses.ts  # API response typy
├── tests/
│   ├── adapters/
│   ├── tools/
│   ├── resources/
│   └── utils/
└── build/                    # Kompilované JS súbory
```

## 🔧 Implementačný plán

### Fáza 1: Základná štruktúra (30 min)
1. **Vytvorenie projektu**
   ```bash
   cd C:\Users\<USER>\AppData\Roaming\Kilo-Code\MCP
   npx @modelcontextprotocol/create-server trading-data-server
   cd trading-data-server
   ```

2. **Inštalácia závislostí**
   ```bash
   npm install axios zod @modelcontextprotocol/sdk technicalindicators
   npm install --save-dev @types/node jest ts-jest
   ```

3. **Konfigurácia TypeScript a build scriptu**

### Fáza 2: Dátové adaptéry (2 hodiny)

#### 2.1 Základný adaptér
```typescript
// src/adapters/base-adapter.ts
export abstract class BaseAdapter {
  protected rateLimiter: RateLimiter;
  protected cache: CacheManager;
  
  abstract getName(): string;
  abstract isAvailable(): Promise<boolean>;
  protected abstract makeRequest(url: string, params?: any): Promise<any>;
}
```

#### 2.2 Yahoo Finance adaptér
```typescript
// src/adapters/yahoo-finance.ts
export class YahooFinanceAdapter extends BaseAdapter {
  async getStockData(symbol: string, period: string): Promise<StockData>
  async getForexData(pair: string, interval: string): Promise<ForexData>
  async getHistoricalData(symbol: string, range: string): Promise<HistoricalData[]>
}
```

#### 2.3 Ostatné adaptéry
- Alpha Vantage adaptér
- CoinGecko adaptér
- FRED adaptér
- Financial Modeling Prep adaptér

### Fáza 3: Cache systém (45 min)

```typescript
// src/cache/cache-manager.ts
export class CacheManager {
  private cache = new Map<string, CacheEntry>();
  
  async get<T>(key: string): Promise<T | null>
  async set<T>(key: string, data: T, ttl: number): Promise<void>
  async invalidate(pattern: string): Promise<void>
  private isExpired(entry: CacheEntry): boolean
  private generateKey(prefix: string, params: any): string
}
```

**Cache stratégia:**
- Real-time dáta: 30 sekúnd TTL
- Historické dáta: 1 hodina TTL
- Technické indikátory: 5 minút TTL
- Ekonomické správy: 15 minút TTL

### Fáza 4: MCP Tools implementácia (1.5 hodiny)

#### 4.1 Forex tools
```typescript
// src/tools/forex-tools.ts
export function registerForexTools(server: McpServer) {
  server.tool(
    "get_forex_data",
    {
      pair: z.string().describe("Currency pair (e.g., EURUSD)"),
      interval: z.enum(["1m", "5m", "15m", "1h", "1d"]).optional(),
      period: z.string().optional().describe("Time period (e.g., 1mo, 3mo)")
    },
    async ({ pair, interval = "1h", period = "1mo" }) => {
      // Implementácia
    }
  );
}
```

#### 4.2 Stock tools
#### 4.3 Crypto tools
#### 4.4 Indicator tools

### Fáza 5: MCP Resources implementácia (45 min)

```typescript
// src/resources/market-resources.ts
export function registerMarketResources(server: McpServer) {
  // Current market data resource
  server.resource(
    "current_market_data",
    new ResourceTemplate("market://{symbol}/current"),
    async (uri, { symbol }) => {
      // Implementácia
    }
  );
}
```

### Fáza 6: Rate limiting a error handling (30 min)

```typescript
// src/utils/rate-limiter.ts
export class RateLimiter {
  private requests = new Map<string, number[]>();
  
  async checkLimit(key: string, maxRequests: number, windowMs: number): Promise<boolean>
  async waitForSlot(key: string, maxRequests: number, windowMs: number): Promise<void>
}
```

### Fáza 7: Integrácia so SuperAgent (1 hodina)

#### 7.1 Modifikácia DataFetcher
```typescript
// src/data/fetcher.ts - rozšírenie
import { use_mcp_tool } from '../utils/mcp-client';

class DataFetcher {
  async fetchMarketDataMCP(symbol: string, period: string): Promise<any> {
    return await use_mcp_tool('trading-data-server', 'get_forex_data', {
      pair: symbol,
      period: period
    });
  }
}
```

#### 7.2 Rozšírenie SuperAgent
```typescript
// src/agent/superAgent.ts - pridanie MCP integrácie
class SuperAgent {
  async runFullCycleWithRealData() {
    log('--- SuperAgent: Spúšťam evolučný cyklus s real-time dátami ---');
    
    // Získanie real-time dát cez MCP
    const realTimeData = await this.fetchRealTimeData();
    
    // Optimalizácia na real-time dátach
    const optimization = optimizeStrategy(realTimeData);
    
    // Pokračovanie evolúcie...
  }
  
  private async fetchRealTimeData() {
    // Implementácia MCP volania
  }
}
```

### Fáza 8: Konfigurácia MCP servera (15 min)

```json
// C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\kilocode.kilo-code\settings\mcp_settings.json
{
  "mcpServers": {
    "trading-data-server": {
      "command": "node",
      "args": ["D:\\MCP\\trading-data-server\\build\\index.js"],
      "env": {
        "ALPHA_VANTAGE_API_KEY": "optional-free-key",
        "FRED_API_KEY": "optional-free-key"
      },
      "disabled": false,
      "alwaysAllow": []
    }
  }
}
```

### Fáza 9: Testovanie a optimalizácia (1 hodina)

#### 9.1 Unit testy
- Testovanie adaptérov
- Testovanie cache systému
- Testovanie MCP tools

#### 9.2 Integračné testy
- Testovanie s reálnymi API
- Testovanie rate limiting
- Testovanie error handling

#### 9.3 Performance optimalizácia
- Cache hit rate monitoring
- API response time monitoring
- Memory usage optimization

## 🚀 Očakávané výsledky

Po úspešnej implementácii bude SuperAgent schopný:

### 1. Real-time dáta
- Získavať aktuálne ceny pre EURUSD, GBPUSD, USDJPY
- Sledovať real-time zmeny volatility
- Reagovať na trhové pohyby

### 2. Historické dáta
- Stiahnuť historické dáta pre backtesting
- Analyzovať dlhodobé trendy
- Validovať stratégie na reálnych dátach

### 3. Technické indikátory
- Vypočítavať RSI, MACD, Bollinger Bands z live dát
- Kombinovať viacero indikátorov
- Optimalizovať parametre indikátorov

### 4. Ekonomické udalosti
- Sledovať ekonomický kalendár
- Reagovať na dôležité správy
- Prispôsobiť stratégie podľa fundamentov

### 5. Optimalizácia stratégií
- Evolúcia na reálnych trhových podmienkach
- Adaptácia na meniace sa trhové prostredie
- Dosiahnutie vyššieho win rate

## 📊 Metriky úspechu

### 1. Technické metriky
- **API dostupnosť**: >99% uptime
- **Cache hit rate**: >80%
- **Response time**: <2 sekundy
- **Error rate**: <1%

### 2. Trading metriky
- **Win rate**: Zlepšenie o 10-15%
- **Sharpe ratio**: Zlepšenie o 20%
- **Maximum drawdown**: Zníženie o 15%
- **Profit factor**: Zlepšenie o 25%

## 🔒 Bezpečnosť a limity

### 1. Rate limiting
- Yahoo Finance: Max 60 requests/minute
- Alpha Vantage: Max 5 requests/minute
- CoinGecko: Max 50 requests/minute

### 2. Error handling
- Graceful degradation pri API výpadkoch
- Fallback na cache dáta
- Retry mechanizmy s exponential backoff

### 3. Dáta validácia
- Validácia všetkých API responses
- Sanitizácia user inputs
- Type checking s TypeScript

## 🔄 Budúce rozšírenia

### 1. Ďalšie dátové zdroje
- Quandl API
- IEX Cloud
- Polygon.io (free tier)

### 2. Pokročilé funkcie
- WebSocket real-time streams
- Machine learning pre predikcie
- Sentiment analysis z news

### 3. UI Dashboard
- Web interface pre monitoring
- Grafické zobrazenie dát
- Real-time charts

## 📝 Poznámky k implementácii

### 1. Dôležité upozornenia
- Všetky free APIs majú rate limity
- Cache je kritický pre performance
- Error handling musí byť robustný
- Testovanie na reálnych dátach je kľúčové

### 2. Alternatívne riešenia
- Ak Yahoo Finance blokuje scraping, použiť len API zdroje
- Pre crypto dáta možno použiť aj CoinCap API
- FRED dáta sú užitočné pre makroekonomické analýzy

### 3. Optimalizácie
- Implementovať connection pooling
- Použiť compression pre API responses
- Implementovať circuit breaker pattern

---

**Autor**: Kilo Code Architect Mode  
**Dátum**: 22.6.2025  
**Verzia**: 1.0  
**Status**: Pripravené na implementáciu