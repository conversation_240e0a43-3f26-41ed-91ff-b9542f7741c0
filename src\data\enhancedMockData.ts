// src/data/enhancedMockData.ts

export interface EnhancedMarketData {
    price: number;
    high: number;
    low: number;
    open: number;
    volume: number;
    timestamp: Date;
    movingAverage: number;
    rsi: number;
    trend: 'up' | 'down' | 'sideways';
    volatility: number;
    ema?: number;
    macd?: number;
    macdSignal?: number;
    macdHistogram?: number;
}

// Generátor realistic<PERSON>ch trhových dát s rôznymi scenármi
export class MarketDataGenerator {
    
    static generateTrendingUpData(length: number = 50): EnhancedMarketData[] {
        const data: EnhancedMarketData[] = [];
        let basePrice = 1.2000;
        let volume = 1000;
        
        for (let i = 0; i < length; i++) {
            // Rastúci trend s občasnými korekciami
            const trendStrength = 0.0002 + (Math.random() * 0.0001);
            const noise = (Math.random() - 0.5) * 0.0005;
            const correction = i % 7 === 0 ? -0.0003 : 0; // Korekcia každý 7. deň
            
            basePrice += trendStrength + noise + correction;
            
            const high = basePrice + (Math.random() * 0.0008);
            const low = basePrice - (Math.random() * 0.0006);
            const open = data.length > 0 ? data[data.length - 1].price : basePrice;
            
            volume = 800 + Math.random() * 400;
            
            // Vypočítaj technické indikátory
            const prices = data.map(d => d.price).concat([basePrice]);
            const ma = this.calculateMA(prices.slice(-20));
            const rsi = this.calculateRSI(prices.slice(-14));
            const volatility = this.calculateVolatility(prices.slice(-10));
            
            data.push({
                price: basePrice,
                high,
                low,
                open,
                volume,
                timestamp: new Date(Date.now() + i * 60000), // 1 minúta intervaly
                movingAverage: ma,
                rsi,
                trend: 'up',
                volatility
            });
        }
        
        return data;
    }
    
    static generateTrendingDownData(length: number = 50): EnhancedMarketData[] {
        const data: EnhancedMarketData[] = [];
        let basePrice = 1.2000;
        let volume = 1000;
        
        for (let i = 0; i < length; i++) {
            // Klesajúci trend s občasnými odrazmi
            const trendStrength = -0.0002 - (Math.random() * 0.0001);
            const noise = (Math.random() - 0.5) * 0.0005;
            const bounce = i % 8 === 0 ? 0.0004 : 0; // Odraz každý 8. deň
            
            basePrice += trendStrength + noise + bounce;
            
            const high = basePrice + (Math.random() * 0.0006);
            const low = basePrice - (Math.random() * 0.0008);
            const open = data.length > 0 ? data[data.length - 1].price : basePrice;
            
            volume = 900 + Math.random() * 500; // Vyšší volume pri poklese
            
            const prices = data.map(d => d.price).concat([basePrice]);
            const ma = this.calculateMA(prices.slice(-20));
            const rsi = this.calculateRSI(prices.slice(-14));
            const volatility = this.calculateVolatility(prices.slice(-10));
            
            data.push({
                price: basePrice,
                high,
                low,
                open,
                volume,
                timestamp: new Date(Date.now() + i * 60000),
                movingAverage: ma,
                rsi,
                trend: 'down',
                volatility
            });
        }
        
        return data;
    }
    
    static generateSidewaysData(length: number = 50): EnhancedMarketData[] {
        const data: EnhancedMarketData[] = [];
        let basePrice = 1.2000;
        const centerPrice = basePrice;
        let volume = 1000;
        
        for (let i = 0; i < length; i++) {
            // Sideways pohyb s oscilovaním okolo strednej hodnoty
            const oscillation = Math.sin(i * 0.3) * 0.0015;
            const noise = (Math.random() - 0.5) * 0.0008;
            const meanReversion = (centerPrice - basePrice) * 0.1;
            
            basePrice += oscillation + noise + meanReversion;
            
            const high = basePrice + (Math.random() * 0.0005);
            const low = basePrice - (Math.random() * 0.0005);
            const open = data.length > 0 ? data[data.length - 1].price : basePrice;
            
            volume = 700 + Math.random() * 300; // Nižší volume pri sideways
            
            const prices = data.map(d => d.price).concat([basePrice]);
            const ma = this.calculateMA(prices.slice(-20));
            const rsi = this.calculateRSI(prices.slice(-14));
            const volatility = this.calculateVolatility(prices.slice(-10));
            
            data.push({
                price: basePrice,
                high,
                low,
                open,
                volume,
                timestamp: new Date(Date.now() + i * 60000),
                movingAverage: ma,
                rsi,
                trend: 'sideways',
                volatility
            });
        }
        
        return data;
    }
    
    static generateVolatileData(length: number = 50): EnhancedMarketData[] {
        const data: EnhancedMarketData[] = [];
        let basePrice = 1.2000;
        let volume = 1000;
        
        for (let i = 0; i < length; i++) {
            // Vysoká volatilita s náhodnými skokami
            const volatileMove = (Math.random() - 0.5) * 0.002;
            const spike = Math.random() < 0.1 ? (Math.random() - 0.5) * 0.005 : 0; // 10% šanca na spike
            
            basePrice += volatileMove + spike;
            
            const spread = 0.001 + Math.random() * 0.001; // Väčší spread
            const high = basePrice + spread;
            const low = basePrice - spread;
            const open = data.length > 0 ? data[data.length - 1].price : basePrice;
            
            volume = 1200 + Math.random() * 800; // Vysoký volume
            
            const prices = data.map(d => d.price).concat([basePrice]);
            const ma = this.calculateMA(prices.slice(-20));
            const rsi = this.calculateRSI(prices.slice(-14));
            const volatility = this.calculateVolatility(prices.slice(-10));
            
            data.push({
                price: basePrice,
                high,
                low,
                open,
                volume,
                timestamp: new Date(Date.now() + i * 60000),
                movingAverage: ma,
                rsi,
                trend: Math.random() > 0.5 ? 'up' : 'down',
                volatility
            });
        }
        
        return data;
    }
    
    static generateMixedScenarioData(length: number = 200): EnhancedMarketData[] {
        const scenarios = [
            () => this.generateTrendingUpData(40),
            () => this.generateSidewaysData(30),
            () => this.generateTrendingDownData(35),
            () => this.generateVolatileData(25),
            () => this.generateTrendingUpData(45),
            () => this.generateSidewaysData(25)
        ];
        
        let allData: EnhancedMarketData[] = [];
        let currentTime = Date.now();
        
        for (const scenario of scenarios) {
            const scenarioData = scenario();
            
            // Upravuj timestamp aby nadväzoval
            scenarioData.forEach((point, index) => {
                point.timestamp = new Date(currentTime + (allData.length + index) * 60000);
                
                // Upravuj cenu aby nadväzovala na predchádzajúce dáta
                if (allData.length > 0) {
                    const lastPrice = allData[allData.length - 1].price;
                    const priceDiff = point.price - scenarioData[0].price;
                    point.price = lastPrice + priceDiff;
                    point.high = point.price + (point.high - scenarioData[0].price);
                    point.low = point.price + (point.low - scenarioData[0].price);
                    point.open = index === 0 ? lastPrice : scenarioData[index - 1].price;
                }
            });
            
            allData = allData.concat(scenarioData);
            
            if (allData.length >= length) break;
        }
        
        return allData.slice(0, length);
    }
    
    // Helper metódy
    private static calculateMA(prices: number[]): number {
        if (prices.length === 0) return 0;
        return prices.reduce((sum, price) => sum + price, 0) / prices.length;
    }
    
    private static calculateRSI(prices: number[]): number {
        if (prices.length < 2) return 50;
        
        let gains = 0;
        let losses = 0;
        
        for (let i = 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            if (change > 0) gains += change;
            else losses += Math.abs(change);
        }
        
        const avgGain = gains / (prices.length - 1);
        const avgLoss = losses / (prices.length - 1);
        
        if (avgLoss === 0) return 100;
        
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }
    
    private static calculateVolatility(prices: number[]): number {
        if (prices.length < 2) return 0.01;
        
        const returns = [];
        for (let i = 1; i < prices.length; i++) {
            returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
        }
        
        const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
        
        return Math.sqrt(variance);
    }
}

// Predkonfigurované scenáre pre testovanie
export const testScenarios = {
    // Scenár 1: Silný rastúci trend (očakávaný win rate: 85%+)
    strongUptrend: MarketDataGenerator.generateTrendingUpData(60),
    
    // Scenár 2: Silný klesajúci trend (očakávaný win rate: 85%+)
    strongDowntrend: MarketDataGenerator.generateTrendingDownData(60),
    
    // Scenár 3: Sideways trh (očakávaný win rate: 60-70%)
    sidewaysMarket: MarketDataGenerator.generateSidewaysData(60),
    
    // Scenár 4: Volatilný trh (očakávaný win rate: 50-60%)
    volatileMarket: MarketDataGenerator.generateVolatileData(60),
    
    // Scenár 5: Zmiešané podmienky (očakávaný win rate: 75%+)
    mixedConditions: MarketDataGenerator.generateMixedScenarioData(150),
    
    // Scenár 6: Optimálne podmienky pre vysoký win rate
    optimalConditions: (() => {
        const uptrend1 = MarketDataGenerator.generateTrendingUpData(30);
        const sideways = MarketDataGenerator.generateSidewaysData(20);
        const downtrend = MarketDataGenerator.generateTrendingDownData(25);
        const uptrend2 = MarketDataGenerator.generateTrendingUpData(35);
        
        return [...uptrend1, ...sideways, ...downtrend, ...uptrend2];
    })()
};

// Export pre kompatibilitu s existujúcim kódom
export const enhancedHistoricalData = testScenarios.mixedConditions;