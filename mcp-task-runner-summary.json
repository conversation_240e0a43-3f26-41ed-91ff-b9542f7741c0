{"timestamp": "2025-06-22T19:03:57.726Z", "results": [{"success": false, "message": "Build MCP Server zlyhala: Command failed: npm run build", "duration": 4385}, {"success": false, "message": "Test MCP Server Startup zlyhala: MCP server startup timeout (10s)", "duration": 10013}, {"success": false, "message": "Integration Test zlyhala: MCP error -32602: MCP error -32602: Invalid arguments for tool get_market_indicators: [\n  {\n    \"received\": \"RSI\",\n    \"code\": \"invalid_enum_value\",\n    \"options\": [\n      \"sma\",\n      \"ema\",\n      \"rsi\",\n      \"macd\",\n      \"bollinger\",\n      \"all\"\n    ],\n    \"path\": [\n      \"indicators\",\n      0\n    ],\n    \"message\": \"Invalid enum value. Expected 'sma' | 'ema' | 'rsi' | 'macd' | 'bollinger' | 'all', received 'RSI'\"\n  },\n  {\n    \"received\": \"MACD\",\n    \"code\": \"invalid_enum_value\",\n    \"options\": [\n      \"sma\",\n      \"ema\",\n      \"rsi\",\n      \"macd\",\n      \"bollinger\",\n      \"all\"\n    ],\n    \"path\": [\n      \"indicators\",\n      1\n    ],\n    \"message\": \"Invalid enum value. Expected 'sma' | 'ema' | 'rsi' | 'macd' | 'bollinger' | 'all', received 'MACD'\"\n  }\n]", "duration": 1750}, {"success": true, "message": "Add MCP to SuperAgent úspešne dokončená", "duration": 3, "data": {"message": "MCP integrácia úspešne pridaná", "updated": true}}, {"success": false, "message": "Full System Test zlyhala: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m88\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'constructor'.\r\n\r\n\u001b[7m88\u001b[0m     constructor() {\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m89\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m89\u001b[0m         this.mcpClient = new MCPTradingClient();\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m90\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m90\u001b[0m         this.dataFetcher = new DataFetcher();\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'async'.\r\n\r\n\u001b[7m97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'testMCPConnection'.\r\n\r\n\u001b[7m97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m  \u001b[0m \u001b[91m          ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2365: \u001b[0mOperator '>' cannot be applied to types 'boolean' and '{ try: { const: any; if(isConnected: any): void; else: { log(: any): any; }; return: any; }; catch(error: any): boolean; }'.\r\n\r\n\u001b[7m 97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m   \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m 98\u001b[0m         try {\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m109\u001b[0m         }\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~\u001b[0m\r\n\u001b[7m110\u001b[0m     }\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2693: \u001b[0m'boolean' only refers to a type, but is being used as a value here.\r\n\r\n\u001b[7m97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m99\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'isConnected'.\r\n\r\n\u001b[7m99\u001b[0m             const isConnected = await this.dataFetcher.testMCPConnection();\r\n\u001b[7m  \u001b[0m \u001b[91m                  ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m99\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m99\u001b[0m             const isConnected = await this.dataFetcher.testMCPConnection();\r\n\u001b[7m  \u001b[0m \u001b[91m                                      ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m100\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'isConnected' implicitly has an 'any' type.\r\n\r\n\u001b[7m100\u001b[0m             if (isConnected) {\r\n\u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m103\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter '(Missing)' implicitly has an 'any' type.\r\n\r\n\u001b[7m103\u001b[0m                 log('⚠️ MCP nedostupný - používam fallback dáta');\r\n\u001b[7m   \u001b[0m \u001b[91m                    \u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m105\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'isConnected'.\r\n\r\n\u001b[7m105\u001b[0m             return isConnected;\r\n\u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m106\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'error' implicitly has an 'any' type.\r\n\r\n\u001b[7m106\u001b[0m         } catch (error) {\r\n\u001b[7m   \u001b[0m \u001b[91m                 ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m107\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\r\n\r\n\u001b[7m107\u001b[0m             log('❌ MCP test zlyhal:', error);\r\n\u001b[7m   \u001b[0m \u001b[91m                                      ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'async'.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'getRealTimeMarketData'.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m          ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m33\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'symbol'. Did you mean 'Symbol'?\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                ~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/typescript/lib/lib.es2015.symbol.d.ts\u001b[0m:\u001b[93m48\u001b[0m:\u001b[93m13\u001b[0m\r\n    \u001b[7m48\u001b[0m declare var Symbol: SymbolConstructor;\r\n    \u001b[7m  \u001b[0m \u001b[96m            ~~~~~~\u001b[0m\r\n    'Symbol' is declared here.\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m41\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2693: \u001b[0m'string' only refers to a type, but is being used as a value here.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                        ~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m61\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2365: \u001b[0mOperator '>' cannot be applied to types 'boolean' and '{ try: { const: string; return: any; }; catch(error: any): null; }'.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                            ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m116\u001b[0m         try {\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m124\u001b[0m         }\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~\u001b[0m\r\n\u001b[7m125\u001b[0m     }\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m69\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2693: \u001b[0m'any' only refers to a type, but is being used as a value here.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                    ~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m117\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'endDate'.\r\n\r\n\u001b[7m117\u001b[0m             const endDate = new Date().toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                  ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m118\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'startDate'.\r\n\r\n\u001b[7m118\u001b[0m             const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m26\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                         ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m59\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'symbol'.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                          ~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m67\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'startDate'.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m78\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'endDate'.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                             ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m121\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'error' implicitly has an 'any' type.\r\n\r\n\u001b[7m121\u001b[0m         } catch (error) {\r\n\u001b[7m   \u001b[0m \u001b[91m                 ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m122\u001b[0m:\u001b[93m57\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\r\n\r\n\u001b[7m122\u001b[0m             log('❌ Chyba pri získavaní real-time dát:', error);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                        ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'async'.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'runFullCycleWithMCP'.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m          ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2365: \u001b[0mOperator '>' cannot be applied to types 'boolean' and '{ log(: any): any; const: any; let: any; realTimeData: null; if(mcpAvailable: any): void; if(realTimeData: any): any; await: any; }'.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m131\u001b[0m         log('--- SuperAgent: Spúšťam rozšírený evolučný cyklus s MCP ---');\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m148\u001b[0m         // Ak máme real-time dáta, spustíme dodatočnú optimalizáciu\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m149\u001b[0m         if (realTimeData && Object.keys(realTimeData).length > 0) {\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m131\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter '(Missing)' implicitly has an 'any' type.\r\n\r\n\u001b[7m131\u001b[0m         log('--- SuperAgent: Spúšťam rozšírený evolučný cyklus s MCP ---');\r\n\u001b[7m   \u001b[0m \u001b[91m            \u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m134\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'mcpAvailable'.\r\n\r\n\u001b[7m134\u001b[0m         const mcpAvailable = await this.testMCPConnection();\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m134\u001b[0m:\u001b[93m36\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m134\u001b[0m         const mcpAvailable = await this.testMCPConnection();\r\n\u001b[7m   \u001b[0m \u001b[91m                                   ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m137\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18004: \u001b[0mNo value exists in scope for the shorthand property 'let'. Either declare one or provide an initializer.\r\n\r\n\u001b[7m137\u001b[0m         let realTimeData = null;\r\n\u001b[7m   \u001b[0m \u001b[91m        ~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m138\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'mcpAvailable' implicitly has an 'any' type.\r\n\r\n\u001b[7m138\u001b[0m         if (mcpAvailable) {\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m139\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m139\u001b[0m             realTimeData = await this.getRealTimeMarketData();\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m139\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getRealTimeMarketData' does not exist on type '{ log(: any): any; const: any; let: any; realTimeData: null; if(mcpAvailable: any): void; if(realTimeData: any): any; await: any; }'.\r\n\r\n\u001b[7m139\u001b[0m             realTimeData = await this.getRealTimeMarketData();\r\n\u001b[7m   \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m140\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m140\u001b[0m             if (realTimeData) {\r\n\u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m146\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m146\u001b[0m         await this.runFullCycle();\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m149\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'realTimeData' implicitly has an 'any' type.\r\n\r\n\u001b[7m149\u001b[0m         if (realTimeData && Object.keys(realTimeData).length > 0) {\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m149\u001b[0m:\u001b[93m41\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m149\u001b[0m         if (realTimeData && Object.keys(realTimeData).length > 0) {\r\n\u001b[7m   \u001b[0m \u001b[91m                                        ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m153\u001b[0m:\u001b[93m35\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m153\u001b[0m             const convertedData = this.convertRealTimeDataForBacktest(realTimeData);\r\n\u001b[7m   \u001b[0m \u001b[91m                                  ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m153\u001b[0m:\u001b[93m71\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m153\u001b[0m             const convertedData = this.convertRealTimeDataForBacktest(realTimeData);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                      ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m162\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2532: \u001b[0mObject is possibly 'undefined'.\r\n\r\n\u001b[7m162\u001b[0m         await this.mcpClient.disconnect();\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'private'.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'convertRealTimeDataForBacktest'.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m58\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2693: \u001b[0m'any' only refers to a type, but is being used as a value here.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                         ~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m64\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2693: \u001b[0m'any' only refers to a type, but is being used as a value here.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                               ~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'realTimeData'.\r\n\r\n\u001b[7m172\u001b[0m         for (const [date, data] of Object.entries(realTimeData)) {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m85\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1128: \u001b[0mDeclaration or statement expected.\r\n\r\n\u001b[7m85\u001b[0m     private mcpClient: MCPTradingClient;\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m86\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1128: \u001b[0mDeclaration or statement expected.\r\n\r\n\u001b[7m86\u001b[0m     private dataFetcher: DataFetcher;\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m88\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m88\u001b[0m     constructor() {\r\n\u001b[7m  \u001b[0m \u001b[91m                  ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1434: \u001b[0mUnexpected keyword or identifier.\r\n\r\n\u001b[7m97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m97\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m97\u001b[0m     async testMCPConnection(): Promise<boolean> {\r\n\u001b[7m  \u001b[0m \u001b[91m                             ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m98\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m98\u001b[0m         try {\r\n\u001b[7m  \u001b[0m \u001b[91m            ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m99\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m99\u001b[0m             const isConnected = await this.dataFetcher.testMCPConnection();\r\n\u001b[7m  \u001b[0m \u001b[91m                  ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m99\u001b[0m:\u001b[93m75\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m99\u001b[0m             const isConnected = await this.dataFetcher.testMCPConnection();\r\n\u001b[7m  \u001b[0m \u001b[91m                                                                          ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m102\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m102\u001b[0m             } else {\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m102\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m102\u001b[0m             } else {\r\n\u001b[7m   \u001b[0m \u001b[91m                   ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m103\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1003: \u001b[0mIdentifier expected.\r\n\r\n\u001b[7m103\u001b[0m                 log('⚠️ MCP nedostupný - používam fallback dáta');\r\n\u001b[7m   \u001b[0m \u001b[91m                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m105\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m105\u001b[0m             return isConnected;\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m105\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m105\u001b[0m             return isConnected;\r\n\u001b[7m   \u001b[0m \u001b[91m                   ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m105\u001b[0m:\u001b[93m31\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m105\u001b[0m             return isConnected;\r\n\u001b[7m   \u001b[0m \u001b[91m                              ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m106\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m106\u001b[0m         } catch (error) {\r\n\u001b[7m   \u001b[0m \u001b[91m          ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1434: \u001b[0mUnexpected keyword or identifier.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                      ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m115\u001b[0m:\u001b[93m59\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m115\u001b[0m     async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                          ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m116\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m116\u001b[0m         try {\r\n\u001b[7m   \u001b[0m \u001b[91m            ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m117\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m117\u001b[0m             const endDate = new Date().toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                  ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m117\u001b[0m:\u001b[93m67\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m117\u001b[0m             const endDate = new Date().toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                  ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m118\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m118\u001b[0m             const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m118\u001b[0m:\u001b[93m106\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m118\u001b[0m             const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                                                         ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                   ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m120\u001b[0m:\u001b[93m86\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m120\u001b[0m             return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                                     ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m121\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m121\u001b[0m         } catch (error) {\r\n\u001b[7m   \u001b[0m \u001b[91m          ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1434: \u001b[0mUnexpected keyword or identifier.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m                               ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1109: \u001b[0mExpression expected.\r\n\r\n\u001b[7m130\u001b[0m     async runFullCycleWithMCP(): Promise<void> {\r\n\u001b[7m   \u001b[0m \u001b[91m                                             ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m131\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1003: \u001b[0mIdentifier expected.\r\n\r\n\u001b[7m131\u001b[0m         log('--- SuperAgent: Spúšťam rozšírený evolučný cyklus s MCP ---');\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m134\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m134\u001b[0m         const mcpAvailable = await this.testMCPConnection();\r\n\u001b[7m   \u001b[0m \u001b[91m        ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m134\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m134\u001b[0m         const mcpAvailable = await this.testMCPConnection();\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m134\u001b[0m:\u001b[93m60\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m134\u001b[0m         const mcpAvailable = await this.testMCPConnection();\r\n\u001b[7m   \u001b[0m \u001b[91m                                                           ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m137\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m137\u001b[0m         let realTimeData = null;\r\n\u001b[7m   \u001b[0m \u001b[91m            ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m137\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m137\u001b[0m         let realTimeData = null;\r\n\u001b[7m   \u001b[0m \u001b[91m                               ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m146\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m146\u001b[0m         await this.runFullCycle();\r\n\u001b[7m   \u001b[0m \u001b[91m        ~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m146\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m':' expected.\r\n\r\n\u001b[7m146\u001b[0m         await this.runFullCycle();\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m146\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m146\u001b[0m         await this.runFullCycle();\r\n\u001b[7m   \u001b[0m \u001b[91m                                 ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m149\u001b[0m:\u001b[93m26\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m149\u001b[0m         if (realTimeData && Object.keys(realTimeData).length > 0) {\r\n\u001b[7m   \u001b[0m \u001b[91m                         ~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m149\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m149\u001b[0m         if (realTimeData && Object.keys(realTimeData).length > 0) {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1434: \u001b[0mUnexpected keyword or identifier.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m    ~~~~~~~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m56\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m',' expected.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                       ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m62\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                             ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m68\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1011: \u001b[0mAn element access expression should take an argument.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                   \u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m70\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1005: \u001b[0m';' expected.\r\n\r\n\u001b[7m169\u001b[0m     private convertRealTimeDataForBacktest(realTimeData: any): any[] {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                                     ~\u001b[0m\r\n\u001b[96msrc/agent/superAgent.ts\u001b[0m:\u001b[93m188\u001b[0m:\u001b[93m1\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS1128: \u001b[0mDeclaration or statement expected.\r\n\r\n\u001b[7m188\u001b[0m }\r\n\u001b[7m   \u001b[0m \u001b[91m~\u001b[0m\r\n", "duration": 723}], "summary": {"total": 5, "successful": 1, "failed": 4, "totalDuration": 16874}}