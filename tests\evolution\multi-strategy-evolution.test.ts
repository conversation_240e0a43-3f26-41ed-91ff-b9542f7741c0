// tests/evolution/multi-strategy-evolution.test.ts
// Testy pre evolúciu viacerých stratégií súčasne

import { MultiStrategyEvolution } from './framework/MultiStrategyEvolution';
import { StrategyGenome } from './framework/StrategyGenome';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { AgentEvolutionFramework } from './framework/AgentEvolutionFramework';
import { SmartStrategy } from '../../src/agent/smartStrategy';
import { OptimizedSmartStrategy } from '../../src/agent/optimizedSmartStrategy';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Multi-Strategy Evolution', () => {
    let multiStrategyEvolution: MultiStrategyEvolution;
    let fitnessEvaluator: FitnessEvaluator;
    const initialBalance = 10000;
    const testDataSize = 150;

    beforeEach(() => {
        multiStrategyEvolution = new MultiStrategyEvolution({
            populationSize: 15,
            generations: 8,
            mutationRate: 0.15,
            crossoverRate: 0.8,
            elitismRate: 0.2,
            strategyTypes: ['conservative', 'balanced', 'aggressive'],
            ensembleSize: 3
        });
        
        fitnessEvaluator = new FitnessEvaluator();
    });

    describe('Strategy Type Evolution', () => {
        it('should evolve different strategy types simultaneously', async () => {
            const strategyTypes = ['conservative', 'balanced', 'aggressive'];
            const results = [];

            for (const strategyType of strategyTypes) {
                const evolutionFramework = new AgentEvolutionFramework({
                    populationSize: 10,
                    generations: 5,
                    mutationRate: 0.1,
                    crossoverRate: 0.8,
                    elitismRate: 0.2,
                    tournamentSize: 3
                });

                // Vytvor špecializovanú populáciu pre daný typ stratégie
                const population = await this.createSpecializedPopulation(strategyType, 10);
                
                // Simuluj evolúciu
                const bestGenome = population[0]; // Zjednodušené
                const fitness = await fitnessEvaluator.evaluateFitness(
                    bestGenome,
                    testScenarios.optimalConditions.slice(0, testDataSize),
                    initialBalance
                );

                results.push({
                    strategyType,
                    bestGenome,
                    fitness,
                    parameters: bestGenome.getParameters()
                });

                expect(fitness.overallScore).toBeGreaterThan(0);
                console.log(`📊 ${strategyType} Strategy: Score ${fitness.overallScore.toFixed(2)}, Win Rate ${(fitness.winRate * 100).toFixed(2)}%`);
            }

            // Každý typ stratégie by mal mať odlišné charakteristiky
            const conservativeResult = results.find(r => r.strategyType === 'conservative');
            const aggressiveResult = results.find(r => r.strategyType === 'aggressive');

            expect(conservativeResult!.parameters.maxRiskLevel).toBe('low');
            expect(aggressiveResult!.parameters.maxRiskLevel).toBe('high');
            expect(conservativeResult!.parameters.minConfidence).toBeGreaterThan(aggressiveResult!.parameters.minConfidence);
        });

        it('should adapt strategies to different market conditions', async () => {
            const marketConditions = [
                { name: 'Bull Market', data: testScenarios.trendingMarket },
                { name: 'Bear Market', data: testScenarios.volatileMarket },
                { name: 'Sideways Market', data: testScenarios.sidewaysMarket }
            ];

            const adaptationResults = [];

            for (const condition of marketConditions) {
                const result = await multiStrategyEvolution.adaptToMarketCondition(
                    condition.data.slice(0, testDataSize),
                    initialBalance,
                    fitnessEvaluator
                );

                adaptationResults.push({
                    condition: condition.name,
                    bestStrategy: result.bestStrategy,
                    performance: result.performance,
                    adaptedParameters: result.adaptedParameters
                });

                expect(result.performance.overallScore).toBeGreaterThan(0);
                console.log(`🎯 ${condition.name}: Best strategy type ${result.bestStrategy}, Score ${result.performance.overallScore.toFixed(2)}`);
            }

            // Rôzne trhové podmienky by mali preferovať rôzne stratégie
            const strategyTypes = adaptationResults.map(r => r.bestStrategy);
            const uniqueStrategies = new Set(strategyTypes);
            
            // Očakávame aspoň 2 rôzne typy stratégií pre rôzne podmienky
            expect(uniqueStrategies.size).toBeGreaterThanOrEqual(1);
        });
    });

    describe('Ensemble Evolution', () => {
        it('should evolve ensemble of complementary strategies', async () => {
            const ensembleResult = await multiStrategyEvolution.evolveEnsemble(
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            expect(ensembleResult.ensemble).toHaveLength(3); // ensembleSize = 3
            expect(ensembleResult.ensemblePerformance.overallScore).toBeGreaterThan(0);
            expect(ensembleResult.individualPerformances).toHaveLength(3);

            // Ensemble by mal byť lepší ako priemerný jednotlivec
            const avgIndividualScore = ensembleResult.individualPerformances
                .reduce((sum, perf) => sum + perf.overallScore, 0) / ensembleResult.individualPerformances.length;
            
            expect(ensembleResult.ensemblePerformance.overallScore).toBeGreaterThanOrEqual(avgIndividualScore * 0.9);

            console.log(`🎭 Ensemble Results:`);
            console.log(`  Ensemble Score: ${ensembleResult.ensemblePerformance.overallScore.toFixed(2)}`);
            console.log(`  Average Individual Score: ${avgIndividualScore.toFixed(2)}`);
            console.log(`  Improvement: ${((ensembleResult.ensemblePerformance.overallScore / avgIndividualScore - 1) * 100).toFixed(2)}%`);
        });

        it('should create diverse ensemble members', async () => {
            const ensembleResult = await multiStrategyEvolution.evolveEnsemble(
                testScenarios.volatileMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator
            );

            // Skontroluj diverzitu ensemble členov
            const ensemble = ensembleResult.ensemble;
            let totalDistance = 0;
            let comparisons = 0;

            for (let i = 0; i < ensemble.length; i++) {
                for (let j = i + 1; j < ensemble.length; j++) {
                    totalDistance += ensemble[i].distance(ensemble[j]);
                    comparisons++;
                }
            }

            const averageDistance = totalDistance / comparisons;
            expect(averageDistance).toBeGreaterThan(0.1); // Minimálna diverzita

            console.log(`🔄 Ensemble Diversity: ${averageDistance.toFixed(4)}`);
        });

        it('should handle ensemble voting mechanisms', async () => {
            const ensemble = [
                new StrategyGenome({ minConfidence: 80, maxRiskLevel: 'low' }),
                new StrategyGenome({ minConfidence: 70, maxRiskLevel: 'medium' }),
                new StrategyGenome({ minConfidence: 85, maxRiskLevel: 'low' })
            ];

            const votingMechanisms = ['majority', 'weighted', 'unanimous'];
            const results = [];

            for (const mechanism of votingMechanisms) {
                const result = await multiStrategyEvolution.testVotingMechanism(
                    ensemble,
                    mechanism,
                    testScenarios.optimalConditions.slice(0, testDataSize),
                    initialBalance,
                    fitnessEvaluator
                );

                results.push({
                    mechanism,
                    performance: result.performance,
                    tradeCount: result.tradeCount,
                    agreementRate: result.agreementRate
                });

                expect(result.performance.overallScore).toBeGreaterThanOrEqual(0);
                console.log(`🗳️ ${mechanism} voting: Score ${result.performance.overallScore.toFixed(2)}, Agreement ${(result.agreementRate * 100).toFixed(1)}%`);
            }

            // Unanimous voting by mal mať najvyššiu agreement rate ale možno menej obchodov
            const unanimousResult = results.find(r => r.mechanism === 'unanimous');
            const majorityResult = results.find(r => r.mechanism === 'majority');

            expect(unanimousResult!.agreementRate).toBeGreaterThanOrEqual(majorityResult!.agreementRate);
        });
    });

    describe('Competitive Evolution', () => {
        it('should evolve strategies through competition', async () => {
            const competitionResult = await multiStrategyEvolution.runCompetition(
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator,
                {
                    rounds: 5,
                    participantsPerRound: 8,
                    eliminationRate: 0.5
                }
            );

            expect(competitionResult.winner).toBeDefined();
            expect(competitionResult.finalRanking).toHaveLength(8);
            expect(competitionResult.roundResults).toHaveLength(5);

            // Víťaz by mal mať najlepšie skóre
            const winnerScore = competitionResult.winner.fitness.overallScore;
            const secondPlaceScore = competitionResult.finalRanking[1].fitness.overallScore;
            
            expect(winnerScore).toBeGreaterThanOrEqual(secondPlaceScore);

            console.log(`🏆 Competition Results:`);
            console.log(`  Winner Score: ${winnerScore.toFixed(2)}`);
            console.log(`  Second Place: ${secondPlaceScore.toFixed(2)}`);
            console.log(`  Total Participants: ${competitionResult.finalRanking.length}`);
        });

        it('should maintain strategy diversity through competition', async () => {
            const diversityResult = await multiStrategyEvolution.competitiveEvolutionWithDiversity(
                testScenarios.volatileMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator,
                {
                    populationSize: 20,
                    generations: 6,
                    diversityWeight: 0.3,
                    fitnessWeight: 0.7
                }
            );

            expect(diversityResult.finalPopulation).toHaveLength(20);
            expect(diversityResult.diversityHistory).toHaveLength(6);
            expect(diversityResult.fitnessHistory).toHaveLength(6);

            // Diverzita by sa mala udržiavať na rozumnej úrovni
            const finalDiversity = diversityResult.diversityHistory[diversityResult.diversityHistory.length - 1];
            expect(finalDiversity).toBeGreaterThan(0.1);

            console.log(`🌈 Diversity Evolution:`);
            console.log(`  Final Diversity: ${finalDiversity.toFixed(4)}`);
            console.log(`  Final Best Fitness: ${diversityResult.bestFitness.overallScore.toFixed(2)}`);
        });
    });

    describe('Cooperative Evolution', () => {
        it('should evolve strategies that work well together', async () => {
            const cooperativeResult = await multiStrategyEvolution.cooperativeEvolution(
                testScenarios.trendingMarket.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator,
                {
                    teamSize: 3,
                    populationSize: 15,
                    generations: 5,
                    cooperationWeight: 0.4
                }
            );

            expect(cooperativeResult.bestTeam).toHaveLength(3);
            expect(cooperativeResult.teamPerformance.overallScore).toBeGreaterThan(0);
            expect(cooperativeResult.cooperationScore).toBeGreaterThan(0);

            // Team performance by mal byť lepší ako priemerný jednotlivec
            const avgIndividualScore = cooperativeResult.individualScores
                .reduce((sum, score) => sum + score, 0) / cooperativeResult.individualScores.length;
            
            expect(cooperativeResult.teamPerformance.overallScore).toBeGreaterThanOrEqual(avgIndividualScore * 0.9);

            console.log(`🤝 Cooperative Evolution:`);
            console.log(`  Team Score: ${cooperativeResult.teamPerformance.overallScore.toFixed(2)}`);
            console.log(`  Cooperation Score: ${cooperativeResult.cooperationScore.toFixed(2)}`);
            console.log(`  Average Individual: ${avgIndividualScore.toFixed(2)}`);
        });
    });

    describe('Meta-Evolution', () => {
        it('should evolve evolution parameters themselves', async () => {
            const metaEvolutionResult = await multiStrategyEvolution.metaEvolution(
                testScenarios.optimalConditions.slice(0, testDataSize),
                initialBalance,
                fitnessEvaluator,
                {
                    metaGenerations: 3,
                    strategiesPerMeta: 5,
                    parameterRanges: {
                        populationSize: [10, 20, 30],
                        mutationRate: [0.05, 0.1, 0.2],
                        crossoverRate: [0.7, 0.8, 0.9]
                    }
                }
            );

            expect(metaEvolutionResult.bestEvolutionParams).toBeDefined();
            expect(metaEvolutionResult.bestStrategy).toBeDefined();
            expect(metaEvolutionResult.metaFitness.overallScore).toBeGreaterThan(0);

            console.log(`🧬 Meta-Evolution Results:`);
            console.log(`  Best Population Size: ${metaEvolutionResult.bestEvolutionParams.populationSize}`);
            console.log(`  Best Mutation Rate: ${metaEvolutionResult.bestEvolutionParams.mutationRate}`);
            console.log(`  Best Crossover Rate: ${metaEvolutionResult.bestEvolutionParams.crossoverRate}`);
            console.log(`  Meta Fitness: ${metaEvolutionResult.metaFitness.overallScore.toFixed(2)}`);
        });
    });

    // Pomocné metódy
    private async createSpecializedPopulation(strategyType: string, size: number): Promise<StrategyGenome[]> {
        const population = [];
        
        for (let i = 0; i < size; i++) {
            let params;
            
            switch (strategyType) {
                case 'conservative':
                    params = {
                        minConfidence: 85 + Math.random() * 10,
                        minSignalStrength: 80 + Math.random() * 10,
                        maxRiskLevel: 'low' as const,
                        maxDailyTrades: 2 + Math.floor(Math.random() * 3),
                        maxConsecutiveLosses: 2,
                        profitTarget: 1.5 + Math.random() * 0.5,
                        stopLoss: 0.5 + Math.random() * 0.3
                    };
                    break;
                    
                case 'aggressive':
                    params = {
                        minConfidence: 60 + Math.random() * 15,
                        minSignalStrength: 55 + Math.random() * 20,
                        maxRiskLevel: 'high' as const,
                        maxDailyTrades: 8 + Math.floor(Math.random() * 7),
                        maxConsecutiveLosses: 3 + Math.floor(Math.random() * 2),
                        profitTarget: 2.0 + Math.random() * 1.0,
                        stopLoss: 1.0 + Math.random() * 0.5
                    };
                    break;
                    
                default: // balanced
                    params = {
                        minConfidence: 70 + Math.random() * 15,
                        minSignalStrength: 65 + Math.random() * 15,
                        maxRiskLevel: 'medium' as const,
                        maxDailyTrades: 4 + Math.floor(Math.random() * 4),
                        maxConsecutiveLosses: 2 + Math.floor(Math.random() * 2),
                        profitTarget: 1.8 + Math.random() * 0.7,
                        stopLoss: 0.8 + Math.random() * 0.4
                    };
            }
            
            population.push(new StrategyGenome(params));
        }
        
        return population;
    }
});
