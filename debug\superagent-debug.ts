// SuperAgent Debug & Fix Utility
// Komplexný debug nástroj pre identifikáciu a opravu problémov SuperAgent systému

import * as fs from 'fs';
import * as path from 'path';
import { log } from '../src/utils/logger';

interface DebugReport {
    timestamp: string;
    issues: DebugIssue[];
    fixes: DebugFix[];
    recommendations: string[];
    systemHealth: SystemHealth;
}

interface DebugIssue {
    type: 'CRITICAL' | 'WARNING' | 'INFO';
    category: 'COMPILATION' | 'RUNTIME' | 'CONFIGURATION' | 'DEPENDENCY' | 'MCP';
    description: string;
    file?: string;
    line?: number;
    solution?: string;
}

interface DebugFix {
    issue: string;
    action: string;
    status: 'APPLIED' | 'PENDING' | 'FAILED';
    details: string;
}

interface SystemHealth {
    compilation: 'HEALTHY' | 'ISSUES' | 'CRITICAL';
    dependencies: 'HEALTHY' | 'ISSUES' | 'CRITICAL';
    configuration: 'HEALTHY' | 'ISSUES' | 'CRITICAL';
    mcpIntegration: 'HEALTHY' | 'ISSUES' | 'CRITICAL';
    overallScore: number; // 0-100
}

class SuperAgentDebugger {
    private debugReport: DebugReport;
    private projectRoot: string;

    constructor() {
        this.projectRoot = process.cwd();
        this.debugReport = {
            timestamp: new Date().toISOString(),
            issues: [],
            fixes: [],
            recommendations: [],
            systemHealth: {
                compilation: 'HEALTHY',
                dependencies: 'HEALTHY',
                configuration: 'HEALTHY',
                mcpIntegration: 'HEALTHY',
                overallScore: 100
            }
        };
    }

    /**
     * Hlavná debug funkcia - analyzuje všetky komponenty
     */
    async runFullDiagnostics(): Promise<DebugReport> {
        log('🔍 === SPÚŠŤAM KOMPLETNÚ DIAGNOSTIKU SUPERAGENT ===');
        
        await this.checkCompilationIssues();
        await this.checkDependencies();
        await this.checkConfiguration();
        await this.checkMCPIntegration();
        await this.checkLogFiles();
        await this.generateRecommendations();
        
        this.calculateSystemHealth();
        await this.saveDebugReport();
        
        return this.debugReport;
    }

    /**
     * Kontrola TypeScript kompilačných chýb
     */
    private async checkCompilationIssues(): Promise<void> {
        log('🔧 Kontrolujem TypeScript kompilačné chyby...');
        
        // Kontrola SuperAgent.ts súboru
        const superAgentPath = path.join(this.projectRoot, 'src/agent/superAgent.ts');
        if (fs.existsSync(superAgentPath)) {
            const content = fs.readFileSync(superAgentPath, 'utf8');
            
            // Identifikácia známych problémov z logov
            const issues = this.identifyCompilationIssues(content);
            this.debugReport.issues.push(...issues);
            
            // Automatické opravy
            if (issues.length > 0) {
                await this.fixSuperAgentStructure();
            }
        } else {
            this.addIssue('CRITICAL', 'COMPILATION', 'SuperAgent súbor neexistuje', superAgentPath);
        }
    }

    /**
     * Identifikácia kompilačných problémov
     */
    private identifyCompilationIssues(content: string): DebugIssue[] {
        const issues: DebugIssue[] = [];
        
        // Kontrola class deklarácie
        if (!content.includes('class SuperAgent')) {
            issues.push({
                type: 'CRITICAL',
                category: 'COMPILATION',
                description: 'Chýba class SuperAgent deklarácia',
                solution: 'Pridať správnu class deklaráciu'
            });
        }
        
        // Kontrola importov
        if (!content.includes('import { log }')) {
            issues.push({
                type: 'WARNING',
                category: 'COMPILATION',
                description: 'Chýba import pre logger',
                solution: 'Pridať: import { log } from "../utils/logger";'
            });
        }
        
        // Kontrola MCP importov
        if (!content.includes('MCPTradingClient')) {
            issues.push({
                type: 'WARNING',
                category: 'COMPILATION',
                description: 'Chýba MCP client import',
                solution: 'Pridať: import { MCPTradingClient } from "../utils/mcp-client";'
            });
        }
        
        return issues;
    }

    /**
     * Oprava SuperAgent štruktúry
     */
    private async fixSuperAgentStructure(): Promise<void> {
        log('🔨 Opravujem SuperAgent štruktúru...');
        
        const fixedContent = `// superAgent.ts
// Autonómny orchestrátor pre evolúciu, optimalizáciu a rozširovanie trading agenta

import { optimizeStrategy, runBacktest, generateSyntheticData } from '../backtest';
import { AdvancedStrategy } from './advancedStrategy';
import { SimpleStrategy, MovingAverageCrossoverStrategy } from './strategy';
import { log } from '../utils/logger';
import * as fs from 'fs';
import { MCPTradingClient } from '../utils/mcp-client';
import DataFetcher from '../data/fetcher';

class SuperAgent {
    private mcpClient: MCPTradingClient;
    private dataFetcher: DataFetcher;

    constructor() {
        this.mcpClient = new MCPTradingClient();
        this.dataFetcher = new DataFetcher();
        log('SuperAgent inicializovaný s MCP podporou');
    }

    /**
     * Test MCP pripojenia pred spustením evolučného cyklu
     */
    async testMCPConnection(): Promise<boolean> {
        try {
            const isConnected = await this.dataFetcher.testMCPConnection();
            if (isConnected) {
                log('✅ MCP pripojenie úspešné - používam real-time dáta');
            } else {
                log('⚠️ MCP nedostupný - používam fallback dáta');
            }
            return isConnected;
        } catch (error: any) {
            log(\`❌ MCP test zlyhal: \${error.message}\`);
            return false;
        }
    }

    /**
     * Získanie real-time market dát pre optimalizáciu
     */
    async getRealTimeMarketData(symbol: string = 'EURUSD'): Promise<any> {
        try {
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            
            return await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);
        } catch (error: any) {
            log(\`❌ Chyba pri získavaní real-time dát: \${error.message}\`);
            return null;
        }
    }

    async runFullCycle() {
        log('--- SuperAgent: Spúšťam evolučný cyklus ---');
        // Implementácia evolučného cyklu...
        // (zachovaná pôvodná logika)
    }

    /**
     * Rozšírený evolučný cyklus s MCP dátami
     */
    async runFullCycleWithMCP(): Promise<void> {
        log('--- SuperAgent: Spúšťam rozšírený evolučný cyklus s MCP ---');
        
        // Test MCP pripojenia
        const mcpAvailable = await this.testMCPConnection();
        
        // Získanie real-time dát ak je MCP dostupný
        let realTimeData = null;
        if (mcpAvailable) {
            realTimeData = await this.getRealTimeMarketData();
            if (realTimeData) {
                log('📊 Real-time dáta získané, používam pre optimalizáciu');
            }
        }
        
        // Spustenie pôvodného cyklu
        await this.runFullCycle();
        
        // Ak máme real-time dáta, spustíme dodatočnú optimalizáciu
        if (realTimeData && Object.keys(realTimeData).length > 0) {
            log('🔄 Spúšťam dodatočnú optimalizáciu na real-time dátach...');
            
            // Konverzia real-time dát na formát pre backtest
            const convertedData = this.convertRealTimeDataForBacktest(realTimeData);
            
            if (convertedData.length > 0) {
                const realTimeOpt = optimizeStrategy(convertedData);
                log(\`Real-time optimalizácia: Win rate: \${(realTimeOpt.bestWinRate*100).toFixed(2)}%, Params: \${JSON.stringify(realTimeOpt.bestParams)}\`);
            }
        }
        
        // Odpojenie MCP
        await this.mcpClient.disconnect();
        log('🔌 MCP pripojenie ukončené');
    }

    /**
     * Konverzia real-time dát na formát pre backtest
     */
    private convertRealTimeDataForBacktest(realTimeData: any): any[] {
        const converted = [];
        
        for (const [date, data] of Object.entries(realTimeData)) {
            if (data && typeof data === 'object' && 'close' in data) {
                converted.push({
                    timestamp: new Date(date).getTime(),
                    price: (data as any).close,
                    volume: (data as any).volume || 1000,
                    high: (data as any).high || (data as any).close,
                    low: (data as any).low || (data as any).close,
                    open: (data as any).open || (data as any).close
                });
            }
        }
        
        return converted.sort((a, b) => a.timestamp - b.timestamp);
    }
}

// Ak je skript spustený priamo, spusti cyklus
if (require.main === module) {
    const agent = new SuperAgent();
    agent.runFullCycle();
}

export default SuperAgent;
`;

        const superAgentPath = path.join(this.projectRoot, 'src/agent/superAgent.ts');
        fs.writeFileSync(superAgentPath, fixedContent);
        
        this.addFix('SuperAgent štruktúra opravená', 'Prepísaný súbor s opravenou TypeScript syntaxou', 'APPLIED', 'Opravené class deklarácie, importy a type annotations');
    }

    /**
     * Kontrola závislostí
     */
    private async checkDependencies(): Promise<void> {
        log('📦 Kontrolujem závislosti...');
        
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            // Kontrola kritických závislostí
            const requiredDeps = [
                'typescript',
                'ts-node',
                '@types/node',
                'technicalindicators'
            ];
            
            for (const dep of requiredDeps) {
                if (!packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]) {
                    this.addIssue('WARNING', 'DEPENDENCY', `Chýba závislost: ${dep}`);
                }
            }
        }
    }

    /**
     * Kontrola konfigurácie
     */
    private async checkConfiguration(): Promise<void> {
        log('⚙️ Kontrolujem konfiguráciu...');
        
        // Kontrola config.json
        const configPath = path.join(this.projectRoot, 'config/config.json');
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                
                if (!config.initialBalance) {
                    this.addIssue('WARNING', 'CONFIGURATION', 'Chýba initialBalance v config.json');
                }
                
                if (!config.apiUrl) {
                    this.addIssue('WARNING', 'CONFIGURATION', 'Chýba apiUrl v config.json');
                }
            } catch (error) {
                this.addIssue('CRITICAL', 'CONFIGURATION', 'Neplatný JSON v config.json');
            }
        } else {
            this.addIssue('WARNING', 'CONFIGURATION', 'Chýba config/config.json súbor');
        }
    }

    /**
     * Kontrola MCP integrácie
     */
    private async checkMCPIntegration(): Promise<void> {
        log('🔗 Kontrolujem MCP integráciu...');
        
        const mcpClientPath = path.join(this.projectRoot, 'src/utils/mcp-client.ts');
        if (!fs.existsSync(mcpClientPath)) {
            this.addIssue('CRITICAL', 'MCP', 'Chýba MCP client súbor');
        }
        
        // Kontrola MCP logov
        const mcpLogPath = path.join(this.projectRoot, 'mcp-task-runner.log');
        if (fs.existsSync(mcpLogPath)) {
            const logContent = fs.readFileSync(mcpLogPath, 'utf8');
            
            if (logContent.includes('MCP server build failed')) {
                this.addIssue('CRITICAL', 'MCP', 'MCP server build zlyhal');
            }
            
            if (logContent.includes('Integration test failed')) {
                this.addIssue('WARNING', 'MCP', 'MCP integračné testy zlyhali');
            }
        }
    }

    /**
     * Analýza log súborov
     */
    private async checkLogFiles(): Promise<void> {
        log('📋 Analyzujem log súbory...');
        
        // Analýza evolver-errors.log
        const evolverLogPath = path.join(this.projectRoot, 'evolver-errors.log');
        if (fs.existsSync(evolverLogPath)) {
            const logContent = fs.readFileSync(evolverLogPath, 'utf8');
            
            // Počítanie chýb
            const errorCount = (logContent.match(/TSError:/g) || []).length;
            if (errorCount > 0) {
                this.addIssue('CRITICAL', 'COMPILATION', `Nájdených ${errorCount} TypeScript chýb v evolver-errors.log`);
            }
            
            // Kontrola špecifických chýb
            if (logContent.includes("Cannot find module '../utils/logger'")) {
                this.addIssue('CRITICAL', 'COMPILATION', 'Chýba logger modul');
            }
            
            if (logContent.includes('error TS18046')) {
                this.addIssue('WARNING', 'COMPILATION', 'TypeScript strict mode chyby');
            }
        }
    }

    /**
     * Generovanie odporúčaní
     */
    private async generateRecommendations(): Promise<void> {
        const recommendations: string[] = []; // Explicitne typované ako string[]
        
        // Odporúčania na základe nájdených problémov
        const criticalIssues = this.debugReport.issues.filter(i => i.type === 'CRITICAL');
        const compilationIssues = this.debugReport.issues.filter(i => i.category === 'COMPILATION');
        const mcpIssues = this.debugReport.issues.filter(i => i.category === 'MCP');
        
        if (criticalIssues.length > 0) {
            recommendations.push('🚨 PRIORITA 1: Opraviť kritické chyby pred pokračovaním');
        }
        
        if (compilationIssues.length > 0) {
            recommendations.push('🔧 PRIORITA 2: Opraviť TypeScript kompilačné chyby');
            recommendations.push('📝 Spustiť: npx tsc --noEmit pre kontrolu typov');
        }
        
        if (mcpIssues.length > 0) {
            recommendations.push('🔗 PRIORITA 3: Opraviť MCP integráciu');
            recommendations.push('🧪 Spustiť MCP testy: npm run test:mcp');
        }
        
        // Všeobecné odporúčania
        recommendations.push('📦 Aktualizovať závislosti: npm update');
        recommendations.push('🧹 Vyčistiť build cache: npm run clean');
        recommendations.push('🔄 Reštartovať development server');
        
        this.debugReport.recommendations = recommendations;
    }

    /**
     * Výpočet celkového zdravia systému
     */
    private calculateSystemHealth(): void {
        const issues = this.debugReport.issues;
        const criticalCount = issues.filter(i => i.type === 'CRITICAL').length;
        const warningCount = issues.filter(i => i.type === 'WARNING').length;
        
        // Výpočet skóre (0-100)
        let score = 100;
        score -= criticalCount * 25; // Kritické chyby -25 bodov
        score -= warningCount * 10;  // Varovania -10 bodov
        score = Math.max(0, score);
        
        // Nastavenie stavov komponentov
        const compilationIssues = issues.filter(i => i.category === 'COMPILATION');
        const mcpIssues = issues.filter(i => i.category === 'MCP');
        const configIssues = issues.filter(i => i.category === 'CONFIGURATION');
        const depIssues = issues.filter(i => i.category === 'DEPENDENCY');
        
        this.debugReport.systemHealth = {
            compilation: this.getHealthStatus(compilationIssues),
            dependencies: this.getHealthStatus(depIssues),
            configuration: this.getHealthStatus(configIssues),
            mcpIntegration: this.getHealthStatus(mcpIssues),
            overallScore: score
        };
    }

    private getHealthStatus(issues: DebugIssue[]): 'HEALTHY' | 'ISSUES' | 'CRITICAL' {
        const criticalCount = issues.filter(i => i.type === 'CRITICAL').length;
        const warningCount = issues.filter(i => i.type === 'WARNING').length;
        
        if (criticalCount > 0) return 'CRITICAL';
        if (warningCount > 0) return 'ISSUES';
        return 'HEALTHY';
    }

    /**
     * Uloženie debug reportu
     */
    private async saveDebugReport(): Promise<void> {
        const reportPath = path.join(this.projectRoot, 'debug/superagent-debug-report.json');
        
        // Vytvorenie debug adresára ak neexistuje
        const debugDir = path.dirname(reportPath);
        if (!fs.existsSync(debugDir)) {
            fs.mkdirSync(debugDir, { recursive: true });
        }
        
        fs.writeFileSync(reportPath, JSON.stringify(this.debugReport, null, 2));
        log(`📊 Debug report uložený: ${reportPath}`);
    }

    /**
     * Pomocné metódy
     */
    private addIssue(type: 'CRITICAL' | 'WARNING' | 'INFO', category: 'COMPILATION' | 'RUNTIME' | 'CONFIGURATION' | 'DEPENDENCY' | 'MCP', description: string, file?: string, line?: number): void {
        this.debugReport.issues.push({
            type,
            category,
            description,
            file,
            line
        });
    }

    private addFix(issue: string, action: string, status: 'APPLIED' | 'PENDING' | 'FAILED', details: string): void {
        this.debugReport.fixes.push({
            issue,
            action,
            status,
            details
        });
    }

    /**
     * Zobrazenie debug reportu v konzole
     */
    displayReport(): void {
        console.log('\n🔍 === SUPERAGENT DEBUG REPORT ===');
        console.log(`⏰ Čas: ${this.debugReport.timestamp}`);
        console.log(`📊 Celkové skóre: ${this.debugReport.systemHealth.overallScore}/100`);
        
        console.log('\n🏥 ZDRAVIE SYSTÉMU:');
        console.log(`  Kompilácia: ${this.getStatusEmoji(this.debugReport.systemHealth.compilation)} ${this.debugReport.systemHealth.compilation}`);
        console.log(`  Závislosti: ${this.getStatusEmoji(this.debugReport.systemHealth.dependencies)} ${this.debugReport.systemHealth.dependencies}`);
        console.log(`  Konfigurácia: ${this.getStatusEmoji(this.debugReport.systemHealth.configuration)} ${this.debugReport.systemHealth.configuration}`);
        console.log(`  MCP Integrácia: ${this.getStatusEmoji(this.debugReport.systemHealth.mcpIntegration)} ${this.debugReport.systemHealth.mcpIntegration}`);
        
        if (this.debugReport.issues.length > 0) {
            console.log('\n⚠️ NÁJDENÉ PROBLÉMY:');
            this.debugReport.issues.forEach((issue, index) => {
                console.log(`  ${index + 1}. [${issue.type}] ${issue.category}: ${issue.description}`);
                if (issue.file) console.log(`     📁 Súbor: ${issue.file}${issue.line ? `:${issue.line}` : ''}`);
            });
        }
        
        if (this.debugReport.fixes.length > 0) {
            console.log('\n🔧 APLIKOVANÉ OPRAVY:');
            this.debugReport.fixes.forEach((fix, index) => {
                console.log(`  ${index + 1}. [${fix.status}] ${fix.action}`);
                console.log(`     📝 ${fix.details}`);
            });
        }
        
        if (this.debugReport.recommendations.length > 0) {
            console.log('\n💡 ODPORÚČANIA:');
            this.debugReport.recommendations.forEach((rec, index) => {
                console.log(`  ${index + 1}. ${rec}`);
            });
        }
    }

    private getStatusEmoji(status: string): string {
        switch (status) {
            case 'HEALTHY': return '✅';
            case 'ISSUES': return '⚠️';
            case 'CRITICAL': return '❌';
            default: return '❓';
        }
    }
}

// Export pre použitie v iných súboroch
export { SuperAgentDebugger, DebugReport, DebugIssue, DebugFix, SystemHealth };

// Ak je spustený priamo, spusti diagnostiku
if (require.main === module) {
    const agentDebugger = new SuperAgentDebugger(); // Zmenené z debugger na agentDebugger
    agentDebugger.runFullDiagnostics().then(() => { // Zmenené z debugger na agentDebugger, odstránený parameter report
        agentDebugger.displayReport(); // Zmenené z debugger na agentDebugger
        process.exit(0);
    }).catch(error => {
        console.error('❌ Debug zlyhal:', error);
        process.exit(1);
    });
}