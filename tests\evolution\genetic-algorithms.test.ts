// tests/evolution/genetic-algorithms.test.ts
// Špecializované testy pre genetické algoritmy v trading agent evolúcii

import { GeneticOperators } from './framework/GeneticOperators';
import { StrategyGenome } from './framework/StrategyGenome';
import { FitnessEvaluator } from './framework/FitnessEvaluator';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('Genetic Algorithm Operations', () => {
    let geneticOperators: GeneticOperators;
    let fitnessEvaluator: FitnessEvaluator;

    beforeEach(() => {
        geneticOperators = new GeneticOperators();
        fitnessEvaluator = new FitnessEvaluator();
    });

    describe('Selection Mechanisms', () => {
        let testPopulation: any[];

        beforeEach(async () => {
            // Vytvor test populáciu s rôznymi fitness hodnotami
            const genomes = [
                new StrategyGenome({ minConfidence: 90, minSignalStrength: 85 }), // Vysoká fitness
                new StrategyGenome({ minConfidence: 75, minSignalStrength: 70 }), // Stredná fitness
                new StrategyGenome({ minConfidence: 60, minSignalStrength: 55 }), // Nízka fitness
                new StrategyGenome({ minConfidence: 85, minSignalStrength: 80 }), // Vysoká fitness
                new StrategyGenome({ minConfidence: 65, minSignalStrength: 60 })  // Nízka fitness
            ];

            testPopulation = [];
            for (const genome of genomes) {
                const fitness = await fitnessEvaluator.evaluateFitness(
                    genome,
                    testScenarios.optimalConditions.slice(0, 50),
                    10000
                );
                testPopulation.push({ individual: genome, fitness });
            }
        });

        it('should perform tournament selection correctly', () => {
            const selected = geneticOperators.tournamentSelection(testPopulation, 3, 2);
            
            expect(selected).toHaveLength(3);
            expect(selected[0]).toHaveProperty('individual');
            expect(selected[0]).toHaveProperty('fitness');
            
            // Tournament selection by mal preferovať jedincov s vyššou fitness
            const avgSelectedFitness = selected.reduce((sum, s) => sum + s.fitness.overallScore, 0) / selected.length;
            const avgPopulationFitness = testPopulation.reduce((sum, p) => sum + p.fitness.overallScore, 0) / testPopulation.length;
            
            expect(avgSelectedFitness).toBeGreaterThanOrEqual(avgPopulationFitness * 0.8); // Tolerancia 20%
        });

        it('should perform roulette selection correctly', () => {
            const selected = geneticOperators.rouletteSelection(testPopulation, 3);
            
            expect(selected).toHaveLength(3);
            
            // Roulette selection by mal preferovať jedincov s vyššou fitness
            const selectedFitnessValues = selected.map(s => s.fitness.overallScore);
            expect(selectedFitnessValues.some(f => f > 0)).toBe(true);
        });

        it('should perform rank selection correctly', () => {
            const selected = geneticOperators.rankSelection(testPopulation, 3);
            
            expect(selected).toHaveLength(3);
            
            // Rank selection by mal byť menej agresívny ako elite selection
            const selectedIds = selected.map(s => s.individual.getId());
            const uniqueSelected = new Set(selectedIds);
            
            // Môže vybrať duplicitných jedincov, ale by mal preferovať lepších
            expect(uniqueSelected.size).toBeGreaterThanOrEqual(1);
        });

        it('should perform elite selection correctly', () => {
            const selected = geneticOperators.eliteSelection(testPopulation, 2);
            
            expect(selected).toHaveLength(2);
            
            // Elite selection by mal vybrať najlepších jedincov
            const sortedPopulation = [...testPopulation].sort((a, b) => 
                b.fitness.overallScore - a.fitness.overallScore
            );
            
            expect(selected[0].fitness.overallScore).toBe(sortedPopulation[0].fitness.overallScore);
            expect(selected[1].fitness.overallScore).toBe(sortedPopulation[1].fitness.overallScore);
        });
    });

    describe('Crossover Operations', () => {
        let parent1: StrategyGenome;
        let parent2: StrategyGenome;

        beforeEach(() => {
            parent1 = new StrategyGenome({
                minConfidence: 80,
                minSignalStrength: 75,
                maxRiskLevel: 'medium',
                trendConfirmationPeriod: 5,
                volatilityThreshold: 0.02,
                volumeConfirmation: true,
                maxDailyTrades: 5
            });

            parent2 = new StrategyGenome({
                minConfidence: 90,
                minSignalStrength: 85,
                maxRiskLevel: 'low',
                trendConfirmationPeriod: 3,
                volatilityThreshold: 0.015,
                volumeConfirmation: false,
                maxDailyTrades: 8
            });
        });

        it('should perform uniform crossover correctly', () => {
            const [child1, child2] = geneticOperators.uniformCrossover(parent1, parent2);
            
            expect(child1).toBeInstanceOf(StrategyGenome);
            expect(child2).toBeInstanceOf(StrategyGenome);
            
            const child1Params = child1.getParameters();
            const child2Params = child2.getParameters();
            const parent1Params = parent1.getParameters();
            const parent2Params = parent2.getParameters();
            
            // Deti by mali mať kombináciu parametrov od oboch rodičov
            let child1FromParent1 = 0;
            let child1FromParent2 = 0;
            
            Object.keys(parent1Params).forEach(key => {
                if ((child1Params as any)[key] === (parent1Params as any)[key]) {
                    child1FromParent1++;
                } else if ((child1Params as any)[key] === (parent2Params as any)[key]) {
                    child1FromParent2++;
                }
            });
            
            // Dieťa by malo mať parametre od oboch rodičov
            expect(child1FromParent1 + child1FromParent2).toBeGreaterThan(0);
        });

        it('should perform single-point crossover correctly', () => {
            const [child1, child2] = geneticOperators.singlePointCrossover(parent1, parent2);
            
            expect(child1).toBeInstanceOf(StrategyGenome);
            expect(child2).toBeInstanceOf(StrategyGenome);
            
            // Deti by mali byť odlišné od rodičov
            expect(child1.getId()).not.toBe(parent1.getId());
            expect(child2.getId()).not.toBe(parent2.getId());
        });

        it('should perform arithmetic crossover correctly', () => {
            const [child1, child2] = geneticOperators.arithmeticCrossover(parent1, parent2);
            
            expect(child1).toBeInstanceOf(StrategyGenome);
            expect(child2).toBeInstanceOf(StrategyGenome);
            
            const child1Params = child1.getParameters();
            const parent1Params = parent1.getParameters();
            const parent2Params = parent2.getParameters();
            
            // Pre numerické parametre by mali byť hodnoty medzi rodičmi
            expect(child1Params.minConfidence).toBeGreaterThanOrEqual(
                Math.min(parent1Params.minConfidence, parent2Params.minConfidence)
            );
            expect(child1Params.minConfidence).toBeLessThanOrEqual(
                Math.max(parent1Params.minConfidence, parent2Params.minConfidence)
            );
        });

        it('should maintain parameter constraints after crossover', () => {
            const [child1, child2] = geneticOperators.crossover(parent1, parent2);
            
            const child1Params = child1.getParameters();
            const child2Params = child2.getParameters();
            
            // Skontroluj, že parametre sú v platných rozsahoch
            expect(child1Params.minConfidence).toBeGreaterThanOrEqual(60);
            expect(child1Params.minConfidence).toBeLessThanOrEqual(95);
            expect(child1Params.minSignalStrength).toBeGreaterThanOrEqual(50);
            expect(child1Params.minSignalStrength).toBeLessThanOrEqual(90);
            
            expect(child2Params.minConfidence).toBeGreaterThanOrEqual(60);
            expect(child2Params.minConfidence).toBeLessThanOrEqual(95);
            expect(child2Params.minSignalStrength).toBeGreaterThanOrEqual(50);
            expect(child2Params.minSignalStrength).toBeLessThanOrEqual(90);
        });
    });

    describe('Mutation Operations', () => {
        let originalGenome: StrategyGenome;

        beforeEach(() => {
            originalGenome = new StrategyGenome({
                minConfidence: 75,
                minSignalStrength: 70,
                maxRiskLevel: 'medium',
                trendConfirmationPeriod: 5,
                volatilityThreshold: 0.02,
                volumeConfirmation: true,
                maxDailyTrades: 5
            });
        });

        it('should perform gaussian mutation correctly', () => {
            const mutated = geneticOperators.gaussianMutation(originalGenome, 0.5);
            
            expect(mutated).toBeInstanceOf(StrategyGenome);
            expect(mutated.getId()).not.toBe(originalGenome.getId());
            
            const originalParams = originalGenome.getParameters();
            const mutatedParams = mutated.getParameters();
            
            // Aspoň niektoré parametre by mali byť odlišné
            let differences = 0;
            Object.keys(originalParams).forEach(key => {
                if ((originalParams as any)[key] !== (mutatedParams as any)[key]) {
                    differences++;
                }
            });
            
            expect(differences).toBeGreaterThan(0);
        });

        it('should perform uniform mutation correctly', () => {
            const mutated = geneticOperators.uniformMutation(originalGenome, 1.0); // 100% mutation rate
            
            expect(mutated).toBeInstanceOf(StrategyGenome);
            
            const originalParams = originalGenome.getParameters();
            const mutatedParams = mutated.getParameters();
            
            // S vysokou mutation rate by mali byť parametre odlišné
            let differences = 0;
            Object.keys(originalParams).forEach(key => {
                if ((originalParams as any)[key] !== (mutatedParams as any)[key]) {
                    differences++;
                }
            });
            
            expect(differences).toBeGreaterThan(0);
        });

        it('should respect mutation rate', () => {
            const lowMutationRate = 0.1;
            const highMutationRate = 0.9;
            
            const lowMutated = geneticOperators.mutate(originalGenome, lowMutationRate);
            const highMutated = geneticOperators.mutate(originalGenome, highMutationRate);
            
            const originalParams = originalGenome.getParameters();
            
            // Počítaj rozdiely
            const lowDifferences = this.countDifferences(originalParams, lowMutated.getParameters());
            const highDifferences = this.countDifferences(originalParams, highMutated.getParameters());
            
            // Vyššia mutation rate by mala viesť k viacerým rozdielom
            expect(highDifferences).toBeGreaterThanOrEqual(lowDifferences);
        });

        it('should maintain parameter validity after mutation', () => {
            const mutated = geneticOperators.mutate(originalGenome, 1.0);
            const mutatedParams = mutated.getParameters();
            
            // Skontroluj platnosť parametrov
            expect(mutatedParams.minConfidence).toBeGreaterThanOrEqual(60);
            expect(mutatedParams.minConfidence).toBeLessThanOrEqual(95);
            expect(mutatedParams.minSignalStrength).toBeGreaterThanOrEqual(50);
            expect(mutatedParams.minSignalStrength).toBeLessThanOrEqual(90);
            expect(mutatedParams.trendConfirmationPeriod).toBeGreaterThanOrEqual(3);
            expect(mutatedParams.trendConfirmationPeriod).toBeLessThanOrEqual(10);
            expect(mutatedParams.volatilityThreshold).toBeGreaterThanOrEqual(0.01);
            expect(mutatedParams.volatilityThreshold).toBeLessThanOrEqual(0.05);
            expect(['low', 'medium', 'high']).toContain(mutatedParams.maxRiskLevel);
        });

        private countDifferences(params1: any, params2: any): number {
            let differences = 0;
            Object.keys(params1).forEach(key => {
                if (params1[key] !== params2[key]) {
                    differences++;
                }
            });
            return differences;
        }
    });

    describe('Population Diversity', () => {
        it('should calculate population diversity correctly', () => {
            const population = [
                new StrategyGenome({ minConfidence: 70, minSignalStrength: 65 }),
                new StrategyGenome({ minConfidence: 80, minSignalStrength: 75 }),
                new StrategyGenome({ minConfidence: 90, minSignalStrength: 85 }),
                new StrategyGenome({ minConfidence: 75, minSignalStrength: 70 })
            ];
            
            const diversity = geneticOperators.calculatePopulationDiversity(population);
            
            expect(diversity).toBeGreaterThan(0);
            expect(diversity).toBeLessThanOrEqual(1);
        });

        it('should detect low diversity in similar population', () => {
            const population = [
                new StrategyGenome({ minConfidence: 75, minSignalStrength: 70 }),
                new StrategyGenome({ minConfidence: 76, minSignalStrength: 71 }),
                new StrategyGenome({ minConfidence: 74, minSignalStrength: 69 }),
                new StrategyGenome({ minConfidence: 75, minSignalStrength: 70 })
            ];
            
            const diversity = geneticOperators.calculatePopulationDiversity(population);
            
            expect(diversity).toBeLessThan(0.1); // Nízka diverzita
        });

        it('should detect high diversity in varied population', () => {
            const population = [
                new StrategyGenome({ minConfidence: 60, minSignalStrength: 50, maxRiskLevel: 'high' }),
                new StrategyGenome({ minConfidence: 95, minSignalStrength: 90, maxRiskLevel: 'low' }),
                new StrategyGenome({ minConfidence: 75, minSignalStrength: 70, maxRiskLevel: 'medium' }),
                new StrategyGenome({ minConfidence: 85, minSignalStrength: 60, maxRiskLevel: 'high' })
            ];
            
            const diversity = geneticOperators.calculatePopulationDiversity(population);
            
            expect(diversity).toBeGreaterThan(0.2); // Vysoká diverzita
        });
    });

    describe('Genetic Algorithm Configuration', () => {
        it('should allow configuration of selection method', () => {
            geneticOperators.setSelectionConfig({
                method: 'tournament',
                tournamentSize: 5
            });
            
            // Test že konfigurácia funguje
            const testPopulation = [
                { individual: new StrategyGenome(), fitness: { overallScore: 10 } as any },
                { individual: new StrategyGenome(), fitness: { overallScore: 20 } as any },
                { individual: new StrategyGenome(), fitness: { overallScore: 30 } as any }
            ];
            
            const selected = geneticOperators.tournamentSelection(testPopulation, 1, 5);
            expect(selected).toHaveLength(1);
        });

        it('should allow configuration of crossover method', () => {
            geneticOperators.setCrossoverConfig({
                method: 'arithmetic',
                crossoverRate: 0.9
            });
            
            const parent1 = new StrategyGenome({ minConfidence: 70 });
            const parent2 = new StrategyGenome({ minConfidence: 90 });
            
            const [child1, child2] = geneticOperators.crossover(parent1, parent2);
            
            expect(child1).toBeInstanceOf(StrategyGenome);
            expect(child2).toBeInstanceOf(StrategyGenome);
        });

        it('should allow configuration of mutation method', () => {
            geneticOperators.setMutationConfig({
                method: 'uniform',
                mutationRate: 0.2,
                mutationStrength: 0.1
            });
            
            const original = new StrategyGenome();
            const mutated = geneticOperators.mutate(original, 0.2);
            
            expect(mutated).toBeInstanceOf(StrategyGenome);
            expect(mutated.getId()).not.toBe(original.getId());
        });
    });
});
