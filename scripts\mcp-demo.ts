#!/usr/bin/env ts-node

import { MCPTradingClient } from '../src/utils/mcp-client';
import DataFetcher from '../src/data/fetcher';

/**
 * MCP Demo Script - automatické testovanie MCP integrácie
 * Spúšťa sa bez čakania na potvrdenia
 */
class MCPDemo {
  private mcpClient: MCPTradingClient;
  private dataFetcher: DataFetcher;

  constructor() {
    this.mcpClient = new MCPTradingClient();
    this.dataFetcher = new DataFetcher();
    console.log('🚀 MCP Demo Script spustený');
  }

  /**
   * Hlavná demo funkcia
   */
  async runDemo(): Promise<void> {
    console.log('\n=== MCP TRADING INTEGRATION DEMO ===\n');

    // Test 1: MCP Client základné funkcie
    await this.testMCPClient();

    // Test 2: DataFetcher s MCP integráciou
    await this.testDataFetcher();

    // Test 3: <PERSON>mul<PERSON>cia trading scenára
    await this.testTradingScenario();

    console.log('\n=== DEMO DOKONČENÉ ===\n');
  }

  /**
   * Test MCP Client funkcionalít
   */
  private async testMCPClient(): Promise<void> {
    console.log('📡 Test 1: MCP Client funkcionalita');
    console.log('=====================================');

    try {
      // Test connection stats
      const stats = this.mcpClient.getConnectionStats();
      console.log(`📊 Connection stats:`, stats);

      // Test connection (očakáva sa zlyhanie bez spusteného servera)
      console.log('🔌 Testovanie pripojenia k MCP serveru...');
      const isConnected = await this.mcpClient.testConnection();
      
      if (isConnected) {
        console.log('✅ MCP server je dostupný!');
        
        // Test forex data
        console.log('💱 Testovanie forex dát...');
        const forexData = await this.mcpClient.getForexData('EURUSD', '1h', '1d');
        console.log('✅ Forex dáta získané:', forexData?.symbol || 'N/A');

        // Test indicators
        console.log('📊 Testovanie indikátorov...');
        const indicators = await this.mcpClient.getMarketIndicators('EURUSD', ['rsi', 'macd']);
        console.log('✅ Indikátory vypočítané:', Object.keys(indicators?.indicators || {}));

      } else {
        console.log('⚠️ MCP server nie je dostupný - používam fallback režim');
      }

    } catch (error) {
      console.log('❌ MCP Client test zlyhal:', (error as Error).message);
    }

    console.log('');
  }

  /**
   * Test DataFetcher s MCP integráciou
   */
  private async testDataFetcher(): Promise<void> {
    console.log('📊 Test 2: DataFetcher s MCP integráciou');
    console.log('=========================================');

    try {
      // Test MCP status
      const mcpStatus = this.dataFetcher.getMCPStatus();
      console.log('📈 MCP Status:', mcpStatus);

      // Test market data fetch
      console.log('💹 Získavam trhové dáta pre EURUSD...');
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const endDate = new Date().toISOString().split('T')[0];
      
      const marketData = await this.dataFetcher.fetchMarketData('EURUSD', startDate, endDate);
      console.log('✅ Trhové dáta získané:', Object.keys(marketData || {}).length, 'záznamov');

      // Test indicators
      console.log('📊 Získavam technické indikátory...');
      const indicators = await this.dataFetcher.fetchMarketIndicators('EURUSD', ['RSI', 'MACD', 'SMA']);
      console.log('✅ Indikátory získané:', indicators?.symbol || 'N/A');

    } catch (error) {
      console.log('❌ DataFetcher test zlyhal:', (error as Error).message);
    }

    console.log('');
  }

  /**
   * Test trading scenára
   */
  private async testTradingScenario(): Promise<void> {
    console.log('🎯 Test 3: Trading scenár simulácia');
    console.log('====================================');

    try {
      const symbols = ['EURUSD', 'GBPUSD', 'USDJPY'];
      
      for (const symbol of symbols) {
        console.log(`\n💱 Analyzujem ${symbol}...`);
        
        // Získanie dát
        const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        const endDate = new Date().toISOString().split('T')[0];
        
        try {
          const data = await this.dataFetcher.fetchMarketData(symbol, startDate, endDate);
          const dataCount = Object.keys(data || {}).length;
          console.log(`  📈 Dáta: ${dataCount} záznamov`);

          // Získanie indikátorov
          const indicators = await this.dataFetcher.fetchMarketIndicators(symbol, ['RSI', 'MACD']);
          const rsi = indicators?.indicators?.RSI || 'N/A';
          const macd = indicators?.indicators?.MACD || 'N/A';
          
          console.log(`  📊 RSI: ${typeof rsi === 'number' ? rsi.toFixed(2) : rsi}`);
          console.log(`  📊 MACD: ${typeof macd === 'object' ? 'Vypočítané' : macd}`);

          // Simulácia trading signálu
          if (typeof rsi === 'number') {
            if (rsi < 30) {
              console.log(`  🟢 SIGNAL: BUY (RSI oversold: ${rsi.toFixed(2)})`);
            } else if (rsi > 70) {
              console.log(`  🔴 SIGNAL: SELL (RSI overbought: ${rsi.toFixed(2)})`);
            } else {
              console.log(`  ⚪ SIGNAL: HOLD (RSI neutral: ${rsi.toFixed(2)})`);
            }
          }

        } catch (error) {
          console.log(`  ❌ Chyba pre ${symbol}: ${(error as Error).message}`);
        }
      }

    } catch (error) {
      console.log('❌ Trading scenár zlyhal:', (error as Error).message);
    }

    console.log('');
  }

  /**
   * Cleanup
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Čistenie zdrojov...');
    await this.mcpClient.disconnect();
    console.log('✅ Cleanup dokončený');
  }
}

/**
 * Spustenie demo
 */
async function main() {
  const demo = new MCPDemo();
  
  try {
    await demo.runDemo();
  } catch (error) {
    console.error('💥 Demo zlyhal:', error);
  } finally {
    await demo.cleanup();
  }
}

// Spustenie ak je súbor spustený priamo
if (require.main === module) {
  main().catch(console.error);
}

export { MCPDemo };