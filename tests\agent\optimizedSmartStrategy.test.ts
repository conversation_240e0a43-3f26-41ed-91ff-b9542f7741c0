import { OptimizedSmartStrategy } from '../../src/agent/optimizedSmartStrategy';
import { testScenarios } from '../../src/data/enhancedMockData';

describe('OptimizedSmartStrategy', () => {
    let strategy: OptimizedSmartStrategy;

    beforeEach(() => {
        strategy = new OptimizedSmartStrategy();
    });

    test('should require sufficient data before making decisions', () => {
        const marketData = { price: 1.2000, high: 1.2010, low: 1.1990, volume: 1000 };
        
        // Should return null with insufficient data
        for (let i = 0; i < 50; i++) {
            const decision = strategy.decideAction(marketData);
            expect(decision).toBeNull();
        }
    });

    test('should apply strict filtering criteria', () => {
        // Feed enough data
        for (let i = 0; i < 100; i++) {
            const marketData = {
                price: 1.2000 + (i * 0.0001),
                high: 1.2000 + (i * 0.0001) + 0.0005,
                low: 1.2000 + (i * 0.0001) - 0.0005,
                volume: 1000 + (i * 10)
            };
            strategy.decideAction(marketData);
        }

        const stats = strategy.getOptimizedWinRateStats();
        expect(stats.totalSignals).toBeGreaterThan(0);
    });

    test('should handle optimal conditions scenario', () => {
        const optimalData = testScenarios.optimalConditions;
        let totalDecisions = 0;
        let qualityDecisions = 0;

        optimalData.forEach(dataPoint => {
            const decision = strategy.decideAction(dataPoint);
            if (decision) {
                totalDecisions++;
                
                // Check if decision was made with high quality criteria
                const history = strategy.getOptimizedAnalysisHistory();
                const lastAnalysis = history[history.length - 1];
                
                if (lastAnalysis && (lastAnalysis.signals.quality === 'A+' || lastAnalysis.signals.quality === 'A')) {
                    qualityDecisions++;
                }
            }
        });

        // Should make fewer but higher quality decisions
        expect(totalDecisions).toBeGreaterThan(0);
        expect(qualityDecisions / totalDecisions).toBeGreaterThan(0.8); // 80%+ should be high quality
    });

    test('should respect daily trade limits', () => {
        const marketData = {
            price: 1.2000,
            high: 1.2010,
            low: 1.1990,
            volume: 1000
        };

        // Feed enough data first
        for (let i = 0; i < 100; i++) {
            strategy.decideAction({
                ...marketData,
                price: marketData.price + (i * 0.0001)
            });
        }

        // Try to make many trades in one day
        let tradesCount = 0;
        for (let i = 0; i < 20; i++) {
            const decision = strategy.decideAction({
                ...marketData,
                price: marketData.price + (i * 0.001) // Larger price movements
            });
            if (decision) tradesCount++;
        }

        // Should respect daily limit (max 5 trades)
        expect(tradesCount).toBeLessThanOrEqual(5);
    });

    test('should handle consecutive losses correctly', () => {
        // Simulate consecutive losses
        strategy.recordTradeLoss();
        strategy.recordTradeLoss();
        
        // After 2 losses, should be more restrictive
        const marketData = {
            price: 1.2000,
            high: 1.2010,
            low: 1.1990,
            volume: 1000
        };

        // Feed data
        for (let i = 0; i < 100; i++) {
            const decision = strategy.decideAction({
                ...marketData,
                price: marketData.price + (i * 0.0001)
            });
            // Should be null due to consecutive losses limit
            expect(decision).toBeNull();
        }

        // Reset with a win
        strategy.recordTradeWin();
        
        // Now should be able to trade again (if other conditions are met)
        // This is tested implicitly by the strategy logic
    });

    test('should provide detailed analysis history', () => {
        const testData = testScenarios.strongUptrend.slice(0, 50);
        
        testData.forEach(dataPoint => {
            strategy.decideAction(dataPoint);
        });

        const history = strategy.getOptimizedAnalysisHistory();
        const stats = strategy.getOptimizedWinRateStats();

        expect(history.length).toBeGreaterThan(0);
        expect(stats.totalSignals).toBe(history.length);
        expect(stats.avgConfidence).toBeGreaterThanOrEqual(0);
        expect(stats.avgConfirmations).toBeGreaterThanOrEqual(0);
    });

    test('should handle different market scenarios appropriately', () => {
        const scenarios = [
            { name: 'Strong Uptrend', data: testScenarios.strongUptrend.slice(0, 30) },
            { name: 'Strong Downtrend', data: testScenarios.strongDowntrend.slice(0, 30) },
            { name: 'Sideways Market', data: testScenarios.sidewaysMarket.slice(0, 30) },
            { name: 'Volatile Market', data: testScenarios.volatileMarket.slice(0, 30) }
        ];

        const results: any[] = [];

        scenarios.forEach(scenario => {
            const testStrategy = new OptimizedSmartStrategy();
            let decisions = 0;
            let highQualityDecisions = 0;

            scenario.data.forEach(dataPoint => {
                const decision = testStrategy.decideAction(dataPoint);
                if (decision) {
                    decisions++;
                    const history = testStrategy.getOptimizedAnalysisHistory();
                    const lastAnalysis = history[history.length - 1];
                    
                    if (lastAnalysis && (lastAnalysis.signals.quality === 'A+' || lastAnalysis.signals.quality === 'A')) {
                        highQualityDecisions++;
                    }
                }
            });

            results.push({
                scenario: scenario.name,
                decisions,
                qualityRatio: decisions > 0 ? highQualityDecisions / decisions : 0
            });
        });

        // Should make more decisions in trending markets than in volatile/sideways
        const trendingDecisions = results.filter(r => r.scenario.includes('Trend')).reduce((sum, r) => sum + r.decisions, 0);
        const nonTrendingDecisions = results.filter(r => !r.scenario.includes('Trend')).reduce((sum, r) => sum + r.decisions, 0);
        
        expect(trendingDecisions).toBeGreaterThanOrEqual(nonTrendingDecisions);
        
        // All decisions should maintain high quality
        results.forEach(result => {
            if (result.decisions > 0) {
                expect(result.qualityRatio).toBeGreaterThan(0.7); // 70%+ quality
            }
        });
    });
});